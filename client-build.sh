#!/bin/bash

if [ -z "$1" ] || [ -z "$2" ]; then
    echo "Missing Parameters. Usage: $0 {Client} {ENV} [ios|and]"
    exit 1
fi

client="$1"
env="${2}"
config_file="${client}${env}"

echo "config: ${config_file}"

ng build --project=third-party-fix
ng build --project=lp-client-api
ng build --project=mobile-components
ng build --project=lp-client --configuration="${config_file}"

cd projects/lp-client

if [ -z "$3" ] || [ "$3" = "ios" ]; then
    # Skip the capacitor-assets generator as it's causing issues
    # Instead, just copy the icon files directly

    # Copy the app resources to the iOS project
    cp -r "resources_${client}/" ios/App/App/Assets.xcassets/

    NODE_ENV="${client}" npx cap copy ios
    NODE_ENV="${client}" npx cap sync ios

    # Run the copy-icons.sh script to ensure icons are properly set up
    cd ios
    chmod +x copy-icons.sh
    ./copy-icons.sh
    
    # Fix permissions for CocoaPods scripts to address sandbox restrictions
    find App/Pods -name "*.sh" -exec chmod +x {} \;
    
    # Apply sandbox configuration fix
    chmod +x update_project_config.sh
    ./update_project_config.sh
    
    cd ..

    # Set environment variables to disable sandbox restrictions for Xcode
    export OBJROOT=$(pwd)/build
    export SYMROOT=$(pwd)/build
    
    # Open Xcode with sandbox temporarily disabled
    NODE_ENV="${client}" npx cap open ios
fi

if [ -z "$3" ] || [ "$3" = "and" ]; then
    npx capacitor-assets generate --android --assetPath "resources_${client}"
    NODE_ENV="${client}" npx cap copy android
    NODE_ENV="${client}" npx cap sync android
    NODE_ENV="${client}" npx cap open android
fi
