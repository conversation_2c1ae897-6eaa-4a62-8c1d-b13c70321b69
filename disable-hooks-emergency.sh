#!/bin/bash
# Emergency Hook Disable Script
# Use this when you just need to get work done!

echo "🚨 EMERGENCY HOOK DISABLE 🚨"
echo "=========================="
echo ""
echo "This will disable ALL Claude Code hooks in the current project."
echo "You can re-enable them later by running: mv .claude/settings.json.disabled .claude/settings.json"
echo ""

if [ -f ".claude/settings.json" ]; then
    mv .claude/settings.json .claude/settings.json.disabled
    echo "✅ Hooks disabled!"
    echo ""
    echo "Restart Claude Code for changes to take effect."
else
    echo "❌ No .claude/settings.json found in current directory"
    echo "Are you in the right project directory?"
fi