# Angular Development Memory Management Guide

## 🚀 Quick Start Commands

### Development Server (Recommended)
```bash
./serve.sh gdem dev
```

### Full Build
```bash
./dev.sh gdem dev
```

### Clear Cache Only
```bash
./clear-cache.sh
```

## 🔧 Scripts Available

### 1. `serve.sh` - Development Server
- **Purpose**: Start development server with memory optimization
- **Memory**: 12GB limit 
- **Usage**: `./serve.sh {client} {env}`
- **Example**: `./serve.sh gdem dev`

### 2. `dev.sh` - Full Build
- **Purpose**: Build all projects + start watch processes
- **Memory**: 8GB limit
- **Usage**: `./dev.sh {client} {env}`  
- **Example**: `./dev.sh gdem dev`

### 3. `clear-cache.sh` - Cache Management
- **Purpose**: Clear all Angular caches
- **Usage**: `./clear-cache.sh`
- **When**: Use when experiencing memory issues

## 🐛 Troubleshooting

### Memory Issues
1. **Clear cache first**: `./clear-cache.sh`
2. **Use serve script**: `./serve.sh gdem dev` (has higher memory limit)
3. **Check system resources**: Close other applications
4. **Restart terminal**: Fresh environment

### Common Commands
```bash
# Direct Angular CLI with memory optimization
export NODE_OPTIONS="--max-old-space-size=12288"
ng run lp-client:serve:gdemdev --host=localhost --port=8100

# Build specific project
ng build --project=lp-client --configuration=gdemdev

# Watch mode for dependencies
ng build --project=lp-client-api --watch
ng build --project=mobile-components --watch
```

## 📊 Memory Limits Set

- **serve.sh**: 12GB (for development server)
- **dev.sh**: 8GB (for builds)
- **build-with-memory.sh**: 8GB (for builds)

## 🎯 Best Practices

1. **Always clear cache** when switching configurations
2. **Use serve.sh** for development (lighter than full build)
3. **Close unnecessary applications** to free up system memory
4. **Monitor build process** - if it takes too long, restart

## 🔍 Debugging

If issues persist:
1. Check available system memory: `top` or Activity Monitor
2. Check Node.js process memory usage
3. Try building individual projects first
4. Consider using `development` configuration instead of `gdemdev`
