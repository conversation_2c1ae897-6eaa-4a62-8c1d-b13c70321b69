{"project_info": {"project_number": "798238467180", "project_id": "loyalty-test-project-58bcb", "storage_bucket": "loyalty-test-project-58bcb.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:798238467180:android:ddab11e43f82af1de86449", "android_client_info": {"package_name": "za.co.loyaltyplus.Client"}}, "oauth_client": [{"client_id": "798238467180-example.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAseqL6KTURHxDYvK7Wygl0ViRIDS1OM3c"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "798238467180-example.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:798238467180:android:ddab11e43f82af1de86449", "android_client_info": {"package_name": "za.co.loyaltyplus.Client.mica"}}, "oauth_client": [{"client_id": "798238467180-example.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAseqL6KTURHxDYvK7Wygl0ViRIDS1OM3c"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "798238467180-example.apps.googleusercontent.com", "client_type": 3}]}}}], "configuration_version": "1"}