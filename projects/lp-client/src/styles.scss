/* Import Design System */
@import './styles/design-system/index';

/* Base app structure - simplified for mobile compatibility */
html {
  height: 100%;
}

body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden; /* Let Ionic handle scrolling */
}

ion-app {
  height: 100%;
  display: block;
}

/* Ensure ion-content handles scrolling properly */
ion-content {
  --overflow: scroll; /* Changed from auto to scroll for better mobile support */
  
  /* Enable smooth scrolling on iOS */
  &::part(scroll) {
    -webkit-overflow-scrolling: touch;
  }
}

/* Ensure ion-router-outlet displays content properly */
/* Only apply to the main router outlet, not nested ones */
.main-layout > ion-router-outlet {
  display: contents; /* This allows child components to fill the space */
}

/* Nested router outlets should display normally */
ion-router-outlet ion-router-outlet {
  display: block;
  height: 100%;
}

/* Ensure page-wrapper's ion-content works properly */
lib-page-wrapper {
  display: block;
  height: 100%;
  
  ion-content {
    --background: transparent;
    height: 100%;
  }
}

/* Global ion-card styles for blue background theme */
ion-card:not(.dashboard-action-card) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dynamic select popover positioning for stores */
ion-popover.select-popover-upward {
  /* Let Ionic handle positioning dynamically */
  
  .popover-content {
    max-height: 250px !important;
    overflow-y: auto !important;
  }
}

/* Ensure popover stays within viewport */
.select-popover-upward {
  .popover-content {
    /* Remove fixed positioning to allow dynamic placement */
    position: absolute !important;
  }
  
  /* Add scrollbar styling for better UX */
  ion-list {
    max-height: 200px;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 2px;
    }
  }
}

.primary {
  background: var(--ion-color-primary) !important;
  color: var(--ion-color-primary-contrast) !important;
}
.primaryContrast {
  background: var(--ion-color-primary-contrast) !important;
  color: var(--ion-color-primary) !important;
}
.primaryShade {
  background: var(--ion-color-primary-shade) !important;
  color: var(--ion-color-primary-contrast) !important;
}
.primaryTint {
  background: var(--ion-color-primary-tint) !important;
  color: var(--ion-color-primary-contrast) !important;
}
.secondary {
  background: var(--ion-color-secondary) !important;
  color: var(--ion-color-secondary-contrast) !important;
}
.secondaryContrast {
  background: var(--ion-color-secondary-contrast) !important;
  color: var(--ion-color-secondary) !important;
}
.secondaryShade {
  background: var(--ion-color-secondary-shade) !important;
  color: var(--ion-color-secondary-contrast) !important;
}
.secondaryTint {
  background: var(--ion-color-secondary-tint) !important;
  color: var(--ion-color-secondary-contrast) !important;
}
.tertiary {
  background: var(--ion-color-tertiary) !important;
  color: var(--ion-color-tertiary-contrast) !important;
}
.tertiaryContrast {
  background: var(--ion-color-tertiary-contrast) !important;
  color: var(--ion-color-tertiary) !important;
}
.tertiaryShade {
  background: var(--ion-color-tertiary-shade) !important;
  color: var(--ion-color-tertiary-contrast) !important;
}
.tertiaryTint {
  background: var(--ion-color-tertiary-tint) !important;
  color: var(--ion-color-tertiary-contrast) !important;
}
.success {
  background: var(--ion-color-success) !important;
  color: var(--ion-color-success-contrast) !important;
}
.successContrast {
  background: var(--ion-color-success-contrast) !important;
  color: var(--ion-color-success) !important;
}
.successShade {
  background: var(--ion-color-success-shade) !important;
  color: var(--ion-color-success-contrast) !important;
}
.successTint {
  background: var(--ion-color-success-tint) !important;
  color: var(--ion-color-success-contrast) !important;
}
.warning {
  background: var(--ion-color-warning) !important;
  color: var(--ion-color-warning-contrast) !important;
}
.warningContrast {
  background: var(--ion-color-warning-contrast) !important;
  color: var(--ion-color-warning) !important;
}
.warningShade {
  background: var(--ion-color-warning-shade) !important;
  color: var(--ion-color-warning-contrast) !important;
}
.warningTint {
  background: var(--ion-color-warning-tint) !important;
  color: var(--ion-color-warning-contrast) !important;
}
.danger {
  background: var(--ion-color-danger) !important;
  color: var(--ion-color-danger-contrast) !important;
}
.dangerContrast {
  background: var(--ion-color-danger-contrast) !important;
  color: var(--ion-color-danger) !important;
}
.dangerShade {
  background: var(--ion-color-danger-shade) !important;
  color: var(--ion-color-danger-contrast) !important;
}
.dangerTint {
  background: var(--ion-color-danger-tint) !important;
  color: var(--ion-color-danger-contrast) !important;
}
.medium {
  background: var(--ion-color-medium) !important;
  color: var(--ion-color-medium-contrast) !important;
}
.mediumContrast {
  background: var(--ion-color-medium-contrast) !important;
  color: var(--ion-color-medium) !important;
}
.mediumShade {
  background: var(--ion-color-medium-shade) !important;
  color: var(--ion-color-medium-contrast) !important;
}
.mediumTint {
  background: var(--ion-color-medium-tint) !important;
  color: var(--ion-color-medium-contrast) !important;
}
.base {
  background: var(--ion-color-base) !important;
  color: var(--ion-color-base-contrast) !important;
}
.baseContrast {
  background: var(--ion-color-base-contrast) !important;
  color: var(--ion-color-base) !important;
}
.baseShade {
  background: var(--ion-color-base-shade) !important;
  color: var(--ion-color-base-contrast) !important;
}
.baseTint {
  background: var(--ion-color-base-tint) !important;
  color: var(--ion-color-base-contrast) !important;
}
.light {
  background: var(--ion-color-light) !important;
  color: var(--ion-color-light-contrast) !important;
}
.lightContrast {
  background: var(--ion-color-light-contrast) !important;
  color: var(--ion-color-light) !important;
}
.lightShade {
  background: var(--ion-color-light-shade) !important;
  color: var(--ion-color-light-contrast) !important;
}
.lightTint {
  background: var(--ion-color-light-tint) !important;
  color: var(--ion-color-light-contrast) !important;
}

.relative {
  position: relative !important;
}
.absolute {
  position: absolute !important;
}

.bottom-0 {
  bottom: 0 !important;
}

.top-0 {
  top: 0 !important;
}

.right-0 {
  right: 0 !important;
}

.left-0 {
  left: 0 !important;
}

.bg-clear {
  background: none !important;
}

.bg-primary {
  background: var(--ion-color-primary) !important;
}
.bg-primaryContrast {
  background: var(--ion-color-primary-contrast) !important;
}
.bg-primaryShade {
  background: var(--ion-color-primary-shade) !important;
}
.bg-primaryTint {
  background: var(--ion-color-primary-tint) !important;
}
.bg-secondary {
  background: var(--ion-color-secondary) !important;
}
.bg-secondaryContrast {
  background: var(--ion-color-secondary-contrast) !important;
}
.bg-secondaryShade {
  background: var(--ion-color-secondary-shade) !important;
}
.bg-secondaryTint {
  background: var(--ion-color-secondary-tint) !important;
}
.bg-tertiary {
  background: var(--ion-color-tertiary) !important;
}
.bg-tertiaryContrast {
  background: var(--ion-color-tertiary-contrast) !important;
}
.bg-tertiaryShade {
  background: var(--ion-color-tertiary-shade) !important;
}
.bg-tertiaryTint {
  background: var(--ion-color-tertiary-tint) !important;
}
.bg-success {
  background: var(--ion-color-success) !important;
}
.bg-successContrast {
  background: var(--ion-color-success-contrast) !important;
}
.bg-successShade {
  background: var(--ion-color-success-shade) !important;
}
.bg-successTint {
  background: var(--ion-color-success-tint) !important;
}
.bg-warning {
  background: var(--ion-color-warning) !important;
}
.bg-warningContrast {
  background: var(--ion-color-warning-contrast) !important;
}
.bg-warningShade {
  background: var(--ion-color-warning-shade) !important;
}
.bg-warningTint {
  background: var(--ion-color-warning-tint) !important;
}
.bg-danger {
  background: var(--ion-color-danger) !important;
}
.bg-dangerContrast {
  background: var(--ion-color-danger-contrast) !important;
}
.bg-dangerShade {
  background: var(--ion-color-danger-shade) !important;
}
.bg-dangerTint {
  background: var(--ion-color-danger-tint) !important;
}
.bg-medium {
  background: var(--ion-color-medium) !important;
}
.bg-mediumContrast {
  background: var(--ion-color-medium-contrast) !important;
}
.bg-mediumShade {
  background: var(--ion-color-medium-shade) !important;
}
.bg-mediumTint {
  background: var(--ion-color-medium-tint) !important;
}
.bg-base {
  background: var(--ion-color-base) !important;
}
.bg-baseContrast {
  background: var(--ion-color-base-contrast) !important;
}
.bg-baseShade {
  background: var(--ion-color-base-shade) !important;
}
.bg-baseTint {
  background: var(--ion-color-base-tint) !important;
}
.bg-light {
  background: var(--ion-color-light) !important;
}
.bg-lightContrast {
  background: var(--ion-color-light-contrast) !important;
}
.bg-lightShade {
  background: var(--ion-color-light-shade) !important;
}
.bg-lightTint {
  background: var(--ion-color-light-tint) !important;
}

.border-primary {
  border-color: var(--ion-color-primary) !important;
}
.border-primaryContrast {
  border-color: var(--ion-color-primary-contrast) !important;
}
.border-primaryShade {
  border-color: var(--ion-color-primary-shade) !important;
}
.border-primaryTint {
  border-color: var(--ion-color-primary-tint) !important;
}
.border-secondary {
  border-color: var(--ion-color-secondary) !important;
}
.border-secondaryContrast {
  border-color: var(--ion-color-secondary-contrast) !important;
}
.border-secondaryShade {
  border-color: var(--ion-color-secondary-shade) !important;
}
.border-secondaryTint {
  border-color: var(--ion-color-secondary-tint) !important;
}
.border-tertiary {
  border-color: var(--ion-color-tertiary) !important;
}
.border-tertiaryContrast {
  border-color: var(--ion-color-tertiary-contrast) !important;
}
.border-tertiaryShade {
  border-color: var(--ion-color-tertiary-shade) !important;
}
.border-tertiaryTint {
  border-color: var(--ion-color-tertiary-tint) !important;
}
.border-success {
  border-color: var(--ion-color-success) !important;
}
.border-successContrast {
  border-color: var(--ion-color-success-contrast) !important;
}
.border-successShade {
  border-color: var(--ion-color-success-shade) !important;
}
.border-successTint {
  border-color: var(--ion-color-success-tint) !important;
}
.border-warning {
  border-color: var(--ion-color-warning) !important;
}
.border-warningContrast {
  border-color: var(--ion-color-warning-contrast) !important;
}
.border-warningShade {
  border-color: var(--ion-color-warning-shade) !important;
}
.border-warningTint {
  border-color: var(--ion-color-warning-tint) !important;
}
.border-danger {
  border-color: var(--ion-color-danger) !important;
}
.border-dangerContrast {
  border-color: var(--ion-color-danger-contrast) !important;
}
.border-dangerShade {
  border-color: var(--ion-color-danger-shade) !important;
}
.border-dangerTint {
  border-color: var(--ion-color-danger-tint) !important;
}
.border-medium {
  border-color: var(--ion-color-medium) !important;
}
.border-mediumContrast {
  border-color: var(--ion-color-medium-contrast) !important;
}
.border-mediumShade {
  border-color: var(--ion-color-medium-shade) !important;
}
.border-mediumTint {
  border-color: var(--ion-color-medium-tint) !important;
}
.border-base {
  border-color: var(--ion-color-base) !important;
}
.border-baseContrast {
  border-color: var(--ion-color-base-contrast) !important;
}
.border-baseShade {
  border-color: var(--ion-color-base-shade) !important;
}
.border-baseTint {
  border-color: var(--ion-color-base-tint) !important;
}
.border-light {
  border-color: var(--ion-color-light) !important;
}
.border-lightContrast {
  border-color: var(--ion-color-light-contrast) !important;
}
.border-lightShade {
  border-color: var(--ion-color-light-shade) !important;
}
.border-lightTint {
  border-color: var(--ion-color-light-tint) !important;
}

.border-top-primary {
  border-top-color: var(--ion-color-primary) !important;
}
.border-top-primaryContrast {
  border-top-color: var(--ion-color-primary-contrast) !important;
}
.border-top-primaryShade {
  border-top-color: var(--ion-color-primary-shade) !important;
}
.border-top-primaryTint {
  border-top-color: var(--ion-color-primary-tint) !important;
}
.border-top-secondary {
  border-top-color: var(--ion-color-secondary) !important;
}
.border-top-secondaryContrast {
  border-top-color: var(--ion-color-secondary-contrast) !important;
}
.border-top-secondaryShade {
  border-top-color: var(--ion-color-secondary-shade) !important;
}
.border-top-secondaryTint {
  border-top-color: var(--ion-color-secondary-tint) !important;
}
.border-top-tertiary {
  border-top-color: var(--ion-color-tertiary) !important;
}
.border-top-tertiaryContrast {
  border-top-color: var(--ion-color-tertiary-contrast) !important;
}
.border-top-tertiaryShade {
  border-top-color: var(--ion-color-tertiary-shade) !important;
}
.border-top-tertiaryTint {
  border-top-color: var(--ion-color-tertiary-tint) !important;
}
.border-top-success {
  border-top-color: var(--ion-color-success) !important;
}
.border-top-successContrast {
  border-top-color: var(--ion-color-success-contrast) !important;
}
.border-top-successShade {
  border-top-color: var(--ion-color-success-shade) !important;
}
.border-top-successTint {
  border-top-color: var(--ion-color-success-tint) !important;
}
.border-top-warning {
  border-top-color: var(--ion-color-warning) !important;
}
.border-top-warningContrast {
  border-top-color: var(--ion-color-warning-contrast) !important;
}
.border-top-warningShade {
  border-top-color: var(--ion-color-warning-shade) !important;
}
.border-top-warningTint {
  border-top-color: var(--ion-color-warning-tint) !important;
}
.border-top-danger {
  border-top-color: var(--ion-color-danger) !important;
}
.border-top-dangerContrast {
  border-top-color: var(--ion-color-danger-contrast) !important;
}
.border-top-dangerShade {
  border-top-color: var(--ion-color-danger-shade) !important;
}
.border-top-dangerTint {
  border-top-color: var(--ion-color-danger-tint) !important;
}
.border-top-medium {
  border-top-color: var(--ion-color-medium) !important;
}
.border-top-mediumContrast {
  border-top-color: var(--ion-color-medium-contrast) !important;
}
.border-top-mediumShade {
  border-top-color: var(--ion-color-medium-shade) !important;
}
.border-top-mediumTint {
  border-top-color: var(--ion-color-medium-tint) !important;
}
.border-top-base {
  border-top-color: var(--ion-color-base) !important;
}
.border-top-baseContrast {
  border-top-color: var(--ion-color-base-contrast) !important;
}
.border-top-baseShade {
  border-top-color: var(--ion-color-base-shade) !important;
}
.border-top-baseTint {
  border-top-color: var(--ion-color-base-tint) !important;
}
.border-top-light {
  border-top-color: var(--ion-color-light) !important;
}
.border-top-lightContrast {
  border-top-color: var(--ion-color-light-contrast) !important;
}
.border-top-lightShade {
  border-top-color: var(--ion-color-light-shade) !important;
}
.border-top-lightTint {
  border-top-color: var(--ion-color-light-tint) !important;
}

.border-right-primary {
  border-right-color: var(--ion-color-primary) !important;
}
.border-right-primaryContrast {
  border-right-color: var(--ion-color-primary-contrast) !important;
}
.border-right-primaryShade {
  border-right-color: var(--ion-color-primary-shade) !important;
}
.border-right-primaryTint {
  border-right-color: var(--ion-color-primary-tint) !important;
}
.border-right-secondary {
  border-right-color: var(--ion-color-secondary) !important;
}
.border-right-secondaryContrast {
  border-right-color: var(--ion-color-secondary-contrast) !important;
}
.border-right-secondaryShade {
  border-right-color: var(--ion-color-secondary-shade) !important;
}
.border-right-secondaryTint {
  border-right-color: var(--ion-color-secondary-tint) !important;
}
.border-right-tertiary {
  border-right-color: var(--ion-color-tertiary) !important;
}
.border-right-tertiaryContrast {
  border-right-color: var(--ion-color-tertiary-contrast) !important;
}
.border-right-tertiaryShade {
  border-right-color: var(--ion-color-tertiary-shade) !important;
}
.border-right-tertiaryTint {
  border-right-color: var(--ion-color-tertiary-tint) !important;
}
.border-right-success {
  border-right-color: var(--ion-color-success) !important;
}
.border-right-successContrast {
  border-right-color: var(--ion-color-success-contrast) !important;
}
.border-right-successShade {
  border-right-color: var(--ion-color-success-shade) !important;
}
.border-right-successTint {
  border-right-color: var(--ion-color-success-tint) !important;
}
.border-right-warning {
  border-right-color: var(--ion-color-warning) !important;
}
.border-right-warningContrast {
  border-right-color: var(--ion-color-warning-contrast) !important;
}
.border-right-warningShade {
  border-right-color: var(--ion-color-warning-shade) !important;
}
.border-right-warningTint {
  border-right-color: var(--ion-color-warning-tint) !important;
}
.border-right-danger {
  border-right-color: var(--ion-color-danger) !important;
}
.border-right-dangerContrast {
  border-right-color: var(--ion-color-danger-contrast) !important;
}
.border-right-dangerShade {
  border-right-color: var(--ion-color-danger-shade) !important;
}
.border-right-dangerTint {
  border-right-color: var(--ion-color-danger-tint) !important;
}
.border-right-medium {
  border-right-color: var(--ion-color-medium) !important;
}
.border-right-mediumContrast {
  border-right-color: var(--ion-color-medium-contrast) !important;
}
.border-right-mediumShade {
  border-right-color: var(--ion-color-medium-shade) !important;
}
.border-right-mediumTint {
  border-right-color: var(--ion-color-medium-tint) !important;
}
.border-right-base {
  border-right-color: var(--ion-color-base) !important;
}
.border-right-baseContrast {
  border-right-color: var(--ion-color-base-contrast) !important;
}
.border-right-baseShade {
  border-right-color: var(--ion-color-base-shade) !important;
}
.border-right-baseTint {
  border-right-color: var(--ion-color-base-tint) !important;
}
.border-right-light {
  border-right-color: var(--ion-color-light) !important;
}
.border-right-lightContrast {
  border-right-color: var(--ion-color-light-contrast) !important;
}
.border-right-lightShade {
  border-right-color: var(--ion-color-light-shade) !important;
}
.border-right-lightTint {
  border-right-color: var(--ion-color-light-tint) !important;
}

.border-bottom-primary {
  border-bottom-color: var(--ion-color-primary) !important;
}
.border-bottom-primaryContrast {
  border-bottom-color: var(--ion-color-primary-contrast) !important;
}
.border-bottom-primaryShade {
  border-bottom-color: var(--ion-color-primary-shade) !important;
}
.border-bottom-primaryTint {
  border-bottom-color: var(--ion-color-primary-tint) !important;
}
.border-bottom-secondary {
  border-bottom-color: var(--ion-color-secondary) !important;
}
.border-bottom-secondaryContrast {
  border-bottom-color: var(--ion-color-secondary-contrast) !important;
}
.border-bottom-secondaryShade {
  border-bottom-color: var(--ion-color-secondary-shade) !important;
}
.border-bottom-secondaryTint {
  border-bottom-color: var(--ion-color-secondary-tint) !important;
}
.border-bottom-tertiary {
  border-bottom-color: var(--ion-color-tertiary) !important;
}
.border-bottom-tertiaryContrast {
  border-bottom-color: var(--ion-color-tertiary-contrast) !important;
}
.border-bottom-tertiaryShade {
  border-bottom-color: var(--ion-color-tertiary-shade) !important;
}
.border-bottom-tertiaryTint {
  border-bottom-color: var(--ion-color-tertiary-tint) !important;
}
.border-bottom-success {
  border-bottom-color: var(--ion-color-success) !important;
}
.border-bottom-successContrast {
  border-bottom-color: var(--ion-color-success-contrast) !important;
}
.border-bottom-successShade {
  border-bottom-color: var(--ion-color-success-shade) !important;
}
.border-bottom-successTint {
  border-bottom-color: var(--ion-color-success-tint) !important;
}
.border-bottom-warning {
  border-bottom-color: var(--ion-color-warning) !important;
}
.border-bottom-warningContrast {
  border-bottom-color: var(--ion-color-warning-contrast) !important;
}
.border-bottom-warningShade {
  border-bottom-color: var(--ion-color-warning-shade) !important;
}
.border-bottom-warningTint {
  border-bottom-color: var(--ion-color-warning-tint) !important;
}
.border-bottom-danger {
  border-bottom-color: var(--ion-color-danger) !important;
}
.border-bottom-dangerContrast {
  border-bottom-color: var(--ion-color-danger-contrast) !important;
}
.border-bottom-dangerShade {
  border-bottom-color: var(--ion-color-danger-shade) !important;
}
.border-bottom-dangerTint {
  border-bottom-color: var(--ion-color-danger-tint) !important;
}
.border-bottom-medium {
  border-bottom-color: var(--ion-color-medium) !important;
}
.border-bottom-mediumContrast {
  border-bottom-color: var(--ion-color-medium-contrast) !important;
}
.border-bottom-mediumShade {
  border-bottom-color: var(--ion-color-medium-shade) !important;
}
.border-bottom-mediumTint {
  border-bottom-color: var(--ion-color-medium-tint) !important;
}
.border-bottom-base {
  border-bottom-color: var(--ion-color-base) !important;
}
.border-bottom-baseContrast {
  border-bottom-color: var(--ion-color-base-contrast) !important;
}
.border-bottom-baseShade {
  border-bottom-color: var(--ion-color-base-shade) !important;
}
.border-bottom-baseTint {
  border-bottom-color: var(--ion-color-base-tint) !important;
}
.border-bottom-light {
  border-bottom-color: var(--ion-color-light) !important;
}
.border-bottom-lightContrast {
  border-bottom-color: var(--ion-color-light-contrast) !important;
}
.border-bottom-lightShade {
  border-bottom-color: var(--ion-color-light-shade) !important;
}
.border-bottom-lightTint {
  border-bottom-color: var(--ion-color-light-tint) !important;
}

.border-left-primary {
  border-left-color: var(--ion-color-primary) !important;
}
.border-left-primaryContrast {
  border-left-color: var(--ion-color-primary-contrast) !important;
}
.border-left-primaryShade {
  border-left-color: var(--ion-color-primary-shade) !important;
}
.border-left-primaryTint {
  border-left-color: var(--ion-color-primary-tint) !important;
}
.border-left-secondary {
  border-left-color: var(--ion-color-secondary) !important;
}
.border-left-secondaryContrast {
  border-left-color: var(--ion-color-secondary-contrast) !important;
}
.border-left-secondaryShade {
  border-left-color: var(--ion-color-secondary-shade) !important;
}
.border-left-secondaryTint {
  border-left-color: var(--ion-color-secondary-tint) !important;
}
.border-left-tertiary {
  border-left-color: var(--ion-color-tertiary) !important;
}
.border-left-tertiaryContrast {
  border-left-color: var(--ion-color-tertiary-contrast) !important;
}
.border-left-tertiaryShade {
  border-left-color: var(--ion-color-tertiary-shade) !important;
}
.border-left-tertiaryTint {
  border-left-color: var(--ion-color-tertiary-tint) !important;
}
.border-left-success {
  border-left-color: var(--ion-color-success) !important;
}
.border-left-successContrast {
  border-left-color: var(--ion-color-success-contrast) !important;
}
.border-left-successShade {
  border-left-color: var(--ion-color-success-shade) !important;
}
.border-left-successTint {
  border-left-color: var(--ion-color-success-tint) !important;
}
.border-left-warning {
  border-left-color: var(--ion-color-warning) !important;
}
.border-left-warningContrast {
  border-left-color: var(--ion-color-warning-contrast) !important;
}
.border-left-warningShade {
  border-left-color: var(--ion-color-warning-shade) !important;
}
.border-left-warningTint {
  border-left-color: var(--ion-color-warning-tint) !important;
}
.border-left-danger {
  border-left-color: var(--ion-color-danger) !important;
}
.border-left-dangerContrast {
  border-left-color: var(--ion-color-danger-contrast) !important;
}
.border-left-dangerShade {
  border-left-color: var(--ion-color-danger-shade) !important;
}
.border-left-dangerTint {
  border-left-color: var(--ion-color-danger-tint) !important;
}
.border-left-medium {
  border-left-color: var(--ion-color-medium) !important;
}
.border-left-mediumContrast {
  border-left-color: var(--ion-color-medium-contrast) !important;
}
.border-left-mediumShade {
  border-left-color: var(--ion-color-medium-shade) !important;
}
.border-left-mediumTint {
  border-left-color: var(--ion-color-medium-tint) !important;
}
.border-left-base {
  border-left-color: var(--ion-color-base) !important;
}
.border-left-baseContrast {
  border-left-color: var(--ion-color-base-contrast) !important;
}
.border-left-baseShade {
  border-left-color: var(--ion-color-base-shade) !important;
}
.border-left-baseTint {
  border-left-color: var(--ion-color-base-tint) !important;
}
.border-left-light {
  border-left-color: var(--ion-color-light) !important;
}
.border-left-lightContrast {
  border-left-color: var(--ion-color-light-contrast) !important;
}
.border-left-lightShade {
  border-left-color: var(--ion-color-light-shade) !important;
}
.border-left-lightTint {
  border-left-color: var(--ion-color-light-tint) !important;
}

.border-1 {
  border-width: 1px !important;
}

.border-2 {
  border-width: 2px !important;
}

.border-3 {
  border-width: 3px !important;
}

.border-4 {
  border-width: 4px !important;
}

.border-5 {
  border-width: 5px !important;
}

.border-solid {
  border-style: solid !important;
}

.border-dashed {
  border-style: dashed !important;
}

.border-dotted {
  border-style: dotted !important;
}

.border-none {
  border-style: none !important;
}

.text-primary {
  color: var(--ion-color-primary) !important;
}
.text-primaryContrast {
  color: var(--ion-color-primary-contrast) !important;
}
.text-primaryShade {
  color: var(--ion-color-primary-shade) !important;
}
.text-primaryTint {
  color: var(--ion-color-primary-tint) !important;
}
.text-secondary {
  color: var(--ion-color-secondary) !important;
}
.text-secondaryContrast {
  color: var(--ion-color-secondary-contrast) !important;
}
.text-secondaryShade {
  color: var(--ion-color-secondary-shade) !important;
}
.text-secondaryTint {
  color: var(--ion-color-secondary-tint) !important;
}
.text-tertiary {
  color: var(--ion-color-tertiary) !important;
}
.text-tertiaryContrast {
  color: var(--ion-color-tertiary-contrast) !important;
}
.text-tertiaryShade {
  color: var(--ion-color-tertiary-shade) !important;
}
.text-tertiaryTint {
  color: var(--ion-color-tertiary-tint) !important;
}
.text-success {
  color: var(--ion-color-success) !important;
}
.text-successContrast {
  color: var(--ion-color-success-contrast) !important;
}
.text-successShade {
  color: var(--ion-color-success-shade) !important;
}
.text-successTint {
  color: var(--ion-color-success-tint) !important;
}
.text-warning {
  color: var(--ion-color-warning) !important;
}
.text-warningContrast {
  color: var(--ion-color-warning-contrast) !important;
}
.text-warningShade {
  color: var(--ion-color-warning-shade) !important;
}
.text-warningTint {
  color: var(--ion-color-warning-tint) !important;
}
.text-danger {
  color: var(--ion-color-danger) !important;
}
.text-dangerContrast {
  color: var(--ion-color-danger-contrast) !important;
}
.text-dangerShade {
  color: var(--ion-color-danger-shade) !important;
}
.text-dangerTint {
  color: var(--ion-color-danger-tint) !important;
}
.text-medium {
  color: var(--ion-color-medium) !important;
}
.text-mediumContrast {
  color: var(--ion-color-medium-contrast) !important;
}
.text-mediumShade {
  color: var(--ion-color-medium-shade) !important;
}
.text-mediumTint {
  color: var(--ion-color-medium-tint) !important;
}
.text-base {
  color: var(--ion-color-base) !important;
}
.text-baseContrast {
  color: var(--ion-color-base-contrast) !important;
}
.text-baseShade {
  color: var(--ion-color-base-shade) !important;
}
.text-baseTint {
  color: var(--ion-color-base-tint) !important;
}
.text-light {
  color: var(--ion-color-light) !important;
}
.text-lightContrast {
  color: var(--ion-color-light-contrast) !important;
}
.text-lightShade {
  color: var(--ion-color-light-shade) !important;
}
.text-lightTint {
  color: var(--ion-color-light-tint) !important;
}

.shadow {
  box-shadow: 0px 0px 25px black !important;
}
.shadow-primary {
  box-shadow: 0px 0px 25px var(--ion-color-primary) !important;
}
.shadow-primaryContrast {
  box-shadow: 0px 0px 25px var(--ion-color-primary-contrast) !important;
}
.shadow-primaryShade {
  box-shadow: 0px 0px 25px var(--ion-color-primary-shade) !important;
}
.shadow-primaryTint {
  box-shadow: 0px 0px 25px var(--ion-color-primary-tint) !important;
}
.shadow-secondary {
  box-shadow: 0px 0px 25px var(--ion-color-secondary) !important;
}
.shadow-secondaryContrast {
  box-shadow: 0px 0px 25px var(--ion-color-secondary-contrast) !important;
}
.shadow-secondaryShade {
  box-shadow: 0px 0px 25px var(--ion-color-secondary-shade) !important;
}
.shadow-secondaryTint {
  box-shadow: 0px 0px 25px var(--ion-color-secondary-tint) !important;
}
.shadow-tertiary {
  box-shadow: 0px 0px 25px var(--ion-color-tertiary) !important;
}
.shadow-tertiaryContrast {
  box-shadow: 0px 0px 25px var(--ion-color-tertiary-contrast) !important;
}
.shadow-tertiaryShade {
  box-shadow: 0px 0px 25px var(--ion-color-tertiary-shade) !important;
}
.shadow-tertiaryTint {
  box-shadow: 0px 0px 25px var(--ion-color-tertiary-tint) !important;
}
.shadow-success {
  box-shadow: 0px 0px 25px var(--ion-color-success) !important;
}
.shadow-successContrast {
  box-shadow: 0px 0px 25px var(--ion-color-success-contrast) !important;
}
.shadow-successShade {
  box-shadow: 0px 0px 25px var(--ion-color-success-shade) !important;
}
.shadow-successTint {
  box-shadow: 0px 0px 25px var(--ion-color-success-tint) !important;
}
.shadow-warning {
  box-shadow: 0px 0px 25px var(--ion-color-warning) !important;
}
.shadow-warningContrast {
  box-shadow: 0px 0px 25px var(--ion-color-warning-contrast) !important;
}
.shadow-warningShade {
  box-shadow: 0px 0px 25px var(--ion-color-warning-shade) !important;
}
.shadow-warningTint {
  box-shadow: 0px 0px 25px var(--ion-color-warning-tint) !important;
}
.shadow-danger {
  box-shadow: 0px 0px 25px var(--ion-color-danger) !important;
}
.shadow-dangerContrast {
  box-shadow: 0px 0px 25px var(--ion-color-danger-contrast) !important;
}
.shadow-dangerShade {
  box-shadow: 0px 0px 25px var(--ion-color-danger-shade) !important;
}
.shadow-dangerTint {
  box-shadow: 0px 0px 25px var(--ion-color-danger-tint) !important;
}
.shadow-medium {
  box-shadow: 0px 0px 25px var(--ion-color-medium) !important;
}
.shadow-mediumContrast {
  box-shadow: 0px 0px 25px var(--ion-color-medium-contrast) !important;
}
.shadow-mediumShade {
  box-shadow: 0px 0px 25px var(--ion-color-medium-shade) !important;
}
.shadow-mediumTint {
  box-shadow: 0px 0px 25px var(--ion-color-medium-tint) !important;
}
.shadow-base {
  box-shadow: 0px 0px 25px var(--ion-color-base) !important;
}
.shadow-baseContrast {
  box-shadow: 0px 0px 25px var(--ion-color-base-contrast) !important;
}
.shadow-baseShade {
  box-shadow: 0px 0px 25px var(--ion-color-base-shade) !important;
}
.shadow-baseTint {
  box-shadow: 0px 0px 25px var(--ion-color-base-tint) !important;
}
.shadow-light {
  box-shadow: 0px 0px 25px var(--ion-color-light) !important;
}
.shadow-lightContrast {
  box-shadow: 0px 0px 25px var(--ion-color-light-contrast) !important;
}
.shadow-lightShade {
  box-shadow: 0px 0px 25px var(--ion-color-light-shade) !important;
}
.shadow-lightTint {
  box-shadow: 0px 0px 25px var(--ion-color-light-tint) !important;
}

.flex-nowrap {
  display: flex;
  flex-wrap: nowrap;
}

.flex-wrap {
  display: flex;
  flex-direction: row;
}

.col-2 {
  column-count: 2;
}

.col-3 {
  column-count: 3;
}

.col-4 {
  column-count: 4;
}

.col-5 {
  column-count: 5;
}

.col-6 {
  column-count: 6;
}

.col-7 {
  column-count: 7;
}

.col-8 {
  column-count: 8;
}

.col-9 {
  column-count: 9;
}

.col-10 {
  column-count: 10;
}

.col-11 {
  column-count: 11;
}

.col-12 {
  column-count: 12;
}

.rounded-sm {
  border-radius: 10px !important;
}
.rounded-md {
  border-radius: 20px !important;
}
.rounded-lg {
  border-radius: 30px !important;
}
.rounded-full {
  border-radius: 50% !important;
}

.rounded-top {
  border-radius: 10px 10px 0px 0px !important;
}
.rounded-bottom {
  border-radius: 0px 0px 10px 10px !important;
}
.rounded-left {
  border-radius: 10px 0px 0px 10px !important;
}
.rounded-right {
  border-radius: 0px 10px 10px 0px !important;
}

.rounded-top-left {
  border-radius: 10px 0px 0px 0px !important;
}

.rounded-top-right {
  border-radius: 0px 10px 0px 0px !important;
}

.rounded-bottom-left {
  border-radius: 0px 0px 10px 0px !important;
}

.rounded-bottom-right {
  border-radius: 0px 0px 0px 10px !important;
}

.rounded-none {
  border-radius: 0px !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.w-full {
  width: 100% !important;
}
.w-90 {
  width: 90% !important;
}

.w-80 {
  width: 80% !important;
}

.w-70 {
  width: 70% !important;
}

.w-60 {
  width: 60% !important;
}
.w-50 {
  width: 50% !important;
}

.w-33 {
  width: 33.333333% !important;
}

.h-screen {
  height: 100vh !important;
}

.h-full {
  height: 100% !important;
}

.h-50 {
  height: 50% !important;
}

.h-33 {
  height: 33.333333% !important;
}

.h-1 {
  height: 1px !important;
}

.h-2 {
  height: 6px !important;
}

.h-3 {
  height: 12px !important;
}

.h-4 {
  height: 20px !important;
}

.h-5 {
  height: 35px !important;
}

.h-6 {
  height: 40px !important;
}

.h-7 {
  height: 45px !important;
}

.h-8 {
  height: 50px !important;
}

.h-9 {
  height: 55px !important;
}

.h-10 {
  height: 60px !important;
}

.h-11 {
  height: 65px !important;
}

.h-12 {
  height: 70px !important;
}

.h-13 {
  height: 75px !important;
}

.h-14 {
  height: 80px !important;
}

.h-15 {
  height: 85px !important;
}

.h-16 {
  height: 90px !important;
}

.w-1 {
  width: 1px !important;
}

.w-2 {
  width: 6px !important;
}

.w-3 {
  width: 12px !important;
}

.w-4 {
  width: 20px !important;
}

.w-5 {
  width: 35px !important;
}

.w-6 {
  width: 40px !important;
}

.w-7 {
  width: 45px !important;
}

.w-8 {
  width: 50px !important;
}

.w-9 {
  width: 55px !important;
}

.w-10 {
  width: 60px !important;
}

.w-11 {
  width: 65px !important;
}

.w-12 {
  width: 70px !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-3 {
  margin-bottom: 0.75rem !important;
}

.mb-4 {
  margin-bottom: 1rem !important;
}

.mb-5 {
  margin-bottom: 1.25rem !important;
}

.mb-6 {
  margin-bottom: 1.5rem !important;
}

.mb-7 {
  margin-bottom: 1.75rem !important;
}

.mb-8 {
  margin-bottom: 2rem !important;
}

.mb-9 {
  margin-bottom: 2.25rem !important;
}

.mb-10 {
  margin-bottom: 2.5rem !important;
}

.mb-11 {
  margin-bottom: 2.75rem !important;
}

.mb-12 {
  margin-bottom: 3rem !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mt-3 {
  margin-top: 0.75rem !important;
}

.mt-4 {
  margin-top: 1rem !important;
}

.mt-5 {
  margin-top: 1.25rem !important;
}

.mt-6 {
  margin-top: 1.5rem !important;
}

.mt-7 {
  margin-top: 1.75rem !important;
}

.mt-8 {
  margin-top: 2rem !important;
}

.mt-9 {
  margin-top: 2.25rem !important;
}

.mt-10 {
  margin-top: 2.5rem !important;
}

.mt-11 {
  margin-top: 2.75rem !important;
}

.mt-12 {
  margin-top: 3rem !important;
}

.mr-1 {
  margin-right: 0.25rem !important;
}

.mr-2 {
  margin-right: 0.5rem !important;
}

.mr-3 {
  margin-right: 0.75rem !important;
}

.mr-4 {
  margin-right: 1rem !important;
}

.mr-5 {
  margin-right: 1.25rem !important;
}

.mr-6 {
  margin-right: 1.5rem !important;
}

.mr-7 {
  margin-right: 1.75rem !important;
}

.mr-8 {
  margin-right: 2rem !important;
}

.mr-9 {
  margin-right: 2.25rem !important;
}

.mr-10 {
  margin-right: 2.5rem !important;
}

.mr-11 {
  margin-right: 2.75rem !important;
}

.mr-12 {
  margin-right: 3rem !important;
}

.ml-1 {
  margin-left: 0.25rem !important;
}

.ml-2 {
  margin-left: 0.5rem !important;
}

.ml-3 {
  margin-left: 0.75rem !important;
}

.ml-4 {
  margin-left: 1rem !important;
}

.ml-5 {
  margin-left: 1.25rem !important;
}

.ml-6 {
  margin-left: 1.5rem !important;
}

.ml-7 {
  margin-left: 1.75rem !important;
}

.ml-8 {
  margin-left: 2rem !important;
}

.ml-9 {
  margin-left: 2.25rem !important;
}

.ml-10 {
  margin-left: 2.5rem !important;
}

.ml-11 {
  margin-left: 2.75rem !important;
}

.ml-12 {
  margin-left: 3rem !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.m-3 {
  margin: 0.75rem !important;
}

.m-4 {
  margin: 1rem !important;
}

.m-5 {
  margin: 1.25rem !important;
}

.m-6 {
  margin: 1.5rem !important;
}

.m-7 {
  margin: 1.75rem !important;
}

.m-8 {
  margin: 2rem !important;
}

.m-9 {
  margin: 2.25rem !important;
}

.m-10 {
  margin: 2.5rem !important;
}

.m-11 {
  margin: 2.75rem !important;
}

.m-12 {
  margin: 3rem !important;
}

.-m-1 {
  margin: -0.25rem !important;
}

.-m-2 {
  margin: -0.5rem !important;
}

.-m-3 {
  margin: -0.75rem !important;
}

.-m-4 {
  margin: -1rem !important;
}

.-m-5 {
  margin: -1.25rem !important;
}

.-m-6 {
  margin: -1.5rem !important;
}

.-m-7 {
  margin: -1.75rem !important;
}

.-m-8 {
  margin: -2rem !important;
}

.-m-9 {
  margin: -2.25rem !important;
}

.-m-10 {
  margin: -2.5rem !important;
}

.-m-11 {
  margin: -2.75rem !important;
}

.-m-12 {
  margin: -3rem !important;
}

.my-1 {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
}

.my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}

.my-3 {
  margin-top: 0.75rem !important;
  margin-bottom: 0.75rem !important;
}

.my-4 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.my-5 {
  margin-top: 1.25rem !important;
  margin-bottom: 1.25rem !important;
}

.my-6 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

.my-7 {
  margin-top: 1.75rem !important;
  margin-bottom: 1.75rem !important;
}

.my-8 {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
}

.my-9 {
  margin-top: 2.25rem !important;
  margin-bottom: 2.25rem !important;
}

.my-10 {
  margin-top: 2.5rem !important;
  margin-bottom: 2.5rem !important;
}

.my-11 {
  margin-top: 2.75rem !important;
  margin-bottom: 2.75rem !important;
}

.my-12 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;
}
.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}
.mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0.25rem !important;
}

.mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important;
}

.mx-3 {
  margin-right: 0.75rem !important;
  margin-left: 0.75rem !important;
}

.mx-4 {
  margin-right: 1rem !important;
  margin-left: 1rem !important;
}

.mx-5 {
  margin-right: 1.25rem !important;
  margin-left: 1.25rem !important;
}

.mx-6 {
  margin-right: 1.5rem !important;
  margin-left: 1.5rem !important;
}

.mx-7 {
  margin-right: 1.75rem !important;
  margin-left: 1.75rem !important;
}

.mx-8 {
  margin-right: 2rem !important;
  margin-left: 2rem !important;
}

.mx-9 {
  margin-right: 2.25rem !important;
  margin-left: 2.25rem !important;
}

.mx-10 {
  margin-right: 2.5rem !important;
  margin-left: 2.5rem !important;
}

.mx-11 {
  margin-right: 2.75rem !important;
  margin-left: 2.75rem !important;
}

.mx-12 {
  margin-right: 3rem !important;
  margin-left: 3rem !important;
}

.-mt-1 {
  margin-top: -0.25rem !important;
}

.-mt-2 {
  margin-top: -0.5rem !important;
}

.-mt-3 {
  margin-top: -0.75rem !important;
}

.-mt-4 {
  margin-top: -1rem !important;
}

.-mt-5 {
  margin-top: -1.25rem !important;
}

.-mt-6 {
  margin-top: -1.5rem !important;
}

.-mt-7 {
  margin-top: -1.75rem !important;
}

.-mt-8 {
  margin-top: -2rem !important;
}

.-mt-9 {
  margin-top: -2.25rem !important;
}

.-mt-10 {
  margin-top: -2.5rem !important;
}

.-mt-11 {
  margin-top: -2.75rem !important;
}

.-mt-12 {
  margin-top: -3rem !important;
}

.-mr-1 {
  margin-right: -0.25rem !important;
}

.-mr-2 {
  margin-right: -0.5rem !important;
}

.-mr-3 {
  margin-right: -0.75rem !important;
}

.-mr-4 {
  margin-right: -1rem !important;
}

.-mr-5 {
  margin-right: -1.25rem !important;
}

.-mr-6 {
  margin-right: -1.5rem !important;
}

.-mr-7 {
  margin-right: -1.75rem !important;
}

.-mr-8 {
  margin-right: -2rem !important;
}

.-mr-9 {
  margin-right: -2.25rem !important;
}

.-mr-10 {
  margin-right: -2.5rem !important;
}

.-mr-11 {
  margin-right: -2.75rem !important;
}

.-mr-12 {
  margin-right: -3rem !important;
}

.-mb-1 {
  margin-bottom: -0.25rem !important;
}

.-mb-2 {
  margin-bottom: -0.5rem !important;
}

.-mb-3 {
  margin-bottom: -0.75rem !important;
}

.-mb-4 {
  margin-bottom: -1rem !important;
}

.-mb-5 {
  margin-bottom: -1.25rem !important;
}

.-mb-6 {
  margin-bottom: -1.5rem !important;
}

.-mb-7 {
  margin-bottom: -1.75rem !important;
}

.-mb-8 {
  margin-bottom: -2rem !important;
}

.-mb-9 {
  margin-bottom: -2.25rem !important;
}

.-mb-10 {
  margin-bottom: -2.5rem !important;
}

.-mb-11 {
  margin-bottom: -2.75rem !important;
}

.-mb-12 {
  margin-bottom: -3rem !important;
}

.-ml-1 {
  margin-left: -0.25rem !important;
}

.-ml-2 {
  margin-left: -0.5rem !important;
}

.-ml-3 {
  margin-left: -0.75rem !important;
}

.-ml-4 {
  margin-left: -1rem !important;
}

.-ml-5 {
  margin-left: -1.25rem !important;
}

.-ml-6 {
  margin-left: -1.5rem !important;
}

.-ml-7 {
  margin-left: -1.75rem !important;
}

.-ml-8 {
  margin-left: -2rem !important;
}

.-ml-9 {
  margin-left: -2.25rem !important;
}

.-ml-10 {
  margin-left: -2.5rem !important;
}

.-ml-11 {
  margin-left: -2.75rem !important;
}

.-ml-12 {
  margin-left: -3rem !important;
}

.pt-1 {
  padding-top: 0.25rem !important;
}

.pt-2 {
  padding-top: 0.5rem !important;
}

.pt-3 {
  padding-top: 0.75rem !important;
}

.pt-4 {
  padding-top: 1rem !important;
}

.pt-5 {
  padding-top: 1.25rem !important;
}

.pt-6 {
  padding-top: 1.5rem !important;
}

.pt-7 {
  padding-top: 1.75rem !important;
}

.pt-8 {
  padding-top: 2rem !important;
}

.pt-9 {
  padding-top: 2.25rem !important;
}

.pt-10 {
  padding-top: 2.5rem !important;
}

.pt-11 {
  padding-top: 2.75rem !important;
}

.pt-12 {
  padding-top: 3rem !important;
}

.pr-1 {
  padding-right: 0.25rem !important;
}

.pr-2 {
  padding-right: 0.5rem !important;
}

.pr-3 {
  padding-right: 0.75rem !important;
}

.pr-4 {
  padding-right: 1rem !important;
}

.pr-5 {
  padding-right: 1.25rem !important;
}

.pr-6 {
  padding-right: 1.5rem !important;
}

.pr-7 {
  padding-right: 1.75rem !important;
}

.pr-8 {
  padding-right: 2rem !important;
}

.pr-9 {
  padding-right: 2.25rem !important;
}

.pr-10 {
  padding-right: 2.5rem !important;
}

.pr-11 {
  padding-right: 2.75rem !important;
}

.pr-12 {
  padding-right: 3rem !important;
}

.pb-1 {
  padding-bottom: 0.25rem !important;
}

.pb-2 {
  padding-bottom: 0.5rem !important;
}

.pb-3 {
  padding-bottom: 0.75rem !important;
}

.pb-4 {
  padding-bottom: 1rem !important;
}

.pb-5 {
  padding-bottom: 1.25rem !important;
}

.pb-6 {
  padding-bottom: 1.5rem !important;
}

.pb-7 {
  padding-bottom: 1.75rem !important;
}

.pb-8 {
  padding-bottom: 2rem !important;
}

.pb-9 {
  padding-bottom: 2.25rem !important;
}

.pb-10 {
  padding-bottom: 2.5rem !important;
}

.pb-11 {
  padding-bottom: 2.75rem !important;
}

.pb-12 {
  padding-bottom: 3rem !important;
}

.pl-1 {
  padding-left: 0.25rem !important;
}

.pl-2 {
  padding-left: 0.5rem !important;
}

.pl-3 {
  padding-left: 0.75rem !important;
}

.pl-4 {
  padding-left: 1rem !important;
}

.pl-5 {
  padding-left: 1.25rem !important;
}

.pl-6 {
  padding-left: 1.5rem !important;
}

.pl-7 {
  padding-left: 1.75rem !important;
}

.pl-8 {
  padding-left: 2rem !important;
}

.pl-9 {
  padding-left: 2.25rem !important;
}

.pl-10 {
  padding-left: 2.5rem !important;
}

.pl-11 {
  padding-left: 2.75rem !important;
}

.pl-12 {
  padding-left: 3rem !important;
}

.px-1 {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.px-2 {
  padding-left: 2rem !important;
  padding-right: 2rem !important;
}

.px-3 {
  padding-left: 3rem !important;
  padding-right: 3rem !important;
}

.px-4 {
  padding-left: 4rem !important;
  padding-right: 4rem !important;
}

.px-5 {
  padding-left: 5rem !important;
  padding-right: 5rem !important;
}

.px-6 {
  padding-left: 6rem !important;
  padding-right: 6rem !important;
}

.px-7 {
  padding-left: 7rem !important;
  padding-right: 7rem !important;
}

.px-8 {
  padding-left: 8rem !important;
  padding-right: 8rem !important;
}

.px-9 {
  padding-left: 9rem !important;
  padding-right: 9rem !important;
}

.px-10 {
  padding-left: 10rem !important;
  padding-right: 10rem !important;
}

.px-11 {
  padding-left: 11rem !important;
  padding-right: 11rem !important;
}

.px-12 {
  padding-left: 12rem !important;
  padding-right: 12rem !important;
}

.py-1 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.py-2 {
  padding-top: 2rem !important;
  padding-bottom: 2rem !important;
}

.py-3 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

.py-4 {
  padding-top: 4rem !important;
  padding-top: 4rem !important;
}

.py-5 {
  padding-top: 5rem !important;
  padding-bottom: 5rem !important;
}

.py-6 {
  padding-top: 6rem !important;
  padding-bottom: 6rem !important;
}

.py-7 {
  padding-top: 7rem !important;
  padding-bottom: 7rem !important;
}

.py-8 {
  padding-top: 8rem !important;
  padding-bottom: 8rem !important;
}

.py-9 {
  padding-top: 9rem !important;
  padding-bottom: 9rem !important;
}

.py-10 {
  padding-top: 10rem !important;
  padding-bottom: 10rem !important;
}

.py-11 {
  padding-top: 11rem !important;
  padding-bottom: 11rem !important;
}

.py-12 {
  padding-top: 12rem !important;
  padding-bottom: 12rem !important;
}

.p-1 {
  padding: 1px !important;
}

.p-2 {
  padding: 5px !important;
}

.p-3 {
  padding: 7px !important;
}

.p-4 {
  padding: 10px !important;
}

.p-5 {
  padding: 16px !important;
}

.p-6 {
  padding: 18px !important;
}

.p-7 {
  padding: 20px !important;
}

.p-8 {
  padding: 25px !important;
}

.p-9 {
  padding: 30px !important;
}

.p-10 {
  padding: 35px !important;
}
.text-xs {
  font-size: 0.75rem !important;
}

.text-sm {
  font-size: 0.875rem !important;
}

.text-base {
  font-size: 1rem !important;
}

.text-lg {
  font-size: 1.125rem !important;
}

.text-xl {
  font-size: 1.25rem !important;
}

.text-2xl {
  font-size: 1.5rem !important;
}

.text-3xl {
  font-size: 1.875rem !important;
}

.text-4xl {
  font-size: 2.25rem !important;
}

.text-5xl {
  font-size: 3rem !important;
}

.text-6xl {
  font-size: 4rem !important;
}

.text-center {
  text-align: center !important;
}

.items-center {
  align-items: center !important;
}

.items-start {
  align-items: flex-start !important;
}

.items-end {
  align-items: flex-end !important;
}

.items-baseline {
  align-items: baseline !important;
}

.content-center {
  align-content: center !important;
}

.content-start {
  align-content: flex-start !important;
}

.content-end {
  align-content: flex-end !important;
}

.content-between {
  align-content: space-between !important;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-justify {
  text-align: justify !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

.text-truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-decoration-underline {
  text-decoration: underline !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.text-decoration-overline {
  text-decoration: overline !important;
}

.text-decoration-underline-line-through {
  text-decoration: underline line-through !important;
}

.text-decoration-underline-overline {
  text-decoration: underline overline !important;
}

.text-decoration-line-through-overline {
  text-decoration: line-through overline !important;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-spin {
  animation: spin 2s linear infinite;
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-none {
  animation: none;
}

.bg-fixed {
  background-attachment: fixed !important;
}

.bg-local {
  background-attachment: local !important;
}

.bg-scroll {
  background-attachment: scroll !important;
}

.bg-clip-border {
  background-clip: border-box !important;
}

.bg-clip-padding {
  background-clip: padding-box !important;
}

.bg-clip-content {
  background-clip: content-box !important;
}

.bg-clip-text {
  background-clip: text !important;
}

.icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  stroke-width: 0;
  stroke: currentColor;
  fill: currentColor;
}

.icon-lg {
  width: 1.25em;
  height: 1.25em;
}

.icon-xl {
  width: 1.5em;
  height: 1.5em;
}

.icon-2xl {
  width: 2em;
  height: 2em;
}

.action-icon {
  position: relative;
  top: -25px;
  right: -23px;
  border-radius: 3rem;
  width: 4rem;
  height: 4rem;
  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.4);
  text-align: center;
  padding: 25px 0;
  background-color: var(--ion-color-secondary);
  color: var(--ion-color-primary);
}
// Slate
// 50
// #f8fafc
// 100
// #f1f5f9
// 200
// #e2e8f0
// 300
// #cbd5e1
// 400
// #94a3b8
// 500
// #64748b
// 600
// #475569
// 700
// #334155
// 800
// #1e293b
// 900
// #0f172a
// 950
// #020617
// Gray
// 50
// #f9fafb
// 100
// #f3f4f6
// 200
// #e5e7eb
// 300
// #d1d5db
// 400
// #9ca3af
// 500
// #6b7280
// 600
// #4b5563
// 700
// #374151
// 800
// #1f2937
// 900
// #111827
// 950
// #030712
// Zinc
// 50
// #fafafa
// 100
// #f4f4f5
// 200
// #e4e4e7
// 300
// #d4d4d8
// 400
// #a1a1aa
// 500
// #71717a
// 600
// #52525b
// 700
// #3f3f46
// 800
// #27272a
// 900
// #18181b
// 950
// #09090b
// Neutral
// 50
// #fafafa
// 100
// #f5f5f5
// 200
// #e5e5e5
// 300
// #d4d4d4
// 400
// #a3a3a3
// 500
// #737373
// 600
// #525252
// 700
// #404040
// 800
// #262626
// 900
// #171717
// 950
// #0a0a0a
// Stone
// 50
// #fafaf9
// 100
// #f5f5f4
// 200
// #e7e5e4
// 300
// #d6d3d1
// 400
// #a8a29e
// 500
// #78716c
// 600
// #57534e
// 700
// #44403c
// 800
// #292524
// 900
// #1c1917
// 950
// #0c0a09
// Red
// 50
// #fef2f2
// 100
// #fee2e2
// 200
// #fecaca
// 300
// #fca5a5
// 400
// #f87171
// 500
// #ef4444
// 600
// #dc2626
// 700
// #b91c1c
// 800
// #991b1b
// 900
// #7f1d1d
// 950
// #450a0a
// Orange
// 50
// #fff7ed
// 100
// #ffedd5
// 200
// #fed7aa
// 300
// #fdba74
// 400
// #fb923c
// 500
// #f97316
// 600
// #ea580c
// 700
// #c2410c
// 800
// #9a3412
// 900
// #7c2d12
// 950
// #431407
// Amber
// 50
// #fffbeb
// 100
// #fef3c7
// 200
// #fde68a
// 300
// #fcd34d
// 400
// #fbbf24
// 500
// #f59e0b
// 600
// #d97706
// 700
// #b45309
// 800
// #92400e
// 900
// #78350f
// 950
// #451a03
// Yellow
// 50
// #fefce8
// 100
// #fef9c3
// 200
// #fef08a
// 300
// #fde047
// 400
// #facc15
// 500
// #eab308
// 600
// #ca8a04
// 700
// #a16207
// 800
// #854d0e
// 900
// #713f12
// 950
// #422006
// Lime
// 50
// #f7fee7
// 100
// #ecfccb
// 200
// #d9f99d
// 300
// #bef264
// 400
// #a3e635
// 500
// #84cc16
// 600
// #65a30d
// 700
// #4d7c0f
// 800
// #3f6212
// 900
// #365314
// 950
// #1a2e05
// Green
// 50
// #f0fdf4
// 100
// #dcfce7
// 200
// #bbf7d0
// 300
// #86efac
// 400
// #4ade80
// 500
// #22c55e
// 600
// #16a34a
// 700
// #15803d
// 800
// #166534
// 900
// #14532d
// 950
// #052e16
// Emerald
// 50
// #ecfdf5
// 100
// #d1fae5
// 200
// #a7f3d0
// 300
// #6ee7b7
// 400
// #34d399
// 500
// #10b981
// 600
// #059669
// 700
// #047857
// 800
// #065f46
// 900
// #064e3b
// 950
// #022c22
// Teal
// 50
// #f0fdfa
// 100
// #ccfbf1
// 200
// #99f6e4
// 300
// #5eead4
// 400
// #2dd4bf
// 500
// #14b8a6
// 600
// #0d9488
// 700
// #0f766e
// 800
// #115e59
// 900
// #134e4a
// 950
// #042f2e
// Cyan
// 50
// #ecfeff
// 100
// #cffafe
// 200
// #a5f3fc
// 300
// #67e8f9
// 400
// #22d3ee
// 500
// #06b6d4
// 600
// #0891b2
// 700
// #0e7490
// 800
// #155e75
// 900
// #164e63
// 950
// #083344
// Sky
// 50
// #f0f9ff
// 100
// #e0f2fe
// 200
// #bae6fd
// 300
// #7dd3fc
// 400
// #38bdf8
// 500
// #0ea5e9
// 600
// #0284c7
// 700
// #0369a1
// 800
// #075985
// 900
// #0c4a6e
// 950
// #082f49
// Blue
// 50
// #eff6ff
// 100
// #dbeafe
// 200
// #bfdbfe
// 300
// #93c5fd
// 400
// #60a5fa
// 500
// #3b82f6
// 600
// #2563eb
// 700
// #1d4ed8
// 800
// #1e40af
// 900
// #1e3a8a
// 950
// #172554
// Indigo
// 50
// #eef2ff
// 100
// #e0e7ff
// 200
// #c7d2fe
// 300
// #a5b4fc
// 400
// #818cf8
// 500
// #6366f1
// 600
// #4f46e5
// 700
// #4338ca
// 800
// #3730a3
// 900
// #312e81
// 950
// #1e1b4b
// Violet
// 50
// #f5f3ff
// 100
// #ede9fe
// 200
// #ddd6fe
// 300
// #c4b5fd
// 400
// #a78bfa
// 500
// #8b5cf6
// 600
// #7c3aed
// 700
// #6d28d9
// 800
// #5b21b6
// 900
// #4c1d95
// 950
// #2e1065
// Purple
// 50
// #faf5ff
// 100
// #f3e8ff
// 200
// #e9d5ff
// 300
// #d8b4fe
// 400
// #c084fc
// 500
// #a855f7
// 600
// #9333ea
// 700
// #7e22ce
// 800
// #6b21a8
// 900
// #581c87
// 950
// #3b0764
// Fuchsia
// 50
// #fdf4ff
// 100
// #fae8ff
// 200
// #f5d0fe
// 300
// #f0abfc
// 400
// #e879f9
// 500
// #d946ef
// 600
// #c026d3
// 700
// #a21caf
// 800
// #86198f
// 900
// #701a75
// 950
// #4a044e
// Pink
// 50
// #fdf2f8
// 100
// #fce7f3
// 200
// #fbcfe8
// 300
// #f9a8d4
// 400
// #f472b6
// 500
// #ec4899
// 600
// #db2777
// 700
// #be185d
// 800
// #9d174d
// 900
// #831843
// 950
// #500724
// Rose
// 50
// #fff1f2
// 100
// #ffe4e6
// 200
// #fecdd3
// 300
// #fda4af
// 400
// #fb7185
// 500
// #f43f5e
// 600
// #e11d48
// 700
// #be123c
// 800
// #9f1239
// 900
// #881337
// 950
// #4c0519

// // Use above colours to make text colours for each colour
// // e.g. text-red-500
// // e.g. text-rose-500

// .text-slate-50 {
//   color: #f0f4f8;
// }

// .text-slate-100 {
//   color: #d9e2ec;
// }

// .text-slate-200 {
//   color: #bcccdd;
// }

// .text-slate-300 {
//   color: #9fb3c8;
// }

// .text-slate-400 {
//   color: #829ab1;
// }

// .text-slate-500 {
//   color: #627d98;
// }

// .text-slate-600 {
//   color: #486581;
// }

// .text-slate-700 {
//   color: #334e68;
// }

// .text-slate-800 {
//   color: #243b53;
// }

// .text-slate-900 {
//   color: #102a43;
// }

// .text-slate-950 {
//   color: #000814;
// }

// .text-crimson-50 {
//   color: #fef2f2;
// }

// .text-crimson-100 {
//   color: #fee2e2;
// }

// .text-crimson-200 {
//   color: #fecaca;
// }

// .text-crimson-300 {
//   color: #fca5a5;
// }

// .text-crimson-400 {
//   color: #f87171;
// }

// .text-crimson-500 {
//   color: #ef4444;
// }

// .text-crimson-600 {
//   color: #dc2626;
// }

// .text-crimson-700 {
//   color: #b91c1c;
// }

// .text-crimson-800 {
//   color: #991b1b;
// }

// .text-crimson-900 {
//   color: #7f1d1d;
// }

// .text-crimson-950 {
//   color: #410b0b;
// }

// .text-red-50 {
//   color: #fef2f2;
// }

// .text-red-100 {
//   color: #fee2e2;
// }

// .text-red-200 {
//   color: #fecaca;
// }

// .text-red-300 {
//   color: #fca5a5;
// }

// .text-red-400 {
//   color: #f87171;
// }

// .text-red-500 {
//   color: #ef4444;
// }

// .text-red-600 {
//   color: #dc2626;
// }

// .text-red-700 {
//   color: #b91c1c;
// }

// .text-red-800 {
//   color: #991b1b;
// }

// .text-red-900 {
//   color: #7f1d1d;
// }

// .text-red-950 {
//   color: #410b0b;
// }

// .text-orange-50 {
//   color: #fff7ed;
// }

// .text-orange-100 {
//   color: #ffedd5;
// }

// .text-orange-200 {
//   color: #fed7aa;
// }

// .text-orange-300 {
//   color: #fdba74;
// }
