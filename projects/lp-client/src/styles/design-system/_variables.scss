// Design System Variables
// Core spacing system based on 8px grid

// Spacing System (8px base unit)
$spacing-unit: 8px;
$spacing-xs: $spacing-unit;       // 8px
$spacing-sm: $spacing-unit * 2;   // 16px
$spacing-md: $spacing-unit * 3;   // 24px
$spacing-lg: $spacing-unit * 4;   // 32px
$spacing-xl: $spacing-unit * 5;   // 40px
$spacing-2xl: $spacing-unit * 6;  // 48px
$spacing-3xl: $spacing-unit * 8;  // 64px

// Border Radius System
$radius-sm: 8px;   // Small elements (badges, chips)
$radius-md: 12px;  // Buttons, inputs
$radius-lg: 16px;  // Cards, modals
$radius-xl: 20px;  // Major containers
$radius-2xl: 24px; // Hero sections

// Breakpoints
$breakpoint-mobile: 0px;
$breakpoint-tablet: 768px;
$breakpoint-desktop: 1024px;
$breakpoint-wide: 1440px;

// Container Max Widths
$container-sm: 640px;
$container-md: 900px;
$container-lg: 1200px;
$container-xl: 1440px;

// Primary Colors
$color-primary: #FF6B35;
$color-primary-light: #FF8F66;
$color-primary-dark: #CC5429;

// Base Colors
$color-base: #0072BC;
$color-base-light: #0090E8;
$color-base-dark: #00569A;

// Neutral Colors
$color-gray-100: #F5F5F5;
$color-gray-200: #EEEEEE;
$color-gray-300: #E0E0E0;
$color-gray-400: #BDBDBD;
$color-gray-500: #9E9E9E;
$color-gray-600: #757575;
$color-gray-700: #616161;
$color-gray-800: #424242;
$color-gray-900: #212121;

// Semantic Colors
$color-success: #4CAF50;
$color-warning: #FFC107;
$color-danger: #F44336;
$color-info: #2196F3;

// Typography
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 30px;
$font-size-4xl: 36px;

// Line Heights
$line-height-tight: 1.2;
$line-height-base: 1.5;
$line-height-loose: 1.8;

// Font Weights
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Shadows
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.1);
$shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
$shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.12);

// Component Heights
$input-height: 48px;
$button-height: 44px;
$button-height-desktop: 48px;
$header-height: 56px;
$header-height-desktop: 64px;

// Z-index layers
$z-index-dropdown: 100;
$z-index-sticky: 200;
$z-index-fixed: 300;
$z-index-modal-backdrop: 400;
$z-index-modal: 500;
$z-index-popover: 600;
$z-index-tooltip: 700;
$z-index-toast: 800;