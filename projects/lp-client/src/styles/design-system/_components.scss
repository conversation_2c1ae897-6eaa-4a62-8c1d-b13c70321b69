@import './variables';
@import './mixins';

// Form Components
.ui-form-field {
  @include form-field;
  
  .ui-input-label {
    display: block;
    margin-bottom: $spacing-xs;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $color-gray-700;
  }
  
  .ui-input-helper {
    display: block;
    margin-top: $spacing-xs;
    font-size: $font-size-xs;
    color: $color-gray-600;
  }
  
  .ui-input-error {
    display: flex;
    align-items: center;
    gap: 4px;
    color: $color-danger;
    font-size: $font-size-xs;
    margin-top: 4px;
    padding: 0 $spacing-sm;
    
    ion-icon {
      font-size: 14px;
    }
  }
}

// Grid System
.ui-grid {
  display: grid;
  gap: $spacing-md;
  
  // Default: 1 column
  grid-template-columns: 1fr;
  
  @include tablet {
    gap: $spacing-lg;
    
    &.ui-grid-2 {
      grid-template-columns: repeat(2, 1fr);
    }
    
    &.ui-grid-3 {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  @include desktop {
    gap: $spacing-xl;
    
    &.ui-grid-2 {
      grid-template-columns: repeat(2, 1fr);
    }
    
    &.ui-grid-3 {
      grid-template-columns: repeat(3, 1fr);
    }
    
    &.ui-grid-4 {
      grid-template-columns: repeat(4, 1fr);
    }
  }
  
  // Span utilities
  .ui-col-span-2 {
    @include tablet {
      grid-column: span 2;
    }
  }
  
  .ui-col-span-3 {
    @include desktop {
      grid-column: span 3;
    }
  }
  
  .ui-col-span-full {
    grid-column: 1 / -1;
  }
}

// Section Headers
.ui-section {
  margin-bottom: $spacing-lg;
  
  @include tablet {
    margin-bottom: $spacing-xl;
  }
  
  @include desktop {
    margin-bottom: $spacing-2xl;
  }
  
  .ui-section-title {
    font-size: $font-size-xl;
    font-weight: $font-weight-semibold;
    color: $color-gray-900;
    margin: 0 0 $spacing-md 0;
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    
    @include tablet {
      font-size: $font-size-2xl;
      margin-bottom: $spacing-lg;
      gap: $spacing-md;
    }
    
    @include desktop {
      font-size: $font-size-3xl;
      margin-bottom: $spacing-xl;
    }
    
    ion-icon {
      font-size: 24px;
      color: $color-primary;
      
      @include tablet {
        font-size: 28px;
      }
      
      @include desktop {
        font-size: 32px;
      }
    }
  }
  
  .ui-section-subtitle {
    font-size: $font-size-base;
    color: $color-gray-600;
    margin-top: $spacing-xs;
    line-height: $line-height-base;
  }
}

// Dividers
.ui-divider {
  height: 1px;
  background: rgba(0, 0, 0, 0.08);
  margin: $spacing-md 0;
  
  @include tablet {
    margin: $spacing-lg 0;
  }
  
  @include desktop {
    margin: $spacing-xl 0;
  }
  
  &.ui-divider-thick {
    height: 2px;
  }
  
  &.ui-divider-dashed {
    background: none;
    border-top: 1px dashed rgba(0, 0, 0, 0.08);
  }
}

// Loading States
.ui-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  
  ion-spinner {
    --color: #{$color-primary};
  }
}

// Empty States
.ui-empty {
  text-align: center;
  padding: $spacing-2xl $spacing-lg;
  
  ion-icon {
    font-size: 64px;
    color: $color-gray-400;
    margin-bottom: $spacing-md;
  }
  
  .ui-empty-title {
    font-size: $font-size-xl;
    font-weight: $font-weight-semibold;
    color: $color-gray-700;
    margin-bottom: $spacing-sm;
  }
  
  .ui-empty-message {
    font-size: $font-size-base;
    color: $color-gray-600;
    margin-bottom: $spacing-lg;
  }
}

// Badges
.ui-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  border-radius: $radius-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-semibold;
  background: $color-gray-200;
  color: $color-gray-700;
  
  &.ui-badge-primary {
    background: $color-primary;
    color: white;
  }
  
  &.ui-badge-success {
    background: $color-success;
    color: white;
  }
  
  &.ui-badge-warning {
    background: $color-warning;
    color: $color-gray-900;
  }
  
  &.ui-badge-danger {
    background: $color-danger;
    color: white;
  }
  
  &.ui-badge-info {
    background: $color-info;
    color: white;
  }
}