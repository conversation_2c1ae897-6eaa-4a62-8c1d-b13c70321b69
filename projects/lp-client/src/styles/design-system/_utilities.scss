@import './variables';
@import './mixins';

// Grid System
.ui-grid {
  display: grid;
  gap: $spacing-md;
  
  @include tablet {
    gap: $spacing-lg;
  }
  
  &.ui-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  &.ui-grid-3 {
    grid-template-columns: repeat(3, 1fr);
    
    @media (max-width: #{$breakpoint-tablet - 1px}) {
      grid-template-columns: 1fr;
    }
  }
  
  &.ui-grid-4 {
    grid-template-columns: repeat(4, 1fr);
    
    @media (max-width: #{$breakpoint-desktop - 1px}) {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @media (max-width: #{$breakpoint-tablet - 1px}) {
      grid-template-columns: 1fr;
    }
  }
}

// Dividers
.ui-divider {
  height: 1px;
  background: $color-gray-200;
  margin: $spacing-md 0;
  
  @include tablet {
    margin: $spacing-lg 0;
  }
}

// Text Utilities
.ui-text-h1,
.ui-text-h2,
.ui-text-h3 {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  margin-bottom: $spacing-md;
  
  ion-icon {
    font-size: 1.2em;
    color: $color-primary;
  }
}

.ui-text-h1 {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: $color-gray-900;
  
  @include tablet {
    font-size: $font-size-3xl;
  }
}

.ui-text-h2 {
  font-size: $font-size-xl;
  font-weight: $font-weight-semibold;
  color: $color-gray-800;
  
  @include tablet {
    font-size: $font-size-2xl;
  }
}

.ui-text-h3 {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $color-gray-700;
  
  @include tablet {
    font-size: $font-size-xl;
  }
}

// Spacing Utilities
.ui-mb-sm { margin-bottom: $spacing-sm !important; }
.ui-mb-md { margin-bottom: $spacing-md !important; }
.ui-mb-lg { margin-bottom: $spacing-lg !important; }
.ui-mb-xl { margin-bottom: $spacing-xl !important; }

.ui-mt-sm { margin-top: $spacing-sm !important; }
.ui-mt-md { margin-top: $spacing-md !important; }
.ui-mt-lg { margin-top: $spacing-lg !important; }
.ui-mt-xl { margin-top: $spacing-xl !important; }

.ui-p-sm { padding: $spacing-sm !important; }
.ui-p-md { padding: $spacing-md !important; }
.ui-p-lg { padding: $spacing-lg !important; }
.ui-p-xl { padding: $spacing-xl !important; }

// Color Utilities
.ui-text-primary { color: $color-primary !important; }
.ui-text-success { color: $color-success !important; }
.ui-text-danger { color: $color-danger !important; }
.ui-text-warning { color: $color-warning !important; }

.ui-bg-primary { background-color: $color-primary !important; }
.ui-bg-success { background-color: $color-success !important; }
.ui-bg-danger { background-color: $color-danger !important; }
.ui-bg-warning { background-color: $color-warning !important; }