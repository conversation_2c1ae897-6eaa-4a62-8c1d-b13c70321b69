// Design System Mixins
@import './variables';

// Responsive breakpoint mixins
@mixin tablet {
  @media (min-width: #{$breakpoint-tablet}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-desktop}) {
    @content;
  }
}

@mixin wide {
  @media (min-width: #{$breakpoint-wide}) {
    @content;
  }
}

// Container mixin
@mixin container($max-width: $container-lg) {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  max-width: $max-width;
}

// Card mixin
@mixin card($padding: $spacing-md, $radius: $radius-lg) {
  background: white;
  border-radius: $radius;
  padding: $padding;
  box-shadow: $shadow-md;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  @include tablet {
    padding: $spacing-lg;
    border-radius: $radius-xl;
  }
  
  @include desktop {
    padding: $spacing-xl;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-lg;
    }
  }
}

// Button base mixin
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  min-height: $button-height;
  border-radius: $radius-md;
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  outline: none;
  text-decoration: none;
  
  @include desktop {
    min-height: $button-height-desktop;
    padding: $spacing-sm $spacing-lg;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }
}

// Form field mixin
@mixin form-field {
  margin-bottom: $spacing-md;
  
  ion-item {
    --background: white;
    --border-radius: #{$radius-md};
    --border-color: rgba(0, 0, 0, 0.12);
    --border-width: 1px;
    --padding-start: #{$spacing-sm};
    --padding-end: #{$spacing-sm};
    --min-height: #{$input-height};
    
    &.item-has-focus {
      --border-color: var(--ion-color-primary);
      --border-width: 2px;
    }
    
    &.ion-invalid.ion-touched {
      --border-color: var(--ion-color-danger);
    }
  }
}

// Page padding mixin
@mixin page-padding {
  padding: $spacing-sm;
  
  @include tablet {
    padding: $spacing-md $spacing-lg;
  }
  
  @include desktop {
    padding: $spacing-lg $spacing-xl;
  }
  
  @include wide {
    padding: $spacing-xl $spacing-3xl;
  }
}

// Focus visible mixin
@mixin focus-visible {
  &:focus-visible {
    outline: 2px solid var(--ion-color-primary);
    outline-offset: 2px;
  }
}

// Truncate text mixin
@mixin truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Line clamp mixin
@mixin line-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// Smooth scroll mixin
@mixin smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

// Aspect ratio mixin
@mixin aspect-ratio($width: 16, $height: 9) {
  position: relative;
  
  &::before {
    content: '';
    display: block;
    padding-top: percentage($height / $width);
  }
  
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}