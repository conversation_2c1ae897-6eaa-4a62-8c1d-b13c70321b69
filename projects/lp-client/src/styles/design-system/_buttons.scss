@import './variables';
@import './mixins';

// Base button styles
.ui-btn {
  @include button-base;
  
  // Primary variant (default)
  &.ui-btn-primary {
    background: var(--ion-color-primary, $color-primary);
    color: white;
    
    &:hover:not(:disabled) {
      background: var(--ion-color-primary-shade, $color-primary-dark);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    }
  }
  
  // Secondary variant
  &.ui-btn-secondary {
    background: transparent;
    color: var(--ion-color-primary, $color-primary);
    border: 2px solid var(--ion-color-primary, $color-primary);
    
    &:hover:not(:disabled) {
      background: var(--ion-color-primary, $color-primary);
      color: white;
      transform: translateY(-1px);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
    }
  }
  
  // Danger variant
  &.ui-btn-danger {
    background: $color-danger;
    color: white;
    
    &:hover:not(:disabled) {
      background: darken($color-danger, 10%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba($color-danger, 0.3);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba($color-danger, 0.3);
    }
  }
  
  // Success variant
  &.ui-btn-success {
    background: $color-success;
    color: white;
    
    &:hover:not(:disabled) {
      background: darken($color-success, 10%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba($color-success, 0.3);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba($color-success, 0.3);
    }
  }
  
  // Size variants
  &.ui-btn-sm {
    min-height: 36px;
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
  }
  
  &.ui-btn-lg {
    min-height: 56px;
    padding: $spacing-md $spacing-xl;
    font-size: $font-size-lg;
    
    @include desktop {
      min-height: 60px;
    }
  }
  
  // Block variant
  &.ui-btn-block {
    width: 100%;
  }
  
  // Focus state
  @include focus-visible;
}

// Submit button helper class
.ui-btn-submit {
  @extend .ui-btn;
  @extend .ui-btn-primary;
  @extend .ui-btn-block;
  margin-top: $spacing-md;
}

// Icon button
.ui-btn-icon {
  @extend .ui-btn;
  padding: $spacing-sm;
  min-width: $button-height;
  
  @include desktop {
    min-width: $button-height-desktop;
  }
  
  ion-icon {
    font-size: 20px;
  }
}

// Button group
.ui-btn-group {
  display: flex;
  gap: $spacing-sm;
  
  &.ui-btn-group-vertical {
    flex-direction: column;
  }
  
  &.ui-btn-group-justified {
    .ui-btn {
      flex: 1;
    }
  }
}