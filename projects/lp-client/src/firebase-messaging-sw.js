// Firebase Cloud Messaging Service Worker

// Demo Firebase configuration - replace with actual config in production
importScripts('https://www.gstatic.com/firebasejs/11.7.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/11.7.1/firebase-messaging-compat.js');

// Helper functions for handling body as string or string[]
function isArray(body) {
  return Array.isArray(body);
}

function renderBody(body) {
  if (!body) return '';
  if (Array.isArray(body)) {
    return body.join('\n');
  }
  return body;
}

let messaging;

try {
  firebase.initializeApp({
    apiKey: "AIzaSyAseqL6KTURHxDYvK7Wygl0ViRIDS1OM3c",
    authDomain: "loyalty-test-project-58bcb.firebaseapp.com",
    projectId: "loyalty-test-project-58bcb",
    storageBucket: "loyalty-test-project-58bcb.firebasestorage.app",
    messagingSenderId: "798238467180",
    appId: "1:798238467180:web:ddab11e43f82af1de86449",
    measurementId: "G-8CCCGX5VTK",
    vapidKey: "BE9BCB1mD6jOEL5wGM46BK4eVsVOTtuq4YmKEACpDqXJymeXQG7O2d8QBpN0NE9FBzsrZ3FnySi7unGKbqdQ1yQ"
  });

  messaging = firebase.messaging();
  console.log('🔥 SW: Firebase initialized successfully');
} catch (error) {
  console.error('🔥 SW: Firebase initialization error:', error);
}

// Handle background messages with topic information
if (messaging) {
  messaging.onBackgroundMessage((payload) => {
    console.log('🔥 SW: Received background message with topic:', payload);
  console.log('🔥 SW: About to post message to Angular app');
  
  const notificationTitle = payload.notification?.title || 'New Notification';
  const notificationOptions = {
    body: renderBody(payload.notification?.body || payload.data?.body || 'You have a new message'),
    icon: '/assets/icons/icon-192x192.png',
    badge: '/assets/icons/badge-72x72.png',
    tag: payload.data?.topic || 'general', // Use topic as tag
    data: payload.data
  };

  // Show browser notification
  self.registration.showNotification(notificationTitle, notificationOptions);
  
  // Also notify the Angular app if it's active
  // This ensures Firebase Console notifications appear in the app
  console.log('🔥 SW: Looking for active clients...');
  self.clients.matchAll({ includeUncontrolled: true, type: 'window' }).then(clients => {
    console.log('🔥 SW: Found', clients.length, 'clients');
    clients.forEach(client => {
      console.log('🔥 SW: Client URL:', client.url);
      if (client.url.includes(self.location.origin)) {
        console.log('🔥 SW: Posting message to client:', client.url);
        client.postMessage({
          type: 'FIREBASE_NOTIFICATION',
          payload: payload
        });
        console.log('🔥 SW: Message posted successfully');
      }
    });
  });
  });
} else {
  console.error('🔥 SW: Firebase messaging not available');
}

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);
  
  event.notification.close();
  
  // This looks to see if the current is already open and focuses if it is
  event.waitUntil(
    clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    })
    .then((clientList) => {
      // If a tab is already open, focus it
      for (const client of clientList) {
        if (client.url.includes('/notifications') && 'focus' in client) {
          return client.focus();
        }
      }
      
      // If no tab is open, open a new one
      if (clients.openWindow) {
        let url = '/notifications';
        
        // If we have specific data, we can navigate to a specific page
        if (event.notification.data && event.notification.data.type) {
          if (event.notification.data.type === 'message') {
            url = `/messages/${event.notification.data.id}`;
          } else if (event.notification.data.type === 'promotion') {
            url = `/promotions/${event.notification.data.id}`;
          }
        }
        
        return clients.openWindow(url);
      }
    })
  );
});