// Type definitions for Google Maps Advanced Marker API
// These extend the existing @types/google.maps definitions

declare namespace google.maps.marker {
  /**
   * PinElement class for creating custom pin elements
   */
  export class PinElement {
    constructor(options?: PinElementOptions);
    element: HTMLElement;
    glyph?: string | Element | URL | null;
    glyphColor?: string;
    background?: string;
    borderColor?: string;
    scale?: number;
  }

  /**
   * Options for creating a PinElement
   */
  export interface PinElementOptions {
    glyph?: string | Element | URL | null;
    glyphColor?: string;
    background?: string;
    borderColor?: string;
    scale?: number;
  }

  /**
   * AdvancedMarkerElement class for creating advanced markers
   */
  export class AdvancedMarkerElement {
    constructor(options?: AdvancedMarkerElementOptions);
    position: google.maps.LatLng | google.maps.LatLngLiteral | null;
    title?: string | null;
    map: google.maps.Map | null;
    content?: Node | PinElement | null;
    gmpDraggable?: boolean;
    collisionBehavior?: CollisionBehavior;
    zIndex?: number | null;
    
    addListener(eventName: string, handler: Function): google.maps.MapsEventListener;
  }

  /**
   * Options for creating an AdvancedMarkerElement
   */
  export interface AdvancedMarkerElementOptions {
    position?: google.maps.LatLng | google.maps.LatLngLiteral | null;
    map?: google.maps.Map | null;
    content?: Node | PinElement | null;
    title?: string | null;
    collisionBehavior?: CollisionBehavior;
    gmpDraggable?: boolean;
    zIndex?: number | null;
  }

  /**
   * Collision behavior options for advanced markers
   */
  export enum CollisionBehavior {
    OPTIONAL_AND_HIDES_LOWER_PRIORITY = 'OPTIONAL_AND_HIDES_LOWER_PRIORITY',
    REQUIRED = 'REQUIRED',
    REQUIRED_AND_HIDES_OPTIONAL = 'REQUIRED_AND_HIDES_OPTIONAL'
  }
}