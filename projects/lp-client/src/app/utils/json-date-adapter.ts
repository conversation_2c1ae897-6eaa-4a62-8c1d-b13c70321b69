import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class JsonDateAdapter {
  /**
   * Safely parse date strings from the API
   * Handles both ISO strings and Java date formats
   */
  parse(value: any): Date | null {
    if (!value) return null;
    
    // Handle string dates
    if (typeof value === 'string') {
      return new Date(value);
    }
    
    // Handle numeric timestamps (Java often sends these)
    if (typeof value === 'number') {
      return new Date(value);
    }
    
    // If it's already a Date object
    if (value instanceof Date) {
      return value;
    }
    
    return null;
  }
  
  /**
   * Format a date to ISO string for sending to API
   */
  format(date: Date | null): string | null {
    return date ? date.toISOString() : null;
  }
}