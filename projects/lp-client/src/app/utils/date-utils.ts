import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DateUtils {
  /**
   * Safely parse any date format from API
   */
  parseApiDate(dateValue: any): Date | null {
    if (!dateValue) return null;
    
    try {
      // Handle various formats
      if (typeof dateValue === 'string') {
        return new Date(dateValue);
      } else if (typeof dateValue === 'number') {
        return new Date(dateValue);
      } else if (dateValue instanceof Date) {
        return dateValue;
      }
      return null;
    } catch (error) {
      console.error('Date parsing error:', error);
      return null;
    }
  }
  
  /**
   * Format date for display
   */
  formatForDisplay(date: Date | string | null): string {
    if (!date) return '';
    
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return dateObj.toLocaleDateString();
    } catch (error) {
      console.error('Date formatting error:', error);
      return '';
    }
  }
  
  /**
   * Format date for API
   */
  formatForApi(date: Date | string | null): string | null {
    if (!date) return null;
    
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return dateObj.toISOString();
    } catch (error) {
      console.error('Date API formatting error:', error);
      return null;
    }
  }
}