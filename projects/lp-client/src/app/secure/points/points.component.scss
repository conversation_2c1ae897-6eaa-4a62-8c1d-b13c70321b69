.app-background {
  --background: var(--ion-color-base);
}

/* Points Container for Better Spacing */
.points-container {
  min-height: 100%;
  padding: 0;
  
  @media (min-width: 768px) {
    padding: 0 40px;
  }
  
  @media (min-width: 1024px) {
    padding: 0 40px;
  }
  
  @media (min-width: 1200px) {
    padding: 0 60px;
    max-width: 1600px;
    margin: 0 auto;
  }
}

//   .card {
//     --background: var(--ion-color-primary-shade);
//     color: var(--ion-color-primary-contrast)
//   }
.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.account-name {
  font-size: 26px
}

.name-text {
  margin-left: 20px;
  font-size: 26px

}

.w-full {
  width: 100%;
}
.card-background {
  background-color: #fff;
  width: 100vw;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.transaction-summary {
  ion-row {
      h3 {
          margin-bottom: 1rem;
          margin-left: 16px;
          font-size: 1.4rem;
          font-weight: 400;
      }
  }
  
  ion-col {
      ion-row {
          h3 {
              font-size: 0.8rem;
              font-weight: 400;
              width: 100%;
              margin-right: 16px;

              span {
                  font-size: 1rem;
                  padding-bottom: 0;
              }
          }
      }
  }
  
  h3 {
      margin-bottom: 1rem;
      margin-left: 16px;

      span {
          font-size: 1rem;
          display: block;
          padding-bottom: 12px;
      }
  }
}

.pcu-earned {
  color: var(--ion-color-success) !important;
}

.pcu-spent {
  color: var(--ion-color-danger) !important;
}

.points-item {
  cursor: pointer;
  position: relative;
  
  &::after {
      content: '';
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-top: 2px solid var(--ion-color-medium);
      border-right: 2px solid var(--ion-color-medium);
      transform: translateY(-50%) rotate(45deg);
  }
  
  &:hover {
      background-color: rgba(var(--ion-color-primary-rgb), 0.1);
  }
}

ion-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  margin: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  
  &.points-summary-card,
  &.points-transfer-container {
    --padding-start: 24px;
    --padding-end: 24px;
    --padding-top: 24px;
    --padding-bottom: 24px;
  }
  
  ion-item {
    --background: transparent;
    --background-focused: transparent;
    --background-hover: transparent;
    --background-activated: transparent;
    --border-color: transparent;
    --inner-border-width: 0;
    --border-width: 0;
    
    &:last-child {
      --border-width: 0;
    }
  }
}

.points-transfer-container {
  padding: 16px;
  border-bottom: 1px solid var(--ion-color-medium);
}