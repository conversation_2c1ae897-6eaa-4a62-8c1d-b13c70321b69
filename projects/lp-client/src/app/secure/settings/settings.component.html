<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
    <div>
        <div class="ion-padding avatar">
            <img src="./assets/images/avatar.png" alt="avatar" />
        </div>
        <div class="ion-text-center">
            <h2 class="ion-no-margin"><PERSON></h2>
            <p class="ion-no-margin">alan</p>
            <p class="ion-no-margin">123456789</p>
        </div>
        <ion-list>
            <ion-list-header>
                <ion-label>Settings</ion-label>
            </ion-list-header>
            <ion-item routerLink="/secure/profile">
                <ion-avatar slot="start">
                    <ion-icon name="person-outline"></ion-icon>
                </ion-avatar>
                <ion-label>
                    <h2>Profile</h2>
                </ion-label>
            </ion-item>
            <ion-item routerLink="/public/password">
                <ion-avatar slot="start" style="background: var(--ion-color-warning);">
                    <ion-icon name="lock-closed-outline"></ion-icon>
                </ion-avatar>
                <ion-label>
                    <h2>Password</h2>
                </ion-label>
            </ion-item>
            <ion-item>
                <ion-avatar slot="start" style="background: #97C66B;">
                    <ion-icon name="shield-outline"></ion-icon>
                </ion-avatar>
                <ion-label>
                    <h2>2-Step Verification</h2>
                </ion-label>
            </ion-item>
            <ion-item routerLink="/secure/notification-settings">
                <ion-avatar slot="start" style="background: var(--ion-color-primary);">
                    <ion-icon name="notifications-outline"></ion-icon>
                </ion-avatar>
                <ion-label>
                    <h2>Notification Settings</h2>
                </ion-label>
            </ion-item>
            <ion-item>
                <ion-avatar slot="start" style="background: #AC85D4;">
                    <ion-icon name="laptop-outline"></ion-icon>
                </ion-avatar>
                <ion-label>
                    <h2>Device History</h2>
                </ion-label>
            </ion-item>
        </ion-list>

        <ion-list>
            <ion-list-header>
                <ion-label>Social</ion-label>
            </ion-list-header>
            <ion-item>
                <ion-avatar slot="start" style="background: #567aa3;">
                    <ion-icon name="logo-facebook"></ion-icon>
                </ion-avatar>
                <ion-label>
                    <h2>Facebook</h2>
                </ion-label>
            </ion-item>
            <ion-item>
                <ion-avatar slot="start" style="background: #48cff5;">
                    <ion-icon name="logo-twitter"></ion-icon>
                </ion-avatar>
                <ion-label>
                    <h2>Twitter</h2>
                </ion-label>
            </ion-item>
            <ion-item>
                <ion-avatar slot="start" style="background: #0077B5;">
                    <ion-icon name="logo-linkedin"></ion-icon>
                </ion-avatar>
                <ion-label>
                    <h2>LinkedIn</h2>
                </ion-label>
            </ion-item>
        </ion-list>

        <ion-list>
            <ion-list-header>
                <ion-label>Help and Support</ion-label>
            </ion-list-header>
            <ion-item routerLink="/secure/contactus">
                <ion-avatar slot="start" style="background: #97C66B;">
                    <ion-icon name="call-outline"></ion-icon>
                </ion-avatar>
                <ion-label>
                    <h2>Contact Us</h2>
                </ion-label>
            </ion-item>
            <ion-item>
                <ion-avatar slot="start" style="background: var(--ion-color-warning);">
                    <ion-icon name="document-outline"></ion-icon>
                </ion-avatar>
                <ion-label>
                    <h2>Terms and Conditions</h2>
                </ion-label>
            </ion-item>
        </ion-list>
    </div>
</lib-page-wrapper>