import { Component, Injector, OnInit, ChangeDetectorRef } from '@angular/core';
import {
  MemberProfile,
  MemberService,
  KeyCloakService,
  LssConfig,
} from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-dashboard',
  templateUrl: 'dashboard.component.html',
  styleUrls: ['dashboard.component.scss'],
})
export class DashboardComponent extends AbstractComponent {
  profile?: MemberProfile;

  environment = environment;
  
  // Terminology helper
  get terminology() {
    return this.lssConfig.terminology?.pool || {
      singular: 'Pool',
      plural: 'Pools'
    };
  }

  constructor(
    injector: Injector,
    private memberService: MemberService,
    private kc: KeyCloakService,
    private cd: ChangeDetectorRef,
    public lssConfig: LssConfig
  ) {
    super(injector);
  }

  ngOnInit() {
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        if (this.profile) this.getBalance();
        this.detectChanges();
      })
    );
  }

  getBalance() {
    this.memberService
      .memberBalance(this.kc.lpUniueReference)
      .subscribe((data: any) => {
        this.profile = { ...this.profile, ...data };
      });
  }

  // Format currency with thousand separators
  formatCurrency(value: number | undefined | null): string {
    if (!value && value !== 0) return '0.00';
    
    // Format with 2 decimal places and thousand separators
    return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  // Format number with thousand separators
  formatNumber(value: number | undefined | null): string {
    if (!value && value !== 0) return '0';
    
    // Format with thousand separators
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  // Calculate dynamic font size based on value length
  calculateFontSize(value: string): string {
    const length = value.length;
    let fontSize = 20; // Default size
    
    if (length <= 5) {
      fontSize = 24;
    } else if (length <= 8) {
      fontSize = 20;
    } else if (length <= 10) {
      fontSize = 18;
    } else if (length <= 12) {
      fontSize = 16;
    } else {
      fontSize = 14;
    }
    
    return `${fontSize}px`;
  }
}
