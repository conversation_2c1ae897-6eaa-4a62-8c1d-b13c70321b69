<ion-content class="app-background">
  <!-- Modern Header -->
  <lib-head-logo
    [names]="profile?.givenNames + ' ' + profile?.surname"
    [membership]="profile?.newMembershipNumber"
    type="welcome"
    [balance]="profile?.currentBalance"
    [src]="lssConfig.pages.landing.loggedinIcon"
  />
  
  <!-- Single Balance Card (same as home screen) -->
  <div class="balance-section">
    <div class="balance-card">
      <div class="balance-item rand-value">
        <div class="balance-header">
          <p class="balance-label">RAND VALUE</p>
          <div class="balance-icon">
            <ion-icon name="cash-outline"></ion-icon>
          </div>
        </div>
        <p class="balance-value" [style.font-size]="calculateFontSize('R ' + formatCurrency(profile?.availRands))">R {{ formatCurrency(profile?.availRands) }}</p>
      </div>
      
      <div class="balance-divider"></div>
      
      <div class="balance-item points">
        <div class="balance-header">
          <p class="balance-label">POINTS</p>
          <div class="balance-icon">
            <ion-icon name="star-outline"></ion-icon>
          </div>
        </div>
        <p class="balance-value points-value" [style.font-size]="calculateFontSize(formatNumber(profile?.currentBalance))">{{ formatNumber(profile?.currentBalance) }}</p>
      </div>
    </div>
  </div>

  <!-- Modern Action Buttons -->
  <div class="modern-actions">
    <h2 class="actions-title">Quick Actions</h2>
    <div class="modern-grid">
      <!-- Profile -->
      <a [routerLink]="['/secure/profile']" class="modern-action-card profile">
        <div class="action-decoration"></div>
        <div class="action-icon">
          <ion-icon name="person-outline"></ion-icon>
        </div>
        <p class="action-label">Profile</p>
      </a>
      
      <!-- Security -->
      <a [routerLink]="['/secure/security']" class="modern-action-card security">
        <div class="action-decoration"></div>
        <div class="action-icon">
          <ion-icon name="shield-checkmark-outline"></ion-icon>
        </div>
        <p class="action-label">Security</p>
      </a>
      
      <!-- Pools -->
      <a [routerLink]="['/secure/pools']" class="modern-action-card pools">
        <div class="action-decoration"></div>
        <div class="action-icon">
          <ion-icon name="layers-outline"></ion-icon>
        </div>
        <p class="action-label">Pools</p>
      </a>
      
      <!-- Points -->
      <a [routerLink]="['/secure/points']" class="modern-action-card points-btn">
        <div class="action-decoration"></div>
        <div class="action-icon">
          <ion-icon name="trophy-outline"></ion-icon>
        </div>
        <p class="action-label">Points</p>
      </a>
    </div>
  </div>

  <!-- Points Summary Section -->
  <div class="points-summary">
    <h2 class="section-title">Points Summary</h2>
    <div class="summary-grid">
      <!-- Available Balance -->
      <div class="summary-card">
        <div class="summary-icon-wrapper">
          <ion-icon name="wallet-outline"></ion-icon>
        </div>
        <div class="summary-content">
          <p class="summary-label">Available Balance</p>
          <p class="summary-value">{{ formatNumber(profile?.availUnits) }}</p>
        </div>
      </div>
      
      <!-- Current Points -->
      <div class="summary-card clickable" [routerLink]="['/secure/points']">
        <div class="summary-icon-wrapper primary">
          <ion-icon name="star"></ion-icon>
        </div>
        <div class="summary-content">
          <p class="summary-label">{{ lssConfig.pointsTitle || 'Points' }}</p>
          <p class="summary-value primary">{{ formatNumber(profile?.currentBalance) }}</p>
        </div>
        <ion-icon name="chevron-forward-outline" class="nav-arrow"></ion-icon>
      </div>
      
      <!-- Earned Points -->
      <div class="summary-card clickable" [routerLink]="['/secure/points']">
        <div class="summary-icon-wrapper success">
          <ion-icon name="trending-up-outline"></ion-icon>
        </div>
        <div class="summary-content">
          <p class="summary-label">Earned</p>
          <p class="summary-value success">+{{ formatNumber((profile?.baseMiles || 0) + (profile?.bonusMiles || 0)) }}</p>
        </div>
        <ion-icon name="chevron-forward-outline" class="nav-arrow"></ion-icon>
      </div>
      
      <!-- Used Points -->
      <div class="summary-card">
        <div class="summary-icon-wrapper danger">
          <ion-icon name="trending-down-outline"></ion-icon>
        </div>
        <div class="summary-content">
          <p class="summary-label">Used</p>
          <p class="summary-value danger">-{{ formatNumber((profile?.expiredMiles || 0) + (profile?.usedMiles || 0)) }}</p>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Danger Zone -->
  <div class="danger-zone">
    <a [routerLink]="['/secure/profileremove']" class="danger-action">
      <ion-icon name="trash-outline"></ion-icon>
      <span>Remove Profile</span>
      <ion-icon name="chevron-forward-outline" class="arrow"></ion-icon>
    </a>
  </div>
</ion-content>