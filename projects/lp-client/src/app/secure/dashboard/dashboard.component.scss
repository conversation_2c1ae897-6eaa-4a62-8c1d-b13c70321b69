/* Dashboard Components */

.dashboard-content {
  width: 100%;
  
  @media (min-width: 768px) {
    max-width: 900px;
    margin: 0 auto;
  }
  
  @media (min-width: 1024px) {
    max-width: 1200px;
  }
  
  @media (min-width: 1400px) {
    max-width: 1400px;
  }
  
  // Ensure all rows are visible
  ion-row {
    display: flex !important;
    flex-wrap: wrap;
    width: 100%;
  }
}


/* Enhanced Dashboard Cards */
.card {
  margin: 8px 0;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

// Orange background for dashboard action cards
.dashboard-action-card {
  background: linear-gradient(135deg, var(--ion-color-primary, #FF6B35) 0%, #ff8659 100%) !important;
  border: 1px solid rgba(255, 107, 53, 0.4) !important;
  
  .quick-action {
    ion-icon {
      color: white !important;
    }
    
    ion-card-subtitle {
      color: white !important;
    }
  }
  
  // Subtle gradient overlay
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
  }
  
  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: rgba(var(--ion-color-primary-rgb), 0.3);
    
    &::before {
      opacity: 1;
    }
  }
}

// Hover state for dashboard action cards
.dashboard-action-card:hover {
  background: linear-gradient(135deg, #ff8659 0%, var(--ion-color-primary, #FF6B35) 100%) !important;
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3) !important;
  transform: translateY(-4px) scale(1.02);
  
  .quick-action {
    ion-icon {
      transform: scale(1.2) rotate(5deg);
      color: white !important;
      filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
    }
    
    ion-card-subtitle {
      color: white !important;
      font-weight: 600;
    }
  }
  
  &:active {
    transform: translateY(-2px) scale(0.98);
    transition: all 0.1s ease;
  }
}

.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  ion-icon {
    font-size: 28px;
    margin-bottom: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: white;
  }
  
  ion-card-subtitle {
    font-size: 14px;
    font-weight: 500;
    color: white;
    transition: all 0.3s ease;
    text-align: center;
  }
}

  .account-name {
    font-size: 26px
  }

  .name-text {
    margin-left: 20px;
    font-size: 26px

  }

  .w-full {
    width: 100%;
  }

// Fix table text cutoff and alignment
ion-card:not([routerLink]) {
  overflow: visible;
  
  ion-item {
    --padding-start: 16px;
    --padding-end: 16px;
    --inner-padding-start: 0;
    --inner-padding-end: 0;
    
    @media (min-width: 768px) {
      --padding-start: 24px;
      --padding-end: 24px;
    }
    
    ion-row.w-full {
      width: 100%;
      margin: 0;
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      @media (min-width: 768px) {
        padding: 0 24px;
      }
      
      ion-card-subtitle {
        margin: 0;
        padding: 0;
        flex: 1 1 auto;
        text-align: left;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      ion-text {
        margin: 0 0 0 16px;
        padding: 0;
        flex: 0 0 auto;
        text-align: right;
        white-space: nowrap;
        font-weight: 600;
      }
    }
  }
}
  .card-background {
    background-color: rgba(255, 255, 255, 0.95);
    width: 100vw;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}
.transaction-summary {
    ion-row {
        h3 {
            margin-bottom: 1rem;
            margin-left: 16px;
            font-size: 1.4rem;
            font-weight: 400;
        }
    }
    
    ion-col {
        ion-row {
            h3 {
                font-size: 0.8rem;
                font-weight: 400;
                width: 100%;
                margin-right: 16px;

                span {
                    font-size: 1rem;
                    padding-bottom: 0;
                }
            }
        }
    }
    
    h3 {
        margin-bottom: 1rem;
        margin-left: 16px;

        span {
            font-size: 1rem;
            display: block;
            padding-bottom: 12px;
        }
    }
}

.pcu-earned {
    color: var(--ion-color-success) !important;
}

.pcu-spent {
    color: var(--ion-color-danger) !important;
}

/* Balance Summary Card - Allow natural height */
.balance-summary-card {
  height: auto !important;
  min-height: auto !important;
  background: rgba(255, 255, 255, 0.95) !important;
  
  .balance-content {
    padding: 16px 0;
    background: transparent !important;
  }
  
  .balance-list {
    padding: 0;
    margin: 0;
    background: transparent !important;
  }
  
  ion-item {
    --padding-start: 16px;
    --padding-end: 16px;
    --min-height: 48px;
    --background: transparent;
    
    @media (min-width: 768px) {
      --padding-start: 24px;
      --padding-end: 24px;
      --min-height: 56px;
    }
  }
}

/* Enhanced Points Section */
.points-item {
    cursor: pointer;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 8px;
    margin: 2px 0;
    
    &::after {
        content: '';
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%) rotate(45deg);
        width: 8px;
        height: 8px;
        border-top: 2px solid var(--ion-color-medium);
        border-right: 2px solid var(--ion-color-medium);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    &:hover {
        background-color: rgba(0, 0, 0, 0.05);
        transform: translateX(4px);
        padding-left: 20px;
        
        &::after {
            transform: translateY(-50%) rotate(45deg) scale(1.2);
            border-color: var(--ion-color-primary);
            right: 12px;
        }
        
        ion-card-subtitle {
            color: var(--ion-color-primary);
            font-weight: 600;
        }
        
        ion-text {
            color: var(--ion-color-primary);
            font-weight: 600;
        }
    }
    
    &:active {
        transform: translateX(2px) scale(0.98);
        transition: all 0.1s ease;
    }
    
    ion-row {
        align-items: center;
        
        ion-card-subtitle {
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        ion-text {
            transition: all 0.3s ease;
            font-weight: 500;
        }
    }
}

/* Desktop Responsive Grid Improvements */
@media (min-width: 768px) {
  // Reset ion-content padding since we're using container
  ion-content {
    padding: 0;
    
    // Ensure full height
    --offset-top: 0;
  }
  
  // Grid container for action cards
  .dashboard-content {
    // Force proper grid layout for the action cards
    ion-row {
      display: flex !important;
      flex-wrap: wrap !important;
      margin: 0 -8px;
      
      // Ensure all ion-col elements take 50% width on tablets
      ion-col {
        flex: 0 0 50% !important;
        max-width: 50% !important;
        padding: 0 8px;
      }
    }
  }
  
  // Enhanced card styles
  .card {
    height: 120px;
    margin: 12px 0;
    
    .quick-action {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 20px;
      
      ion-row {
        margin: 0;
        flex-direction: column;
        align-items: center;
        height: 100%;
        
        .center {
          margin-bottom: 8px;
          
          ion-icon {
            font-size: 32px;
            margin-bottom: 0;
          }
        }
        
        .center:last-child {
          margin-bottom: 0;
          
          ion-card-subtitle {
            font-size: 15px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
  
  // Balance/Points summary card optimizations
  .card:not([routerLink]) {
    margin: 20px 0;
    border-radius: 16px;
    overflow: visible;
    
    ion-item {
      --padding-start: 24px;
      --padding-end: 24px;
      --min-height: 60px;
      --inner-padding-end: 24px;
      
      ion-row {
        align-items: center;
        width: 100%;
        padding: 0;
        
        ion-card-subtitle {
          font-size: 16px;
          font-weight: 500;
          margin: 0;
          padding: 0;
          flex: 1;
          text-align: left;
        }
        
        ion-text {
          font-size: 18px;
          font-weight: 600;
          text-align: right;
          margin-left: auto;
        }
      }
    }
  }
}

/* Large Desktop Optimizations */
@media (min-width: 1024px) {
  ion-content {
    padding: 0;
  }
  
  // Force 3-column layout for action cards
  .dashboard-content {
    ion-row {
      display: flex !important;
      flex-wrap: wrap !important;
      width: 100%;
      margin: 0 -8px;
      
      ion-col {
        flex: 0 0 calc(33.333333% - 0px) !important;
        max-width: calc(33.333333% - 0px) !important;
        min-width: 0 !important;
        padding: 0 8px;
        
        // Force all columns to be exactly 1/3 width
        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3) {
          flex: 0 0 33.333333% !important;
          max-width: 33.333333% !important;
        }
      }
    }
    
    // Special handling for the standalone "Remove Profile" card
    > ion-col {
      flex: 0 0 33.333333% !important;
      max-width: 33.333333% !important;
      padding: 0 8px;
    }
  }
  
  .card {
    height: 130px;
    margin: 8px 0;
    
    @media (min-width: 1200px) {
      height: 140px;
      margin: 12px 0;
    }
        
    .quick-action {
      padding: 20px;
      
      @media (min-width: 1200px) {
        padding: 24px;
      }
          
      ion-row {
        .center {
          ion-icon {
            font-size: 30px;
            
            @media (min-width: 1200px) {
              font-size: 34px;
            }
            
            @media (min-width: 1400px) {
              font-size: 36px;
            }
          }
        }
        
        .center:last-child {
          ion-card-subtitle {
            font-size: 14px;
            
            @media (min-width: 1200px) {
              font-size: 15px;
            }
            
            @media (min-width: 1400px) {
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}

/* Loading States and Accessibility */
.card {
  &:focus {
    outline: 2px solid var(--ion-color-primary);
    outline-offset: 2px;
  }
  
  &.loading {
    opacity: 0.7;
    pointer-events: none;
    
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 20px;
      height: 20px;
      border: 2px solid transparent;
      border-top: 2px solid var(--ion-color-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      z-index: 10;
    }
  }
}

.points-item {
  &:focus {
    outline: 2px solid var(--ion-color-primary);
    outline-offset: -2px;
    border-radius: 8px;
  }
  
  &[aria-busy="true"] {
    opacity: 0.6;
    pointer-events: none;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid var(--ion-color-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      z-index: 10;
    }
  }
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Skeleton Loading Animation */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Mobile Optimizations (preserve existing behavior) */
@media (max-width: 767px) {
  
  .card {
    margin: 6px 0;
    
    &:hover {
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
  
  .points-item {
    &:hover {
      transform: none;
      padding-left: 16px;
    }
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .card,
  .points-item,
  .center ion-icon,
  .notification-button,
  .sidebar-menu-item {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }
  
  .card:hover {
    transform: none !important;
  }
  
  .points-item:hover {
    transform: none !important;
  }
}