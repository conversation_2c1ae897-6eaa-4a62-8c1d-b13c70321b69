/* Modern Dashboard Styles */

/* App Background - Blue like home screen */
.app-background {
  --background: var(--ion-color-base, #0072bc);
  position: relative;
}

/* Single Balance Card - Same as home screen */
.balance-section {
  margin-top: -30px;
  padding: 0 24px 16px;
  position: relative;
  z-index: 2;
  animation: slideUp 0.6s ease-out;
}

.balance-card {
  background: white;
  border-radius: 20px;
  padding: 0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  overflow: hidden;
  align-items: stretch;
}

.balance-item {
  flex: 1;
  padding: 20px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 90px;
  
  &.rand-value {
    background: var(--ion-color-primary, #FF6B35);
    color: white;
    
    .balance-icon {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }
  }
  
  &.points {
    background: white;
    
    .balance-label {
      color: var(--ion-color-primary, #FF6B35);
    }
    
    .balance-icon {
      background: rgba(255, 107, 53, 0.1);
      color: var(--ion-color-primary, #FF6B35);
    }
  }
}

.balance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.balance-label {
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 0.5px;
  opacity: 0.8;
  margin: 0;
}

.balance-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  ion-icon {
    font-size: 20px;
  }
}

.balance-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
  margin: 0;
  
  &.points-value {
    color: var(--ion-color-primary, #FF6B35);
  }
}

.balance-divider {
  width: 1px;
  background: rgba(0, 0, 0, 0.1);
  margin: 16px 0;
  align-self: stretch;
}

/* Modern Action Grid */
.modern-actions {
  padding: 32px 24px;
}

.actions-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 24px;
}

.modern-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.modern-action-card {
  background: #1e3a5f; /* Dark blue background */
  border-radius: 24px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  text-decoration: none;
  animation: scaleIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
  }

  /* All cards now have the same dark blue background */
  &.profile,
  &.security,
  &.pools,
  &.points-btn {
    background: #1e3a5f; /* Dark blue */
  }

  /* Action icons with orange background */
  .action-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%; /* Make it circular */
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    background: var(--ion-color-primary, #f5821f); /* Corporate orange background for all icons */

    ion-icon {
      font-size: 24px;
      color: white;
    }
  }

  .action-label {
    font-size: 16px;
    font-weight: 600;
    color: white; /* White text for dark blue background */
    margin: 0;
  }

  /* Decorative element */
  .action-decoration {
    position: absolute;
    top: -20px;
    right: -20px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(245, 130, 31, 0.2); /* Corporate orange tint for decoration */
  }
}

/* Points Summary Section */
.points-summary {
  padding: 0 24px 24px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.summary-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;

  &.clickable {
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }
  }

  .summary-icon-wrapper {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    flex-shrink: 0;

    ion-icon {
      font-size: 24px;
      color: #666;
    }

    &.primary {
      background: rgba(255, 107, 53, 0.1);
      
      ion-icon {
        color: var(--ion-color-primary, #FF6B35);
      }
    }

    &.success {
      background: rgba(76, 175, 80, 0.1);
      
      ion-icon {
        color: var(--ion-color-success, #4CAF50);
      }
    }

    &.danger {
      background: rgba(244, 67, 54, 0.1);
      
      ion-icon {
        color: var(--ion-color-danger, #F44336);
      }
    }
  }

  .summary-content {
    flex: 1;
  }

  .summary-label {
    font-size: 13px;
    font-weight: 500;
    color: #666;
    margin: 0 0 4px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .summary-value {
    font-size: 22px;
    font-weight: 700;
    color: #212121;
    margin: 0;
    line-height: 1;

    &.primary {
      color: var(--ion-color-primary, #FF6B35);
    }

    &.success {
      color: var(--ion-color-success, #4CAF50);
    }

    &.danger {
      color: var(--ion-color-danger, #F44336);
    }
  }

  .nav-arrow {
    position: absolute;
    right: 20px;
    font-size: 18px;
    color: #BDBDBD;
  }
}

/* Danger Zone */
.danger-zone {
  padding: 24px;
  margin-top: 24px;
}

.danger-action {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: white;
  border: 2px solid #FFEBEE;
  border-radius: 16px;
  color: var(--ion-color-danger, #F44336);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;

  ion-icon:first-child {
    font-size: 24px;
  }

  span {
    flex: 1;
    font-size: 16px;
  }

  .arrow {
    font-size: 18px;
    opacity: 0.5;
  }

  &:hover {
    background: #FFEBEE;
    border-color: var(--ion-color-danger, #F44336);
  }

  &:active {
    transform: scale(0.98);
  }
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 375px) {
  .balance-section {
    padding: 0 16px 12px;
  }
  
  .balance-item {
    padding: 16px;
  }
  
  .balance-value {
    font-size: 18px;
  }
  
  .balance-label {
    font-size: 10px;
  }
  
  .balance-icon {
    width: 32px;
    height: 32px;
  }
  
  .balance-icon ion-icon {
    font-size: 18px;
  }
  
  .modern-actions {
    padding: 24px 16px;
  }
  
  .modern-action-card {
    height: 120px;
    padding: 20px;
  }
  
  .points-summary {
    padding: 0 16px 16px;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .summary-card {
    padding: 16px;
  }
  
  .summary-value {
    font-size: 20px;
  }
}

/* Legacy styles for compatibility */
.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.w-full {
  width: 100%;
}