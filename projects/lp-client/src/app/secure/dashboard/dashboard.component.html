<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo
      [names]="profile?.givenNames + ' ' + profile?.surname"
      [membership]="profile?.newMembershipNumber"
      type="profile"
      [balance]="profile?.currentBalance"
      [src]="lssConfig.pages.landing.loggedinIcon"
    />
  </div>

  <div class="dashboard-content">
    <!-- First Row of 3 cards -->
    <ion-row>
      <ion-col size="12" size-sm="6" size-lg="4">
        <ion-card class="card dashboard-action-card" [routerLink]="['/secure/profile']">
          <ion-card-content class="quick-action">
            <ion-row>
              <div class="center">
                <ion-icon name="person-add-outline"></ion-icon>
              </div>
              <div class="center">
                <ion-card-subtitle>Profile</ion-card-subtitle>
              </div>
            </ion-row>
          </ion-card-content>
        </ion-card>
      </ion-col>
      
      <ion-col size="12" size-sm="6" size-lg="4">
        <ion-card class="card dashboard-action-card" [routerLink]="['/secure/security']">
          <ion-card-content class="quick-action">
            <ion-row>
              <div class="center">
                <ion-icon icon="shield-checkmark-outline"></ion-icon>
              </div>
              <div class="center">
                <ion-card-subtitle>Security</ion-card-subtitle>
              </div>
            </ion-row>
          </ion-card-content>
        </ion-card>
      </ion-col>
      
      <ion-col size="12" size-sm="6" size-lg="4">
        <ion-card class="card dashboard-action-card" [routerLink]="['/secure/pools']">
          <ion-card-content class="quick-action">
            <ion-row>
              <div class="center">
                <ion-icon icon="card-outline"></ion-icon>
              </div>
              <div class="center">
                <ion-card-subtitle>{{ terminology.plural }}</ion-card-subtitle>
              </div>
            </ion-row>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    
    <!-- Second Row of 3 cards -->
    <ion-row>
      <ion-col size="12" size-sm="6" size-lg="4">
        <ion-card class="card dashboard-action-card" [routerLink]="['/secure/points']">
          <ion-card-content class="quick-action">
            <ion-row>
              <div class="center">
                <ion-icon icon="card-outline"></ion-icon>
              </div>
              <div class="center">
                <ion-card-subtitle>Points</ion-card-subtitle>
              </div>
            </ion-row>
          </ion-card-content>
        </ion-card>
      </ion-col>
      
      <ion-col size="12" size-sm="6" size-lg="4">
        <ion-card class="card dashboard-action-card" [routerLink]="['/secure/points-topup']">
          <ion-card-content class="quick-action">
            <ion-row>
              <div class="center">
                <ion-icon name="cart-outline"></ion-icon>
              </div>
              <div class="center">
                <ion-card-subtitle>Points Top-Up</ion-card-subtitle>
              </div>
            </ion-row>
          </ion-card-content>
        </ion-card>
      </ion-col>
      
      <ion-col size="12" size-sm="6" size-lg="4">
        <ion-card class="card dashboard-action-card" [routerLink]="['/secure/claim-gift-card']">
          <ion-card-content class="quick-action">
            <ion-row>
              <div class="center">
                <ion-icon name="gift-outline"></ion-icon>
              </div>
              <div class="center">
                <ion-card-subtitle>Claim Gift Card</ion-card-subtitle>
              </div>
            </ion-row>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  <!-- Balance Summary Card -->
  <ion-card class="card balance-summary-card">
    <ion-card-content class="balance-content">
      <ion-list lines="none" class="balance-list">
        <ion-item>
          <ion-row class="ion-justify-content-between w-full">
            <ion-card-subtitle>Rand Value</ion-card-subtitle>
            <ion-text *ngIf="profile?.availRands">R {{ profile?.availRands?.toFixed(2) }}</ion-text>
            <ion-text *ngIf="!profile?.availRands">R 0.00</ion-text>
          </ion-row>
        </ion-item>

        <ion-item>
          <ion-row class="ion-justify-content-between w-full">
            <ion-card-subtitle>Current available Balance</ion-card-subtitle>
            <ion-text>{{ profile?.availUnits }}</ion-text>
          </ion-row>
        </ion-item>

        <ion-item [routerLink]="['/secure/points']" class="points-item">
          <ion-row class="ion-justify-content-between w-full">
            <ion-card-subtitle>{{lssConfig.pointsTitle}}</ion-card-subtitle>
            <ion-text>{{ profile?.currentBalance! }}</ion-text>
          </ion-row>
        </ion-item>

        <ion-item [routerLink]="['/secure/points']" class="points-item">
          <ion-row class="ion-justify-content-between w-full">
            <ion-card-subtitle>Earned</ion-card-subtitle>
            <ion-text class="pcu-earned">{{profile?.baseMiles! + profile?.bonusMiles!}}</ion-text>
          </ion-row>
        </ion-item>

        <ion-item class="points-item">
          <ion-row class="ion-justify-content-between w-full">
            <ion-card-subtitle>Used</ion-card-subtitle>
            <ion-text class="pcu-spent">{{profile?.expiredMiles! + profile?.usedMiles!}}</ion-text>
          </ion-row>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>
  
  <ion-col>
    <ion-card class="card dashboard-action-card" [routerLink]="['/secure/profileremove']">
      <ion-card-content class="quick-action">
        <ion-row>
          <div class="center">
            <ion-icon name="trash-outline"></ion-icon>
          </div>
          <div class="center">
            <ion-card-subtitle>Remove Profile</ion-card-subtitle>
          </div>
        </ion-row>
      </ion-card-content>
    </ion-card>
  </ion-col>
  </div>
</lib-page-wrapper>
