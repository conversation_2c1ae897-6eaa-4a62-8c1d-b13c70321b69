/* Modern Virtual Card Styles */

/* Card Section */
.card-section {
  padding-bottom: 24px;
}

/* Card Container */
.card-container {
  background: white;
  border-radius: 24px;
  padding: 40px 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  min-height: 480px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: slideUp 0.6s ease-out;
  
  &.card-loaded {
    background: linear-gradient(135deg, #f5f7fa 0%, #e8ecf1 100%);
  }
}

.card-wrapper {
  position: relative;
  width: 100%;
  max-width: 280px;
  aspect-ratio: 0.63; // Vertical card ratio (portrait)
}

/* Card Shadow */
.card-shadow {
  position: absolute;
  top: 8px;
  left: 8px;
  right: -8px;
  bottom: -8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  filter: blur(16px);
  opacity: 0;
  transition: opacity 0.6s ease;
  
  .card-loaded & {
    opacity: 1;
  }
}

/* Virtual Card */
.virtual-card {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 16px;
  overflow: hidden;
  background: transparent;
  transform-style: preserve-3d;
  transition: transform 0.6s ease;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
  }
  
  &.flip-in {
    animation: flipIn 1s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Card Shimmer Effect */
.card-shimmer {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    105deg,
    transparent 40%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 60%
  );
  animation: shimmer 2s infinite;
  animation-delay: 1s;
  opacity: 0;
  
  .card-loaded & {
    opacity: 1;
  }
}

/* Loading State */
.card-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  
  ion-spinner {
    --color: var(--ion-color-primary, #FF6B35);
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }
  
  p {
    color: #666;
    font-size: 16px;
    margin: 0;
  }
}

/* Error State */
.card-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  
  ion-icon {
    font-size: 64px;
    color: #e0e0e0;
    margin-bottom: 16px;
  }
  
  p {
    color: #666;
    font-size: 16px;
    margin: 0 0 16px 0;
  }
  
  ion-button {
    --color: var(--ion-color-primary, #FF6B35);
  }
}

/* Info Section */
.info-section {
  padding-bottom: 32px;
  animation: fadeIn 0.8s ease-out;
  animation-delay: 0.6s;
  animation-fill-mode: both;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.info-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  animation: slideUp 0.6s ease-out;
  animation-fill-mode: both;
  
  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
}

.info-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 107, 53, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  
  ion-icon {
    font-size: 24px;
    color: var(--ion-color-primary, #FF6B35);
  }
}

.info-content {
  flex: 1;
  
  .info-label {
    font-size: 13px;
    color: #666;
    margin: 0 0 4px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .info-value {
    font-size: 18px;
    font-weight: 600;
    color: #212121;
    margin: 0;
    
    &.primary {
      color: var(--ion-color-primary, #FF6B35);
    }
    
    &.locked {
      color: var(--ion-color-warning, #FFC107);
    }
  }
}

/* Animations */
@keyframes flipIn {
  0% {
    transform: perspective(1000px) rotateY(-90deg);
    opacity: 0;
  }
  40% {
    transform: perspective(1000px) rotateY(20deg);
  }
  60% {
    transform: perspective(1000px) rotateY(-10deg);
  }
  80% {
    transform: perspective(1000px) rotateY(5deg);
  }
  100% {
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 375px) {
  .card-section {
    padding-bottom: 20px;
  }
  
  .card-container {
    padding: 24px 16px;
  }
  
  .card-wrapper {
    max-width: 240px;
  }
  
  .action-btn {
    padding: 12px;
    
    ion-icon {
      font-size: 20px;
    }
    
    span {
      font-size: 11px;
    }
  }
  
  .info-section {
    padding-bottom: 24px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .info-card {
    padding: 16px;
  }
  
  .info-value {
    font-size: 16px;
  }
}