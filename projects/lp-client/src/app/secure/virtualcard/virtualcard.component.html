<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo 
      [names]="profile?.givenNames + ' ' + profile?.surname"
      [membership]="profile?.newMembershipNumber"
      type="card"
      [balance]="profile?.currentBalance" 
      [src]="lssConfig.pages.landing.loggedinIcon" 
    />
  </div>
  
  <!-- Virtual Card Section -->
  <div class="card-section">
    <div class="card-container" [class.card-loaded]="cardLoaded">
      <div class="card-wrapper">
        <!-- Card Shadow Layer -->
        <div class="card-shadow"></div>
        
        <!-- Actual Card -->
        <div class="virtual-card" [class.flip-in]="cardLoaded">
          <img 
            [src]="profile?.virtualCard" 
            alt="Virtual Card"
            (load)="onCardImageLoad()"
            (error)="onCardImageError()"
          >
          
          <!-- Card Shimmer Effect -->
          <div class="card-shimmer"></div>
        </div>
        
        <!-- Loading State -->
        <div class="card-loading" *ngIf="!cardLoaded && !cardError">
          <ion-spinner name="crescent"></ion-spinner>
          <p>Loading your card...</p>
        </div>
        
        <!-- Error State -->
        <div class="card-error" *ngIf="cardError">
          <ion-icon name="card-outline"></ion-icon>
          <p>Unable to load card</p>
          <ion-button size="small" fill="clear" (click)="reloadCard()">
            <ion-icon name="refresh-outline" slot="start"></ion-icon>
            Retry
          </ion-button>
        </div>
      </div>
    </div>
    
  </div>
  
  <!-- Card Information -->
  <div class="info-section" *ngIf="cardLoaded">
    <h3 class="section-title">Card Information</h3>
    
    <div class="info-grid">
      <div class="info-card">
        <div class="info-icon">
          <ion-icon name="person-outline"></ion-icon>
        </div>
        <div class="info-content">
          <p class="info-label">Cardholder</p>
          <p class="info-value">{{ profile?.givenNames }} {{ profile?.surname }}</p>
        </div>
      </div>
      
      <div class="info-card">
        <div class="info-icon">
          <ion-icon name="id-card-outline"></ion-icon>
        </div>
        <div class="info-content">
          <p class="info-label">Member Number</p>
          <p class="info-value">{{ profile?.newMembershipNumber }}</p>
        </div>
      </div>
      
      
      <div class="info-card">
        <div class="info-icon">
          <ion-icon name="star-outline"></ion-icon>
        </div>
        <div class="info-content">
          <p class="info-label">Available Points</p>
          <p class="info-value primary">{{ formatNumber(profile?.currentBalance) }}</p>
        </div>
      </div>
    </div>
  </div>
</lib-page-wrapper>