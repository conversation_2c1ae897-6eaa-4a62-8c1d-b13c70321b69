import { Component, Injector, OnInit } from '@angular/core';
import {
  MemberProfile,
  MemberService,
  KeyCloakService,
  LssConfig,
} from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-virtualcard',
  templateUrl: 'virtualcard.component.html',
  styleUrls: ['virtualcard.component.scss'],
})
export class VirtualCardComponent extends AbstractComponent {
  profile?: MemberProfile;
  environment = environment;
  
  // Card state
  cardLoaded = false;
  cardError = false;

  constructor(
    injector: Injector,
    private memberService: MemberService,
    private kc: KeyCloakService,
    public lssConfig: LssConfig
  ) {
    super(injector);
  }

  ngOnInit() {
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        this.loading = false;
        this.detectChanges();
      })
    );
  }

  // Card image handlers
  onCardImageLoad() {
    setTimeout(() => {
      this.cardLoaded = true;
      this.cardError = false;
      this.detectChanges();
    }, 300); // Small delay for smooth animation
  }

  onCardImageError() {
    this.cardError = true;
    this.cardLoaded = false;
    this.detectChanges();
  }

  reloadCard() {
    this.cardError = false;
    this.cardLoaded = false;
    this.detectChanges();
    
    // Force reload by updating the image src
    if (this.profile?.virtualCard) {
      const img = new Image();
      img.src = this.profile.virtualCard;
      img.onload = () => this.onCardImageLoad();
      img.onerror = () => this.onCardImageError();
    }
  }

  // Format number with thousand separators
  formatNumber(value: number | undefined | null): string {
    if (!value && value !== 0) return '0';
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
}