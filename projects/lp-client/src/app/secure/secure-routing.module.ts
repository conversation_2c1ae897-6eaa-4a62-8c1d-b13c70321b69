import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashboardComponent } from './dashboard/dashboard.component';
import { ProfileComponent } from './profile/profile.component';
import { SettingsComponent } from './settings/settings.component';
import { TransactionsComponent } from './transactions/transactions.component';
import { StatementsComponent } from './statements/statements.component';
import { ContactusComponent } from './contactus/contactus.component';
import { SecurityComponent } from './security/security.component';
import { ProfileremoveComponent } from './profileremove/profileremove.component';
import { PoolsComponent } from './pools/pools.component';
import { PointsComponent } from './points/points.component';
import { NotificationSettingsComponent } from './notification-settings/notification-settings.component';
import { PointsTopupComponent } from './points-topup/points-topup.component';
import { ClaimGiftCardComponent } from './claim-gift-card/claim-gift-card.component';


const routes: Routes = [
  {
    path: '',
    component: DashboardComponent,
  },
  {
    path: 'dashboard',
    component: DashboardComponent,
  },
  {
    path: 'contactus',
    component: ContactusComponent,
  },
  {
    path: 'profile',
    component: ProfileComponent,
  },
  {
    path: 'settings',
    component: SettingsComponent,
  },
  {
    path: 'transactions',
    component: TransactionsComponent,
  },
  {
    path: 'statements',
    component: StatementsComponent,
  },
  {
    path: 'security',
    component: SecurityComponent,
  },
  {
    path: 'profileremove',
    component: ProfileremoveComponent,
  },
  {
    path: 'pools',
    component: PoolsComponent,
  },
  {
    path: 'points',
    component: PointsComponent,
  },
  {
    path: 'notification-settings',
    component: NotificationSettingsComponent,
  },
  {
    path: 'points-topup',
    component: PointsTopupComponent,
  },
  {
    path: 'claim-gift-card',
    component: ClaimGiftCardComponent,
  },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SecureRoutingModule {}
