<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo
    [names]="profile?.givenNames + ' ' + profile?.surname"
    [membership]="profile?.newMembershipNumber"
    type="pools"
    [poolsTerminology]="terminology.plural || 'Pools'"
    [balance]="profile?.currentBalance"
    [src]="lssConfig.pages.landing.loggedinIcon"
  />
  </div>

  <!-- Pool Invitation Component -->
  <div *ngIf="hasPoolInvite" class="pool-invite-container">
    <lib-account-pool-invite 
      [membershipNumber]="profile?.newMembershipNumber || ''"
      [terminology]="terminology"
      (inviteAccepted)="onPoolInviteAccepted($event)"
      (inviteDeclined)="onPoolInviteDeclined($event)"
      (error)="onPoolError($event)"
    ></lib-account-pool-invite>
  </div>

  <!-- Account Pool Information - Only show for active members, not invited users -->
  <ion-card *ngIf="poolInfo && isActiveMember && !hasPoolInvite" class="account-pool-card p-4">
    <!-- <ion-card-header> -->
      <h2>{{ terminology.singular }}</h2>
      <h5 *ngIf="poolInfo.POOLNAME">{{ poolInfo.POOLNAME }}</h5>
    <!-- </ion-card-header> -->

    <ion-card-content>
      <ion-list>
        <ion-item>
          <ion-label>{{ terminology.singular }} Account</ion-label>
          <ion-note slot="end">{{ poolInfo.MPACC }}</ion-note>
        </ion-item>
        <ion-item>
          <ion-label>Status</ion-label>
          <ion-note slot="end">{{ formatStatus(poolInfo.STATUS) }}</ion-note>
        </ion-item>
        <ion-item>
          <ion-label>{{ terminology['totalUnits'] || 'Total Units' }}</ion-label>
          <ion-note slot="end">{{ poolInfo.TOTALUNITS }}</ion-note>
        </ion-item>
        <ion-item>
          <ion-label>Created</ion-label>
          <ion-note slot="end">{{ poolInfo.BEGINDATE ? (poolInfo.BEGINDATE | date:'mediumDate') : 'N/A' }}</ion-note>
        </ion-item>
      </ion-list>

      <ion-list-header>
        <ion-label>Members</ion-label>
      </ion-list-header>

      <div class="members-table">
        <div class="table-header">
          <div class="table-cell header">Member</div>
          <div class="table-cell header">Type/Status</div>
          <!-- <div class="table-cell header">Status</div> -->
          <div class="table-cell header">Balance</div>
        </div>
        <div class="table-row" *ngFor="let member of poolInfo.members">
          <div class="table-cell member-info">
            <div class="member-name">{{ member.NAME || 'Unknown' }}</div>
            <div class="member-id">{{ member.MPACC.trim() }}</div>
          </div>
          <div class="table-cell badges-column">
            <ion-badge [color]="member.TYPE === 'ADMN' ? 'primary' : 'medium'">
              {{ member.TYPE === 'ADMN' ? 'Admin' : 'Member' }}
            </ion-badge> 
            <ion-badge [color]="getStatusColor(member)">
              {{ getStatusText(member) }}
            </ion-badge>
          </div>
          <!-- <div class="table-cell">
            <ion-badge [color]="getStatusColor(member)">
              {{ getStatusText(member) }}
            </ion-badge>
          </div> -->
          <div class="table-cell">{{ member.BALANCE }}</div>
        </div>
      </div>
    </ion-card-content>
  </ion-card>

  <!-- Pending Join Request Message -->
  <ion-card *ngIf="hasPendingJoinRequest && !hasPoolInvite && !isPoolLoading && !isRefreshingAfterInvite" class="pending-request-card p-4">
    <ion-card-content>
      <div class="pending-request-content">
        <ion-icon name="hourglass-outline" class="pending-icon"></ion-icon>
        <div class="pending-text">
          <h3>{{ terminology.singular }} Request Pending</h3>
          <p>Your request to join the {{ terminology.singular.toLowerCase() }} is waiting for approval from the {{ terminology.singular.toLowerCase() }} administrator.</p>
        </div>
      </div>
    </ion-card-content>
  </ion-card>

  <!-- Loading and Error States -->
  <div *ngIf="isPoolLoading || isRefreshingAfterInvite" class="loading-container">
    <ion-spinner name="circles"></ion-spinner>
    <p>{{ isRefreshingAfterInvite ? 'Updating ' + terminology.singular.toLowerCase() + ' membership...' : terminology.loadingInfo }}</p>
  </div>

<!-- 
  <div *ngIf="poolError" class="error-container">
    <ion-icon name="alert-circle-outline"></ion-icon>
    <p>{{ poolError }}</p>
  </div> -->

  <!-- Account Pool Management - Show when user is active member OR when they need to create/join -->
  <ion-card *ngIf="!hasPoolInvite && !isPoolLoading && !isRefreshingAfterInvite" class="account-pool-container p-4">
    <lib-account-pool
      [membershipNumber]="profile?.newMembershipNumber || ''"
      [terminology]="terminology"
      [isActiveMember]="isActiveMember"
      [hasPoolInvite]="hasPoolInvite"
      [poolInfo]="poolInfo"
      (error)="onPoolError($event)"
      (poolExited)="onPoolExited($event)"
      (inviteProcessed)="onInviteProcessed($event)"
    ></lib-account-pool>
  </ion-card>
</lib-page-wrapper>
