<ion-content class="app-background">
  <!-- Modern Header -->
  <lib-head-logo
    [names]="profile?.givenNames + ' ' + profile?.surname"
    type="pools"
    [membership]="profile?.newMembershipNumber"
    [balance]="profile?.currentBalance"
    [src]="lssConfig.pages.landing.loggedinIcon"
  />

  <!-- Pools Section -->
  <div class="pools-section">
    <!-- Pool Invitation Card -->
    <div *ngIf="hasPoolInvite" class="invitation-card">
      <div class="invitation-header">
        <div class="invitation-icon">
          <ion-icon name="mail-outline"></ion-icon>
        </div>
        <h3>Pool Invitation</h3>
      </div>
      <lib-account-pool-invite 
        [membershipNumber]="profile?.newMembershipNumber || ''"
        (inviteAccepted)="onPoolInviteAccepted($event)"
        (inviteDeclined)="onPoolInviteDeclined($event)"
        (error)="onPoolError($event)"
      ></lib-account-pool-invite>
    </div>

    <!-- Active Pool Card -->
    <div *ngIf="poolInfo" class="pool-info-card">
      <div class="pool-header">
        <div class="pool-icon">
          <ion-icon name="people-circle"></ion-icon>
        </div>
        <div class="pool-title">
          <h2>{{ poolInfo.POOLNAME }}</h2>
          <p>Account Pool #{{ poolInfo.MPACC }}</p>
        </div>
        <div class="pool-status" [class.active]="poolInfo.STATUS === 'STAA'">
          {{ formatStatus(poolInfo.STATUS) }}
        </div>
      </div>

      <!-- Pool Stats -->
      <div class="pool-stats">
        <div class="stat-item">
          <ion-icon name="wallet-outline"></ion-icon>
          <div class="stat-content">
            <span class="stat-value">{{ poolInfo.TOTALUNITS || 0 }}</span>
            <span class="stat-label">Total Points</span>
          </div>
        </div>
        <div class="stat-item">
          <ion-icon name="people-outline"></ion-icon>
          <div class="stat-content">
            <span class="stat-value">{{ poolInfo.members?.length || 0 }}</span>
            <span class="stat-label">Members</span>
          </div>
        </div>
        <div class="stat-item">
          <ion-icon name="calendar-outline"></ion-icon>
          <div class="stat-content">
            <span class="stat-value">{{ poolInfo.BEGINDATE ? (poolInfo.BEGINDATE | date:'MMM d') : 'N/A' }}</span>
            <span class="stat-label">Created</span>
          </div>
        </div>
      </div>

      <!-- Members Section -->
      <div class="members-section">
        <h3 class="section-title">
          <ion-icon name="people"></ion-icon>
          Pool Members
        </h3>
        
        <div class="members-list">
          <div class="member-card" *ngFor="let member of poolInfo.members; let i = index" 
               [style.animation-delay]="i * 0.1 + 's'">
            <div class="member-avatar">
              <ion-icon name="person-circle"></ion-icon>
            </div>
            <div class="member-info">
              <h4>{{ member.NAME || 'Unknown Member' }}</h4>
              <p class="member-id">{{ member.MPACC.trim() }}</p>
            </div>
            <div class="member-meta">
              <span class="member-type" [class.admin]="member.TYPE === 'ADMN'">
                <ion-icon [name]="member.TYPE === 'ADMN' ? 'shield' : 'person'"></ion-icon>
                {{ member.TYPE === 'ADMN' ? 'Admin' : 'Member' }}
              </span>
              <span class="member-balance">{{ member.BALANCE }} pts</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Pool Card -->
    <div *ngIf="!poolInfo && !hasPoolInvite && !isPoolLoading" class="no-pool-card">
      <div class="empty-state">
        <div class="empty-icon">
          <ion-icon name="people-outline"></ion-icon>
        </div>
        <h3>No Account Pool</h3>
        <p>You're not currently a member of any account pool. Create or join a pool to share points with family and friends.</p>
      </div>
    </div>

    <!-- Pool Management Card -->
    <div class="management-card">
      <lib-account-pool 
        [membershipNumber]="profile?.newMembershipNumber || ''"
        (inviteProcessed)="onInviteProcessed($event)"
        (error)="onPoolError($event)"
      ></lib-account-pool>
    </div>

    <!-- Loading State -->
    <div *ngIf="isPoolLoading" class="loading-state">
      <div class="loading-card">
        <ion-spinner name="crescent"></ion-spinner>
        <p>Loading pool information...</p>
      </div>
    </div>
  </div>
</ion-content>