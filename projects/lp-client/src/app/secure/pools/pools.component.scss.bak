/* Modern Pools Page Styles */

/* App Background */
.app-background {
  --background: var(--ion-color-base, #0072bc);
  height: 100%;
}

/* Pools Section */
.pools-section {
  margin-top: -30px;
  padding: 0 20px 20px;
  position: relative;
  z-index: 2;
  animation: slideUp 0.6s ease-out;
}

/* Invitation Card */
.invitation-card {
  background: white;
  border-radius: 20px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  animation: slideIn 0.6s ease-out;
  border: 2px solid var(--ion-color-primary, #FF6B35);
  
  .invitation-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    
    .invitation-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, #FF6B35, #F44336);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      ion-icon {
        font-size: 24px;
        color: white;
      }
    }
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #212121;
    }
  }
}

/* Pool Info Card */
.pool-info-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  animation: slideIn 0.6s ease-out;
  animation-delay: 0.1s;
  animation-fill-mode: both;
}

.pool-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  position: relative;
  
  .pool-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #2196F3, #1976D2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    
    ion-icon {
      font-size: 32px;
      color: white;
    }
  }
  
  .pool-title {
    flex: 1;
    
    h2 {
      margin: 0 0 4px 0;
      font-size: 22px;
      font-weight: 600;
      color: #212121;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: #666;
      font-family: monospace;
    }
  }
  
  .pool-status {
    position: absolute;
    top: 0;
    right: 0;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    background: #e9ecef;
    color: #495057;
    
    &.active {
      background: #4CAF50;
      color: white;
    }
  }
}

/* Pool Stats */
.pool-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 24px;
  
  .stat-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    ion-icon {
      font-size: 24px;
      color: var(--ion-color-primary, #FF6B35);
      margin-bottom: 8px;
    }
    
    .stat-content {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .stat-value {
        font-size: 20px;
        font-weight: 600;
        color: #212121;
      }
      
      .stat-label {
        font-size: 12px;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

/* Members Section */
.members-section {
  margin-top: 24px;
  
  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: #212121;
    
    ion-icon {
      font-size: 20px;
      color: var(--ion-color-primary, #FF6B35);
    }
  }
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.member-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
  animation: slideIn 0.6s ease-out;
  animation-fill-mode: both;
  
  &:hover {
    background: #e9ecef;
    transform: translateX(4px);
  }
  
  .member-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #9C27B0, #7B1FA2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    
    ion-icon {
      font-size: 28px;
      color: white;
    }
  }
  
  .member-info {
    flex: 1;
    
    h4 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: #212121;
    }
    
    .member-id {
      margin: 0;
      font-size: 12px;
      color: #666;
      font-family: monospace;
      background: #e9ecef;
      padding: 2px 8px;
      border-radius: 12px;
      display: inline-block;
    }
  }
  
  .member-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    
    .member-type {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 4px 10px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
      background: #e9ecef;
      color: #495057;
      
      ion-icon {
        font-size: 14px;
      }
      
      &.admin {
        background: var(--ion-color-primary, #FF6B35);
        color: white;
      }
    }
    
    .member-balance {
      font-size: 14px;
      font-weight: 600;
      color: #4CAF50;
    }
  }
}

/* No Pool Card */
.no-pool-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  animation: fadeIn 0.8s ease-out;
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    
    .empty-icon {
      width: 80px;
      height: 80px;
      background: rgba(33, 150, 243, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;
      
      ion-icon {
        font-size: 40px;
        color: #2196F3;
      }
    }
    
    h3 {
      margin: 0 0 12px 0;
      font-size: 20px;
      font-weight: 600;
      color: #212121;
    }
    
    p {
      margin: 0;
      font-size: 15px;
      color: #666;
      line-height: 1.6;
      max-width: 300px;
      margin: 0 auto;
    }
  }
}

/* Management Card */
.management-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  animation: slideIn 0.6s ease-out;
  animation-delay: 0.2s;
  animation-fill-mode: both;
  
  // Override default lib-account-pool styles
  ::ng-deep {
    ion-card {
      margin: 0;
      padding: 0;
      box-shadow: none;
      --background: transparent;
    }
    
    ion-card-content {
      padding: 0;
    }
  }
}

/* Loading State */
.loading-state {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  
  .loading-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    animation: fadeIn 0.4s ease-out;
    
    ion-spinner {
      --color: var(--ion-color-primary, #FF6B35);
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }
    
    p {
      margin: 0;
      font-size: 16px;
      color: #666;
    }
  }
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 375px) {
  .pools-section {
    padding: 0 16px 16px;
  }
  
  .pool-info-card,
  .invitation-card,
  .no-pool-card,
  .management-card {
    padding: 20px;
    margin-bottom: 12px;
  }
  
  .pool-header {
    gap: 12px;
    
    .pool-icon {
      width: 56px;
      height: 56px;
      
      ion-icon {
        font-size: 28px;
      }
    }
    
    .pool-title {
      h2 {
        font-size: 18px;
      }
      
      p {
        font-size: 12px;
      }
    }
  }
  
  .pool-stats {
    gap: 8px;
    
    .stat-item {
      padding: 12px 8px;
      
      ion-icon {
        font-size: 20px;
      }
      
      .stat-content {
        .stat-value {
          font-size: 16px;
        }
        
        .stat-label {
          font-size: 10px;
        }
      }
    }
  }
  
  .member-card {
    padding: 12px;
    gap: 10px;
    
    .member-avatar {
      width: 40px;
      height: 40px;
      
      ion-icon {
        font-size: 24px;
      }
    }
    
    .member-info {
      h4 {
        font-size: 14px;
      }
    }
    
    .member-meta {
      .member-type {
        font-size: 11px;
        padding: 3px 8px;
      }
      
      .member-balance {
        font-size: 13px;
      }
    }
  }
}