import { Component, Injector, OnInit } from '@angular/core';
import {
  MemberProfile,
  MemberService,
  KeyCloakService,
  LssConfig,
  AccountPoolService
} from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Location } from '@angular/common';

@Component({
  selector: 'app-pools',
  templateUrl: 'pools.component.html',
  styleUrls: ['pools.component.scss'],
})
export class PoolsComponent extends AbstractComponent implements OnInit {
  profile?: MemberProfile;
  poolInfo: any = null;
  hasPoolInvite = false;
  isActiveMember = false; // Track if user is an active member (not just invited)
  hasPendingJoinRequest = false; // Track if user has pending join request (INVITESTATUS: 'REQS')
  isPoolLoading = false;
  poolError = '';
  isRefreshingAfterInvite = false; // Track when we're refreshing after accepting invite
  
  // Add form for points transfer
  transferForm: FormGroup;
  
  // Terminology helper
  get terminology() {
    return this.lssConfig.terminology?.pool || {
      singular: 'Pool',
      plural: 'Pools',
      management: 'Pool Management',
      member: 'Pool Member',
      notInMessage: 'Member is currently not in a pool',
      createNew: 'Create New Pool',
      joinExisting: 'Join Existing Pool',
      invitation: 'Pool Invitation',
      exitConfirmTitle: 'Exit Pool',
      exitConfirmMessage: 'Are you sure you want to exit the pool',
      exitWarning: 'Warning: This action cannot be undone. You will need to be re-invited to rejoin the pool.',
      createdSuccess: 'Pool created successfully!',
      joinRequestSuccess: 'Join request sent successfully!',
      invitationSentSuccess: 'Invitation sent successfully!',
      exitSuccess: 'You have successfully exited the pool.',
      removeMemberSuccess: 'Member removed successfully!',
      approveJoinSuccess: 'Join request approved! Member has been added to the pool.',
      rejectJoinSuccess: 'Join request rejected and member removed.',
      acceptInviteSuccess: 'Successfully joined the pool!',
      inviteAcceptedToast: 'Pool invitation accepted successfully!',
      inviteDeclinedToast: 'Pool invitation declined',
      errorOccurred: 'An error occurred with the account pool',
      exitToast: 'You have successfully exited the pool',
      inviteToast: 'Successfully invited member {membershipNumber} to the pool!',
      loadingInfo: 'Loading pool information...',
      noInfoAvailable: 'No pool information available.',
      alreadyInAnother: 'This member is already in another pool and cannot join this one.',
      totalUnits: 'Total Units',
      failedToCreate: 'Failed to create pool. Please try again.',
      failedToJoin: 'Failed to request pool join. Please try again.',
      failedToExit: 'Failed to exit pool. Please try again.',
      failedToLoad: 'Failed to load pool information'
    };
  }

  constructor(
    injector: Injector,
    private memberService: MemberService,
    private kc: KeyCloakService,
    private accountPoolService: AccountPoolService,
    public lssConfig: LssConfig,
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private location: Location
  ) {
    super(injector);
    // Initialize form to fix "transferForm.get is not a function" error
    this.transferForm = this.fb.group({
      points: ['', [Validators.required, Validators.min(1)]],
      recipient: ['', Validators.required]
    });
  }

  ngOnInit() {
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        if (this.profile?.newMembershipNumber) {
          // Don't show errors initially - check pool status gracefully
          this.checkPoolInvite();
          this.findPool();
        }
        this.detectChanges();
      })
    );
  }


  onTransferSuccess(_event: any) {
    this.presentToast({
      message: 'Points transferred successfully!',
      color: 'success',
      duration: 3000,
      position: 'bottom'
    });
    // Refresh profile data to get updated points balance
    this.refreshProfileData();
  }

  onTransferError(error: any) {
    this.presentToast({
      message: error?.error?.message || 'Failed to transfer points. Please try again.',
      color: 'danger',
      duration: 3000,
      position: 'bottom'
    });
  }

  onPoolInviteAccepted(event: any) {
    this.isRefreshingAfterInvite = true;
    this.hasPoolInvite = false;
    this.detectChanges();

    this.presentToast({
      message: this.terminology.inviteAcceptedToast,
      color: 'success',
      duration: 3000,
      position: 'bottom'
    });

    this.retryFindPool(() => {
      this.router.navigate(['/app/home']);
    });
  }

  onPoolInviteDeclined(_event: any) {
    this.presentToast({
      message: this.terminology.inviteDeclinedToast,
      color: 'medium',
      duration: 3000,
      position: 'bottom'
    });
    this.hasPoolInvite = false;
    this.isActiveMember = false; // User declined, not a member
    this.hasPendingJoinRequest = false;
    this.poolInfo = null; // Clear pool info since they're not a member
    this.detectChanges();
  }

  onPoolError(error: any) {
    // Don't show error toast for 404 errors - user simply not in a pool
    if (error?.status === 404 || error?.originalError?.status === 404) {
      return;
    }

    // Don't show error for "not found" type messages
    const errorMessage = error?.error?.message || error?.message || '';
    if (errorMessage.toLowerCase().includes('not found') ||
        errorMessage.toLowerCase().includes('no pool') ||
        errorMessage.toLowerCase().includes('404')) {
      return;
    }

    // Only show actual errors
    this.presentToast({
      message: errorMessage || this.terminology.errorOccurred,
      color: 'danger',
      duration: 3000,
      position: 'bottom'
    });
  }

  onInviteProcessed(event: any) {
    this.presentToast({
      message: this.terminology.inviteToast.replace('{membershipNumber}', event.membershipNumber),
      color: 'success',
      duration: 3000,
      position: 'bottom'
    });
    // Refresh pool data after inviting a member
    this.findPool();
  }

  onPoolExited(event: any) {
    this.presentToast({
      message: this.terminology.exitToast,
      color: 'success',
      duration: 3000,
      position: 'bottom'
    });

    // Reset pool data
    this.poolInfo = null;
    this.hasPoolInvite = false;
    this.isActiveMember = false; // User is no longer a member
    this.hasPendingJoinRequest = false;
    
    // After a short delay, navigate back to home/dashboard
    setTimeout(() => {
      this.router.navigate(['/app/home']).catch((err) => {
        // Fallback to browser history if navigation fails
        if (window.history.length > 1) {
          window.history.back();
        }
      });
    }, 1000);
  }

  private refreshProfileData() {
    if (this.kc.lpUniueReference) {
      this.memberService.memberBalance(this.kc.lpUniueReference).subscribe((data: any) => {
        this.profile = { ...this.profile, ...data };
        this.detectChanges();
      });
    }
  }

  private checkPoolInvite() {
    if (!this.profile?.newMembershipNumber) {
      return;
    }

    this.accountPoolService.checkInviteStatus(this.profile.newMembershipNumber).subscribe({
      next: (response: any) => {
        this.hasPoolInvite = response?.status === 'Y';
        this.detectChanges();
      },
      error: (error: any) => {
        // Gracefully handle the error - assume no invite if there's an error
        this.hasPoolInvite = false;
        this.detectChanges();
      }
    });
  }

  /**
   * Formats status code to human readable text
   * @param status The status code to format
   * @returns Formatted status text
   */
  formatStatus(status: string): string {
    const statusMap: { [key: string]: string } = {
      'STAA': 'Active',
      'INAC': 'Inactive',
      'PEND': 'Pending',
      'SUSP': 'Suspended'
    };
    return statusMap[status] || status;
  }

  /**
   * Get status text for a member based on their invitation and member status
   * @param member The member object
   * @returns Status text to display
   */
  getStatusText(member: any): string {
    // Check invitation status first
    if (member.INVITESTATUS === 'INVT') {
      return 'Invited';
    } else if (member.INVITESTATUS === 'REQS') {
      return 'Join Request';
    }
    
    // Check member status
    if (member.MEMBERSTATUS === 'PEND') {
      return 'Pending';
    } else if (member.MEMBERSTATUS === 'STAA') {
      return 'Active';
    }
    
    return this.formatStatus(member.MEMBERSTATUS || 'STAA');
  }

  /**
   * Get status color for a member based on their status
   * @param member The member object
   * @returns Color string for ion-badge
   */
  getStatusColor(member: any): string {
    if (member.INVITESTATUS === 'INVT') {
      return 'warning'; // Yellow for invited
    } else if (member.INVITESTATUS === 'REQS') {
      return 'tertiary'; // Purple for join request
    } else if (member.MEMBERSTATUS === 'PEND') {
      return 'warning'; // Yellow for pending
    } else if (member.MEMBERSTATUS === 'STAA') {
      return 'success'; // Green for active
    }
    
    return 'medium'; // Default gray
  }



  private retryFindPool(onSuccess: () => void, attempt = 1, maxAttempts = 5, delayMs = 1500) {
    const membershipNumber = this.kc.lpUniueReference;
    if (!membershipNumber) {
      this.isRefreshingAfterInvite = false;
      this.detectChanges();
      onSuccess();
      return;
    }

    setTimeout(() => {
      this.accountPoolService.findPool(membershipNumber).subscribe({
        next: (response: any) => {
          let isActive = false;
          if (response && response.members) {
            const currentUserMember = response.members.find(
              (member: any) => member.MPACC.trim() === membershipNumber
            );
            if (currentUserMember) {
              const hasActiveStatus = currentUserMember.MEMBERSTATUS === 'STAA';
              const notInvitedState = !currentUserMember.INVITESTATUS || currentUserMember.INVITESTATUS !== 'INVT';
              isActive = hasActiveStatus && notInvitedState;
            }
          }

          if (isActive) {
            this.poolInfo = response;
            this.isActiveMember = true;
            this.isRefreshingAfterInvite = false;
            this.detectChanges();
            onSuccess();
          } else if (attempt < maxAttempts) {
            console.log(`Pool data not reflecting active status, retrying... (attempt ${attempt + 1}/${maxAttempts})`);
            this.retryFindPool(onSuccess, attempt + 1, maxAttempts, delayMs + 500);
          } else {
            console.warn('Max retry attempts reached. Navigating anyway.');
            this.isRefreshingAfterInvite = false;
            this.detectChanges();
            onSuccess();
          }
        },
        error: (error: any) => {
          console.error(`Error finding pool during retry attempt ${attempt}:`, error);
          if (attempt < maxAttempts) {
            this.retryFindPool(onSuccess, attempt + 1, maxAttempts, delayMs + 500);
          } else {
            console.error('Max retry attempts reached after error. Navigating anyway.');
            this.isRefreshingAfterInvite = false;
            this.detectChanges();
            onSuccess();
          }
        }
      });
    }, delayMs);
  }

  private findPool() {
const membershipNumber = this.kc.lpUniueReference;
    if (!membershipNumber) {
      return;
    }

    this.isPoolLoading = true;
    this.poolError = '';
    this.detectChanges(); // Force UI update to show loading state
this.accountPoolService.findPool(membershipNumber).subscribe({
      next: (response: any) => {
        if (response) {
          
          // Improved date parsing logic
          if (response.BEGINDATE && typeof response.BEGINDATE === 'string') {
            try {
              // Better handling of the timezone format
              const dateStr = response.BEGINDATE.replace(/\[.*\]$/, '');
              const parsedDate = new Date(dateStr);
              
              // Verify the date is valid
              if (!isNaN(parsedDate.getTime())) {
                response.BEGINDATE = parsedDate;
              } else {
                response.BEGINDATE = null;
              }
            } catch (e) {
              response.BEGINDATE = null;
            }
          }
          this.poolInfo = response;

          // Check if current user is an active member (not just invited)
          if (response && response.members) {
            
            const currentUserMember = response.members.find(
(member: any) => member.MPACC.trim() === membershipNumber
            );

            if (currentUserMember) {
              // User is an active member if they have STAA status and not in invitation state
              // After accepting an invitation, INVITESTATUS should change from 'INVT' to null/empty
              const hasActiveStatus = currentUserMember.MEMBERSTATUS === 'STAA';
              const notInvitedState = !currentUserMember.INVITESTATUS || currentUserMember.INVITESTATUS !== 'INVT';
              
              this.isActiveMember = hasActiveStatus && notInvitedState;
              
              // Check if user has pending join request
              this.hasPendingJoinRequest = currentUserMember.INVITESTATUS === 'REQS';
              
              console.log('User member status:', {
                MEMBERSTATUS: currentUserMember.MEMBERSTATUS,
                INVITESTATUS: currentUserMember.INVITESTATUS,
                isActiveMember: this.isActiveMember,
                hasPendingJoinRequest: this.hasPendingJoinRequest
              });
            } else {
              this.isActiveMember = false;
              this.hasPendingJoinRequest = false;
            }
          } else {
            this.isActiveMember = false;
            this.hasPendingJoinRequest = false;
          }
        }
        this.isPoolLoading = false;
        
        // Clear the refreshing flag if it was set
        if (this.isRefreshingAfterInvite) {
          this.isRefreshingAfterInvite = false;
        }
        
        // Force change detection to ensure UI updates immediately
        this.detectChanges();
      },
      error: (error: any) => {
        this.isPoolLoading = false;
        // Don't show error for 404 - user simply doesn't belong to a pool yet
        if (error?.status === 404) {
          // User doesn't belong to a pool - this is a normal state
          this.poolInfo = null;
          this.poolError = '';
          this.isActiveMember = false;
          this.hasPendingJoinRequest = false;
        } else if (error?.status !== 404) {
          // Only show error for actual errors, not for 404
          this.poolError = error?.error?.message || this.terminology.failedToLoad;
          this.isActiveMember = false;
          this.hasPendingJoinRequest = false;
        }
        
        // Clear the refreshing flag if it was set
        this.isRefreshingAfterInvite = false;
        
        this.detectChanges();
      }
    });
  }
}
