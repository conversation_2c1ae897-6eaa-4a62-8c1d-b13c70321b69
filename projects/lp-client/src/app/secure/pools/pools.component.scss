.app-background {
  --background: var(--ion-color-base);
}

/* Pools Container for Better Spacing */
.pools-container {
  min-height: 100%;
  padding: 0;
  
  @media (min-width: 768px) {
    padding: 0 40px;
  }
  
  @media (min-width: 1024px) {
    padding: 0 40px;
  }
  
  @media (min-width: 1200px) {
    padding: 0 60px;
    max-width: 1600px;
    margin: 0 auto;
  }
}

//   .card {
//     --background: var(--ion-color-primary-shade);
//     color: var(--ion-color-primary-contrast)
//   }
.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.account-name {
  font-size: 26px
}

.name-text {
  margin-left: 20px;
  font-size: 26px

}

.w-full {
  width: 100%;
}
.card-background {
  background-color: #fff;
  width: 100vw;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Add consistent card padding
ion-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  margin: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  
  &.account-pool-card,
  &.account-pool-container {
    --padding-start: 24px;
    --padding-end: 24px;
    --padding-top: 24px;
    --padding-bottom: 24px;
  }
  
  // Remove blue backgrounds from ion-items and ion-list
  ion-item {
    --background: transparent;
    --background-focused: transparent;
    --background-hover: transparent;
    --background-activated: transparent;
    --border-color: transparent;
    --inner-border-width: 0;
    --border-width: 0;
    
    &:last-child {
      --border-width: 0;
    }
  }
  
  ion-list {
    background: transparent !important;
    --background: transparent;
  }
  
  ion-list-header {
    background: transparent !important;
    --background: transparent;
    --background-focused: transparent;
    --background-hover: transparent;
  }
  
  ion-card-content {
    background: transparent !important;
    --background: transparent;
  }
  
  &.pending-request-card {
    background: linear-gradient(135deg, #fef3cd 0%, #fffbf0 100%);
    border: 1px solid #f59e0b;
    border-radius: 12px;
    
    .pending-request-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      
      .pending-icon {
        font-size: 2rem;
        color: #f59e0b;
        flex-shrink: 0;
      }
      
      .pending-text {
        flex: 1;
        
        h3 {
          color: #92400e;
          font-size: 1.1rem;
          font-weight: 600;
          margin: 0 0 0.5rem 0;
        }
        
        p {
          color: #b45309;
          font-size: 0.9rem;
          margin: 0;
          line-height: 1.4;
        }
      }
    }
  }
}

.transaction-summary {
  ion-row {
      h3 {
          margin-bottom: 1rem;
          margin-left: 16px;
          font-size: 1.4rem;
          font-weight: 400;
      }
  }
  
  ion-col {
      ion-row {
          h3 {
              font-size: 0.8rem;
              font-weight: 400;
              width: 100%;
              margin-right: 16px;

              span {
                  font-size: 1rem;
                  padding-bottom: 0;
              }
          }
      }
  }
  
  h3 {
      margin-bottom: 1rem;
      margin-left: 16px;

      span {
          font-size: 1rem;
          display: block;
          padding-bottom: 12px;
      }
  }
}

.pcu-earned {
  color: var(--ion-color-success) !important;
}

.pcu-spent {
  color: var(--ion-color-danger) !important;
}

.points-item {
  cursor: pointer;
  position: relative;
  
  &::after {
      content: '';
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-top: 2px solid var(--ion-color-medium);
      border-right: 2px solid var(--ion-color-medium);
      transform: translateY(-50%) rotate(45deg);
  }
  
  &:hover {
      background-color: rgba(var(--ion-color-primary-rgb), 0.1);
  }
}

.members-table {
  width: 100%;
  margin-top: 1rem;
  
  .table-header {
    display: grid;
    grid-template-columns: 2fr 1.2fr 1fr;
    gap: 1px;
    background-color: var(--ion-color-light);
    padding: 0;
    border-radius: 8px 8px 0 0;
    border: 1px solid var(--ion-color-medium);
    overflow: hidden;
    
    .table-cell.header {
      font-weight: 600;
      font-size: 0.875rem;
      color: var(--ion-color-dark);
      padding: 0.75rem;
      background-color: var(--ion-color-light);
      
      &:first-child {
        border-top-left-radius: 8px;
      }
      
      &:last-child {
        border-right: none;
        border-top-right-radius: 8px;
      }
    }
  }
  
  .table-row {
    display: grid;
    grid-template-columns: 2fr 1.2fr 1fr;
    gap: 1px;
    padding: 0;
    border-bottom: 1px solid var(--ion-color-medium);
    border-left: 1px solid var(--ion-color-medium);
    border-right: 1px solid var(--ion-color-medium);
    align-items: stretch;
    
    &:last-child {
      border-bottom: 1px solid var(--ion-color-medium);
      border-radius: 0 0 8px 8px;
      overflow: hidden;
      
      .table-cell:first-child {
        border-bottom-left-radius: 8px;
      }
      
      .table-cell:last-child {
        border-bottom-right-radius: 8px;
      }
    }
    
    .table-cell {
      font-size: 0.875rem;
      padding: 0.75rem;
      background-color: white;
      display: flex;
      align-items: center;
      
      &:last-child {
        border-right: none;
      }
      
      &.member-info {
        flex-direction: column;
        align-items: flex-start;
        
        .member-name {
          font-weight: 500;
          color: var(--ion-color-dark);
          margin-bottom: 0.25rem;
        }
        
        .member-id {
          font-size: 0.75rem;
          color: var(--ion-color-medium-shade);
        }
      }
      
      &.badges-column {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        
        ion-badge {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
        }
      }
    }
  }
}

// Responsive design for smaller screens
@media (max-width: 768px) {
  // Reduce card padding on mobile
  ion-card {
    padding: 16px !important;
    margin: 8px !important;
    
    &.account-pool-card,
    &.account-pool-container {
      --padding-start: 16px;
      --padding-end: 16px;
      --padding-top: 16px;
      --padding-bottom: 16px;
    }
  }

  .members-table {
    .table-header,
    .table-row {
      // Adjust grid to give more space to Actions column
      grid-template-columns: 1.8fr 1fr 1.2fr;
    }
    
    .table-cell {
      padding: 0.4rem !important;
      font-size: 0.75rem !important;
    }
    
    .table-header .table-cell.header {
      font-size: 0.7rem;
      padding: 0.5rem !important;
    }
    
    .table-row .table-cell {
      font-size: 0.75rem;
      
      &.member-info {
        .member-name {
          font-size: 0.75rem;
        }
        
        .member-id {
          font-size: 0.65rem;
        }
      }
      
      &.badges-column {
        gap: 0.15rem;
        
        ion-badge {
          font-size: 0.65rem;
          padding: 0.15rem 0.3rem;
        }
      }
      
      // Actions column optimizations
      &:last-child {
        padding: 0.2rem !important;
        
        ion-button {
          --padding-start: 0.3rem;
          --padding-end: 0.3rem;
          --padding-top: 0.2rem;
          --padding-bottom: 0.2rem;
          font-size: 0.7rem;
          height: auto;
          min-height: 28px;
          
          ion-icon {
            font-size: 0.8rem;
            margin-inline-end: 0.2rem;
          }
        }
      }
    }
  }
}

// Extra small screens (narrow phones)
@media (max-width: 480px) {
  .members-table {
    .table-header,
    .table-row {
      // Give even more space to Actions on very narrow screens
      grid-template-columns: 1.5fr 0.8fr 1.3fr;
    }
    
    .table-cell {
      padding: 0.3rem !important;
      
      &:last-child {
        padding: 0.15rem !important;
        
        ion-button {
          --padding-start: 0.2rem;
          --padding-end: 0.2rem;
          font-size: 0.65rem;
          min-height: 24px;
          
          ion-icon {
            font-size: 0.7rem;
            margin-inline-end: 0.1rem;
          }
        }
      }
    }
    
    .table-header .table-cell.header {
      font-size: 0.65rem;
      padding: 0.4rem !important;
    }
  }
}