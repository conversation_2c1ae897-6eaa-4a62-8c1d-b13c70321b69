/* Modern Profile Styles */

/* Profile Content Container */
.profile-content {
  width: 100%;
}

/* Section Cards */
.section-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  animation: slideUp 0.6s ease-out;
  animation-fill-mode: both;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
  &:nth-child(4) { animation-delay: 0.3s; }
  
  // Desktop enhancements
  @media (min-width: 768px) {
    padding: 32px;
    margin-bottom: 28px;
    border-radius: 24px;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    }
  }
  
  @media (min-width: 1200px) {
    padding: 40px;
    margin-bottom: 32px;
  }
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #212121;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 12px;

  ion-icon {
    font-size: 24px;
    color: var(--ion-color-primary, #FF6B35);
    transition: all 0.3s ease;
  }
  
  // Desktop responsive typography
  @media (min-width: 768px) {
    font-size: 20px;
    margin: 0 0 24px 0;
    gap: 16px;
    
    ion-icon {
      font-size: 28px;
    }
  }
  
  @media (min-width: 1200px) {
    font-size: 22px;
    margin: 0 0 28px 0;
    
    ion-icon {
      font-size: 32px;
    }
  }
}

/* Form Grid Layout */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  
  // Desktop optimizations
  @media (min-width: 768px) {
    gap: 20px;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
  
  @media (min-width: 1024px) {
    gap: 24px;
    grid-template-columns: repeat(2, 1fr);
    
    // Special handling for single-column fields
    .form-field.full-width {
      grid-column: 1 / -1;
    }
  }
  
  @media (min-width: 1200px) {
    gap: 28px;
    grid-template-columns: repeat(2, 1fr);
  }
}

.form-field {
  position: relative;

  &.full-width {
    grid-column: 1 / -1;
  }
}

/* Modern Input Styling */
.modern-input {
  --background: #f8f9fa;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --inner-padding-end: 0;
  --border-width: 0;
  --inner-border-width: 0;
  --min-height: 56px;
  border-radius: 12px;
  margin-bottom: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::part(native) {
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  // Subtle hover effect for desktop
  @media (min-width: 768px) {
    &:hover:not(.ion-focused)::part(native) {
      background: #f0f0f0;
      border-color: rgba(var(--ion-color-primary-rgb), 0.2);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }
  }

  &.ion-focused::part(native) {
    border-color: var(--ion-color-primary, #FF6B35);
    background: white;
    box-shadow: 0 0 0 3px rgba(var(--ion-color-primary-rgb), 0.1);
    transform: translateY(-1px);
  }

  &.ion-invalid.ion-touched::part(native) {
    border-color: var(--ion-color-danger, #F44336);
    box-shadow: 0 0 0 3px rgba(var(--ion-color-danger-rgb), 0.1);
  }

  ion-input,
  ion-select {
    --placeholder-opacity: 0.5;
    font-size: 16px;
    transition: all 0.3s ease;
  }

  .field-icon {
    font-size: 20px;
    color: #666;
    margin-right: 8px;
    transition: all 0.3s ease;
  }
  
  // Desktop enhancements
  @media (min-width: 768px) {
    --min-height: 60px;
    --padding-start: 20px;
    --padding-end: 20px;
    border-radius: 16px;
    
    &::part(native) {
      border-radius: 16px;
    }
    
    ion-input,
    ion-select {
      font-size: 17px;
    }
    
    .field-icon {
      font-size: 22px;
      margin-right: 12px;
    }
    
    &.ion-focused {
      .field-icon {
        color: var(--ion-color-primary, #FF6B35);
        transform: scale(1.1);
      }
    }
  }
  
  @media (min-width: 1200px) {
    --min-height: 64px;
    --padding-start: 24px;
    --padding-end: 24px;
    
    ion-input,
    ion-select {
      font-size: 18px;
    }
  }
}

/* Error Messages */
.error-message {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--ion-color-danger, #F44336);
  font-size: 12px;
  margin-top: 4px;
  padding: 0 16px;
  animation: fadeIn 0.3s ease;

  ion-icon {
    font-size: 14px;
    flex-shrink: 0;
  }
}

/* Address Section */
.address-section {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
  
  // Override ion-item backgrounds in address component
  ::ng-deep {
    lp-pos-address {
      ion-item {
        --background: transparent;
        --background-hover: rgba(0, 0, 0, 0.02);
        --background-focused: rgba(0, 0, 0, 0.05);
        --background-activated: rgba(0, 0, 0, 0.05);
        
        &.ion-focused {
          --background: rgba(0, 0, 0, 0.05);
        }
      }
    }
  }
  
  // Remove underlines from all child ion-items
  ion-item {
    --border-width: 0;
    --inner-border-width: 0;
    --border-color: transparent;
  }
  
  // Desktop enhancements
  @media (min-width: 768px) {
    margin-top: 20px;
    padding: 20px;
    border-radius: 16px;
    background: #f5f6f7;
    
    &:hover {
      background: #f0f1f2;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
  }
  
  @media (min-width: 1200px) {
    margin-top: 24px;
    padding: 24px;
    border-radius: 20px;
  }
}

/* Store Section */
.store-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
  transition: all 0.3s ease;
  
  // Override ion-item backgrounds in stores component
  ::ng-deep {
    lib-stores {
      ion-item {
        --background: transparent;
        --background-hover: rgba(0, 0, 0, 0.02);
        --background-focused: rgba(0, 0, 0, 0.05);
        --background-activated: rgba(0, 0, 0, 0.05);
        
        &.ion-focused {
          --background: rgba(0, 0, 0, 0.05);
        }
      }
    }
  }
  
  // Remove underlines from all child ion-items
  ion-item {
    --border-width: 0;
    --inner-border-width: 0;
    --border-color: transparent;
  }
  
  // Desktop enhancements
  @media (min-width: 768px) {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 2px solid #e8e9ea;
    
    &:hover {
      border-top-color: rgba(var(--ion-color-primary-rgb), 0.2);
    }
  }
  
  @media (min-width: 1200px) {
    margin-top: 28px;
    padding-top: 28px;
  }
}

/* Action Section */
.action-section {
  margin-top: 32px;
  
  // Desktop optimizations
  @media (min-width: 768px) {
    margin-top: 40px;
    display: flex;
    justify-content: center;
  }
  
  @media (min-width: 1200px) {
    margin-top: 48px;
  }
}

.save-button {
  --background: var(--ion-color-primary, #FF6B35);
  --background-activated: var(--ion-color-primary-shade, #e85d2f);
  --background-hover: var(--ion-color-primary-tint, #ff7a49);
  --border-radius: 16px;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  // Shimmer effect on desktop
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
  }

  ion-icon {
    font-size: 20px;
    margin-right: 8px;
    transition: all 0.3s ease;
  }

  &:hover:not([disabled]) {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
    
    &::before {
      left: 100%;
    }
    
    ion-icon {
      transform: scale(1.1);
    }
  }

  &:active:not([disabled]) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
    transition: all 0.1s ease;
  }

  &[disabled] {
    --background: #e0e0e0;
    --color: #999;
    box-shadow: none;
    opacity: 0.6;
    
    &::before {
      display: none;
    }
  }
  
  // Desktop enhancements
  @media (min-width: 768px) {
    height: 60px;
    font-size: 17px;
    --border-radius: 20px;
    min-width: 200px;
    max-width: 300px;
    
    ion-icon {
      font-size: 22px;
      margin-right: 10px;
    }
  }
  
  @media (min-width: 1200px) {
    height: 64px;
    font-size: 18px;
    min-width: 220px;
    max-width: 320px;
    
    ion-icon {
      font-size: 24px;
      margin-right: 12px;
    }
  }
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design - Enhanced */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  // Ensure mobile behavior is preserved
  .section-card:hover {
    transform: none;
  }
  
  .modern-input:hover::part(native) {
    transform: none;
    box-shadow: none;
  }
  
  .address-section:hover,
  .store-section:hover {
    transform: none;
    box-shadow: none;
  }
}

@media (max-width: 375px) {
  .section-card {
    padding: 20px 16px;
  }



  .section-title {
    font-size: 16px;
    margin-bottom: 16px;
  }

  .modern-input {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .save-button {
    height: 48px;
    font-size: 15px;
  }
}

/* Legacy styles for compatibility */
.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.w-full {
  width: 100%;
}

/* Override default Ionic styles */
ion-item {
  --min-height: 56px;
  --border-width: 0;
  --inner-border-width: 0;
  --border-color: transparent;
}

ion-select {
  max-width: 100%;
}

/* Fix for readonly inputs */
ion-input[readonly] {
  opacity: 0.7;
  
  &::part(native) {
    background: #f0f0f0;
  }
}

/* Global fix for all ion-items in profile */
.profile-content {
  ion-item {
    --border-width: 0;
    --inner-border-width: 0;
    --border-color: transparent;
    --border-style: none;
  }
}

/* Fix for nested components */
lp-pos-address ion-item,
lib-stores ion-item {
  --border-width: 0;
  --inner-border-width: 0;
  --border-color: transparent;
  --border-style: none;
}

/* Accessibility Enhancements */
.section-card:focus-within {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

.save-button:focus-visible {
  outline: 3px solid var(--ion-color-primary);
  outline-offset: 4px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .section-card,
  .modern-input,
  .address-section,
  .store-section,
  .save-button,
  .field-icon {
    transition: none !important;
    animation: none !important;
  }
  
  .section-card:hover,
  .modern-input:hover::part(native),
  .address-section:hover,
  .store-section:hover,
  .save-button:hover:not([disabled]) {
    transform: none !important;
  }
  
  .save-button::before {
    display: none !important;
  }
}