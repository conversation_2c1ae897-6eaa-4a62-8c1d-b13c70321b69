<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo
      [names]="profile ? (profile.givenNames || '') + ' ' + (profile.surname || '') : ''"
      [membership]="profile?.newMembershipNumber"
      type="profile"
      [balance]="profile?.currentBalance"
      [src]="lssConfig.pages.landing.loggedinIcon"
    />
  </div>
  
  <!-- Profile Content -->
  <div class="profile-content">

    <form [formGroup]="profileForm" (ngSubmit)="doLoad()">
      <!-- Personal Information Section -->
      <div class="section-card">
        <h3 class="section-title">
          <ion-icon name="person-outline"></ion-icon>
          Personal Information
        </h3>
        <div class="form-grid">
          <!-- Title -->
          <div class="form-field">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="ribbon-outline" class="field-icon"></ion-icon>
              <ion-select
                label="Title"
                labelPlacement="floating"
                formControlName="title"
                placeholder="Please select"
              >
                <ion-select-option
                  *ngFor="let codeItem of getCodeList('TITL') | async"
                  [value]="codeItem.codeId"
                  >{{ codeItem.description }}</ion-select-option
                >
              </ion-select>
            </ion-item>
          </div>

          <!-- First Name -->
          <div class="form-field">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="person-outline" class="field-icon"></ion-icon>
              <ion-input
                [readonly]="status_loading"
                labelPlacement="floating"
                label="First Name *"
                type="text"
                formControlName="givenNames"
              ></ion-input>
            </ion-item>
            <div class="error-message" *ngIf="!form.givenNames.valid && form.givenNames.touched">
              <ion-icon name="alert-circle-outline"></ion-icon>
              <span *ngFor="let error of _formValidations.doErrors(form.givenNames.errors, 'First Name')">
                {{ error }}
              </span>
            </div>
          </div>

          <!-- Surname -->
          <div class="form-field">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="person-outline" class="field-icon"></ion-icon>
              <ion-input
                labelPlacement="floating"
                label="Surname *"
                type="text"
                formControlName="surname"
                [readonly]="status_loading"
              ></ion-input>
            </ion-item>
            <div class="error-message" *ngIf="!form.surname.valid && form.surname.touched">
              <ion-icon name="alert-circle-outline"></ion-icon>
              <span *ngFor="let error of _formValidations.doErrors(form.surname.errors, 'Surname')">
                {{ error }}
              </span>
            </div>
          </div>

          <!-- Gender -->
          <div class="form-field">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="male-female-outline" class="field-icon"></ion-icon>
              <ion-select
                label="Gender"
                labelPlacement="floating"
                formControlName="gender"
                placeholder="Please select"
              >
                <ion-select-option
                  *ngFor="let codeItem of getCodeList('SEX') | async"
                  [value]="codeItem.codeId"
                  >{{ codeItem.description }}</ion-select-option
                >
              </ion-select>
            </ion-item>
          </div>

          <!-- Birth Date -->
          <div class="form-field">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="calendar-outline" class="field-icon"></ion-icon>
              <ion-input
                label="Birth Date"
                labelPlacement="floating"
                formControlName="birthDate"
                type="date"
                [max]="todaysDate12YearsAgo()"
              ></ion-input>
            </ion-item>
            <div class="error-message" *ngIf="isFormComponentInvalid('birthDate')">
              <ion-icon name="alert-circle-outline"></ion-icon>
              <span *ngFor="let error of getComponentErrors('birthDate')">
                {{ error }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Identification Section -->
      <div class="section-card">
        <h3 class="section-title">
          <ion-icon name="id-card-outline"></ion-icon>
          Identification
        </h3>
        
        <div class="form-grid">
          <!-- ID Type -->
          <div class="form-field full-width">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="id-card-outline" class="field-icon"></ion-icon>
              <ion-input
                label="ID Type"
                labelPlacement="floating"
                [value]="idType === 'nationalId' ? 'South African ID' : 'Passport'"
                readonly
              ></ion-input>
            </ion-item>
          </div>

          <!-- National ID -->
          <div class="form-field full-width" *ngIf="idType === 'nationalId' || !idType">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="id-card-outline" class="field-icon"></ion-icon>
              <ion-input
                labelPlacement="floating"
                label="South African ID Number"
                type="text"
                formControlName="nationalIdNum"
                [readonly]="true"
              >
              </ion-input>
            </ion-item>
            <div class="error-message" *ngIf="!form.nationalIdNum.valid && form.nationalIdNum.touched && (idType === 'nationalId' || !idType)">
              <ion-icon name="alert-circle-outline"></ion-icon>
              <span *ngFor="let error of _formValidations.doErrors(form.nationalIdNum.errors, 'ID number')">
                {{ error }}
              </span>
            </div>
          </div>

          <!-- Passport Fields -->
          <div class="form-field" *ngIf="idType === 'passport'">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="document-text-outline" class="field-icon"></ion-icon>
              <ion-input
                labelPlacement="floating"
                label="Passport Number"
                type="text"
                formControlName="passortNum"
                [readonly]="true"
              >
              </ion-input>
            </ion-item>
            <div class="error-message" *ngIf="!form.passortNum.valid && form.passortNum.touched && idType === 'passport'">
              <ion-icon name="alert-circle-outline"></ion-icon>
              <span *ngFor="let error of _formValidations.doErrors(form.passortNum.errors, 'Passport number')">
                {{ error }}
              </span>
            </div>
          </div>

          <div class="form-field" *ngIf="idType === 'passport'">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="flag-outline" class="field-icon"></ion-icon>
              <ion-input
                label="Country of Origin *"
                labelPlacement="floating"
                [value]="selectedCountryName"
                readonly
              ></ion-input>
            </ion-item>
          </div>

          <div class="form-field" *ngIf="idType === 'passport'">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="calendar-outline" class="field-icon"></ion-icon>
              <ion-input
                label="Passport Expiry Date *"
                labelPlacement="floating"
                formControlName="expiryDate"
                type="date"
                [min]="todaysDate()"
                [readonly]="true"
              ></ion-input>
            </ion-item>
            <div class="error-message" *ngIf="!form.expiryDate?.valid && form.expiryDate?.touched && idType === 'passport'">
              <ion-icon name="alert-circle-outline"></ion-icon>
              <span *ngFor="let error of _formValidations.doErrors(form.expiryDate?.errors, 'Passport Expiry Date')">
                {{ error }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact Information Section -->
      <div class="section-card">
        <h3 class="section-title">
          <ion-icon name="call-outline"></ion-icon>
          Contact Information
        </h3>
        
        <!-- Address Component -->
        <div class="address-section">
          <lp-pos-address
            type="POST"
            [mainAddress]="addr"
            [mainForm]="profileForm"
            #address_post
            [required_field]="true"
          ></lp-pos-address>
        </div>
        <div class="form-grid">
          <!-- Mobile Number -->
          <div class="form-field">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="phone-portrait-outline" class="field-icon"></ion-icon>
              <ion-input
                labelPlacement="floating"
                label="Mobile Number *"
                type="text"
                [value]="phoneTogether"
                disabled
              ></ion-input>
            </ion-item>
            <div class="error-message" *ngIf="!form.phone.valid && form.phone.touched">
              <ion-icon name="alert-circle-outline"></ion-icon>
              <span *ngFor="let error of _formValidations.doErrors(form.phone.errors, 'Mobile Number')">
                {{ error }}
              </span>
            </div>
          </div>

          <!-- Email -->
          <div class="form-field">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="mail-outline" class="field-icon"></ion-icon>
              <ion-input
                labelPlacement="floating"
                label="Email Address"
                type="email"
                formControlName="emailAddress"
              >
              </ion-input>
            </ion-item>
            <div class="error-message" *ngIf="!form.emailAddress.valid && form.emailAddress.touched">
              <ion-icon name="alert-circle-outline"></ion-icon>
              <span *ngFor="let error of _formValidations.doErrors(form.emailAddress.errors, 'Email')">
                {{ error }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- Favorite Store -->
        <div class="store-section">
          <lib-stores
            [favourite_id]="favourite_id"
            (updateDataEvent)="updateAddress($event)"
            [required_field]="true"
          />
        </div>
      </div>
      <!-- Save Button -->
      <div class="action-section">
        <ion-button 
          expand="block" 
          class="save-button" 
          type="submit"
          [disabled]="status_loading"
        >
          <ion-icon name="save-outline" slot="start"></ion-icon>
          Save Profile
        </ion-button>
      </div>
    </form>
  </div>
</lib-page-wrapper>
