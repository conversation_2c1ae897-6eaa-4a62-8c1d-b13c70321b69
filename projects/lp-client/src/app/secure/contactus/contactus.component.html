<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo 
      type="contact" 
      [names]="profile?.givenNames + ' ' + profile?.surname"
      [src]="lssConfig.pages.landing.loggedinIcon" 
    />
  </div>

  <!-- Contact Section -->
  <div class="contact-section">
    <!-- Quick Contact Card -->
    <div class="quick-contact-card">
      <div class="contact-header">
        <div class="contact-icon">
          <ion-icon name="call-outline"></ion-icon>
        </div>
        <div class="contact-info">
          <h3>Need Quick Help?</h3>
          <p>Call our support center</p>
        </div>
      </div>
      <a href="{{ 'tel:' + lssConfig.contact.callCenter }}" class="call-button">
        <ion-icon name="call"></ion-icon>
        <span>{{ lssConfig.contact.callCenter }}</span>
      </a>
    </div>

    <!-- Contact Form Card -->
    <div class="contact-form-card">
      <div class="form-header">
        <h2>Send us a Message</h2>
        <p>We'll get back to you within 24 hours</p>
      </div>

      <form [formGroup]="_form" class="modern-form">
        <!-- Category Select -->
        <div class="form-group">
          <label class="form-label">Category *</label>
          <ion-select 
            formControlName="category"
            placeholder="Select a category"
            class="modern-select"
            interface="popover"
            mode="ios">
            <ion-select-option 
              *ngFor="let codeItem of getCodeList(lssConfig.pages.contact.categoryCode) | async" 
              [value]="codeItem.codeId">
              {{ codeItem.description }}
            </ion-select-option>
          </ion-select>
          <span class="error-text" *ngIf="_form.get('category')?.touched && _form.get('category')?.errors?.['required']">
            Please select a category
          </span>
        </div>

        <!-- Name Fields Row -->
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">Name *</label>
            <ion-input
              type="text"
              formControlName="givenNames"
              placeholder="Enter your name"
              class="modern-input"
              mode="md"
            ></ion-input>
            <span class="error-text" *ngIf="_form.get('givenNames')?.touched && _form.get('givenNames')?.errors?.['required']">
              Name is required
            </span>
          </div>

          <div class="form-group">
            <label class="form-label">Surname *</label>
            <ion-input
              type="text"
              formControlName="surname"
              placeholder="Enter your surname"
              class="modern-input"
              mode="md"
            ></ion-input>
            <span class="error-text" *ngIf="_form.get('surname')?.touched && _form.get('surname')?.errors?.['required']">
              Surname is required
            </span>
          </div>
        </div>

        <!-- Email Field -->
        <div class="form-group">
          <label class="form-label">Email *</label>
          <ion-input
            type="email"
            formControlName="email"
            placeholder="Enter your email"
            class="modern-input"
            mode="md"
          ></ion-input>
          <span class="error-text" *ngIf="_form.get('email')?.touched && _form.get('email')?.errors?.['required']">
            Email is required
          </span>
          <span class="error-text" *ngIf="_form.get('email')?.touched && _form.get('email')?.errors?.['pattern']">
            Please enter a valid email
          </span>
        </div>

        <!-- Phone Field -->
        <div class="form-group">
          <label class="form-label">Mobile Number *</label>
          <div class="phone-input-wrapper">
            <ion-intl-tel-input 
              id="phone-number" 
              formControlName="phone" 
              [enableAutoCountrySelect]="true" 
              [selectFirstCountry]="lssConfig.telephone.selectFirstCountry" 
              [preferredCountries]="lssConfig.telephone.preferredCountries"
              class="modern-phone-input">
            </ion-intl-tel-input>
          </div>
          <span class="error-text" *ngIf="_form.get('phone')?.touched && _form.get('phone')?.errors?.['required']">
            Mobile number is required
          </span>
        </div>

        <!-- Message Field -->
        <div class="form-group">
          <label class="form-label">Message *</label>
          <ion-textarea
            formControlName="message"
            placeholder="Tell us how we can help you..."
            rows="5"
            class="modern-textarea"
            mode="md"
          ></ion-textarea>
          <span class="error-text" *ngIf="_form.get('message')?.touched && _form.get('message')?.errors?.['required']">
            Message is required
          </span>
        </div>

        <!-- Submit Button -->
        <button 
          type="button"
          class="submit-button" 
          (click)="submit()"
          [disabled]="!isFormValid()">
          <ion-icon name="send"></ion-icon>
          Send Message
        </button>
      </form>
    </div>
  </div>
</lib-page-wrapper>