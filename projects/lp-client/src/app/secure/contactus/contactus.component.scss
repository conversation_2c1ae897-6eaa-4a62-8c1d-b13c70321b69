/* Modern Contact Us Page Styles */

/* App Background */
.app-background {
  --background: var(--ion-color-base, #0072bc);
  height: 100%;
}

/* Contact Container for Better Spacing */
.contact-container {
  min-height: 100%;
  padding: 0;
  
  @media (min-width: 768px) {
    padding: 0 40px;
  }
  
  @media (min-width: 1024px) {
    padding: 0 40px;
  }
  
  @media (min-width: 1200px) {
    padding: 0 60px;
    max-width: 1600px;
    margin: 0 auto;
  }
}

/* Contact Section */
.contact-section {
  margin-top: -30px;
  padding: 0 20px 20px;
  position: relative;
  z-index: 2;
  animation: slideUp 0.6s ease-out;
}

/* Quick Contact Card */
.quick-contact-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  animation: slideIn 0.6s ease-out;
  
  .contact-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
    
    .contact-icon {
      width: 56px;
      height: 56px;
      background: linear-gradient(135deg, #4CAF50, #388E3C);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      ion-icon {
        font-size: 28px;
        color: white;
      }
    }
    
    .contact-info {
      flex: 1;
      
      h3 {
        margin: 0 0 4px 0;
        font-size: 20px;
        font-weight: 600;
        color: #212121;
      }
      
      p {
        margin: 0;
        font-size: 14px;
        color: #666;
      }
    }
  }
  
  .call-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    width: 100%;
    padding: 16px;
    background: var(--ion-color-primary, #FF6B35);
    color: white;
    border-radius: 12px;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.25);
    
    ion-icon {
      font-size: 20px;
    }
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 107, 53, 0.35);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

/* Contact Form Card */
.contact-form-card {
  background: white;
  border-radius: 20px;
  padding: 28px;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  animation: slideIn 0.6s ease-out;
  animation-delay: 0.1s;
  animation-fill-mode: both;
  
  .form-header {
    text-align: center;
    margin-bottom: 32px;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #212121;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: #666;
    }
  }
}

/* Modern Form Styles */
.modern-form {
  .form-group {
    margin-bottom: 24px;
  }
  
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    
    @media (max-width: 600px) {
      grid-template-columns: 1fr;
    }
  }
  
  .form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
    color: #424242;
  }
  
  .modern-input,
  .modern-select,
  .modern-textarea {
    width: 100%;
    --background: #f8f9fa;
    --color: #212121;  // Set text color
    --placeholder-color: #999;  // Set placeholder color
    --placeholder-opacity: 1;
    --padding-start: 16px;
    --padding-end: 16px;
    --padding-top: 12px;
    --padding-bottom: 12px;
    --border-radius: 12px;
    --border-color: #e0e0e0;
    --border-width: 2px;
    font-size: 16px;
    color: #212121;  // Fallback text color
    transition: all 0.3s ease;
    
    &:hover {
      --border-color: #ccc;
    }
    
    &.ion-focused {
      --border-color: var(--ion-color-primary, #FF6B35);
      --background: white;
      --highlight-height: 0;
      box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.1);
    }
    
    &.ion-valid {
      --border-color: #4CAF50;
    }
    
    &.ion-invalid.ion-touched {
      --border-color: #F44336;
    }
  }
  
  .modern-select {
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    background: #f8f9fa;
    
    &::part(icon) {
      color: #666;
      opacity: 1;
    }
    
    &::part(text) {
      color: #212121;
    }
    
    &::part(placeholder) {
      color: #999;
      opacity: 1;
    }
  }
  
  .modern-textarea {
    min-height: 120px;
    resize: vertical;
  }
  
  .phone-input-wrapper {
    ::ng-deep {
      .ion-intl-tel-input {
        --border-radius: 12px;
        --border-color: #e0e0e0;
        --border-width: 2px;
        background: #f8f9fa;
        border-radius: 12px;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: #ccc;
        }
        
        &.ion-focused {
          border-color: var(--ion-color-primary, #FF6B35);
          background: white;
          box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.1);
        }
        
        ion-input {
          --padding-start: 16px;
          --padding-end: 16px;
          --padding-top: 12px;
          --padding-bottom: 12px;
          --color: #212121;  // Set text color for phone input
          --placeholder-color: #999;
          --placeholder-opacity: 1;
          color: #212121;  // Fallback color
        }
        
        // Also target the input element directly
        input {
          color: #212121 !important;
        }
      }
    }
  }
  
  .error-text {
    color: #F44336;
    font-size: 12px;
    margin-top: 6px;
    display: block;
    animation: fadeIn 0.3s ease-out;
  }
}

/* Submit Button */
.submit-button {
  width: 100%;
  padding: 16px;
  background: var(--ion-color-primary, #FF6B35);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.25);
  
  ion-icon {
    font-size: 20px;
  }
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    background: var(--ion-color-primary-shade, #e55a2b);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.35);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    box-shadow: none;
  }
}


/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 375px) {
  .contact-section {
    padding: 0 16px 16px;
  }
  
  .quick-contact-card,
  .contact-form-card {
    padding: 20px;
    margin-bottom: 12px;
  }
  
  .contact-header {
    gap: 12px;
    
    .contact-icon {
      width: 48px;
      height: 48px;
      
      ion-icon {
        font-size: 24px;
      }
    }
    
    .contact-info {
      h3 {
        font-size: 18px;
      }
      
      p {
        font-size: 13px;
      }
    }
  }
  
  .form-header {
    margin-bottom: 24px;
    
    h2 {
      font-size: 20px;
    }
  }
  
  .modern-form {
    .form-group {
      margin-bottom: 20px;
    }
    
    .form-label {
      font-size: 13px;
    }
    
    .modern-input,
    .modern-select,
    .modern-textarea {
      font-size: 15px;
      --padding-start: 14px;
      --padding-end: 14px;
      --padding-top: 10px;
      --padding-bottom: 10px;
    }
  }
}