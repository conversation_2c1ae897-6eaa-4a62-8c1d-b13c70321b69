import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SecureComponent } from './secure.component';
import { ProfileComponent } from './profile/profile.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ComponentsModule } from 'mobile-components';
import { TransactionsComponent } from './transactions/transactions.component'
import { DashboardComponent } from './dashboard/dashboard.component';
import { ProductsComponent } from './products/products.component';
import { SettingsComponent } from './settings/settings.component';
import { StatementsComponent } from './statements/statements.component';
import { VirtualCardComponent } from './virtualcard/virtualcard.component';
import { SecureRoutingModule } from './secure-routing.module';
import { IonIntlTelInputModule } from 'third-party-fix';
import { ContactusComponent } from './contactus/contactus.component';
import { SecurityComponent } from './security/security.component';
import { ProfileremoveComponent } from './profileremove/profileremove.component';
import { PoolsComponent } from './pools/pools.component';
import { PointsComponent } from './points/points.component';
import { PointsTopupComponent } from './points-topup/points-topup.component';
import { NotificationSettingsComponent } from './notification-settings/notification-settings.component';
import { ClaimGiftCardComponent } from './claim-gift-card/claim-gift-card.component';

@NgModule({
  declarations: [
    SecureComponent,
    DashboardComponent,
    ProductsComponent,
    ProfileComponent,
    SettingsComponent,
    StatementsComponent,
    TransactionsComponent,
    ContactusComponent,
    VirtualCardComponent,
    SecurityComponent,
    ProfileremoveComponent,
    PoolsComponent,
    PointsComponent,
    PointsTopupComponent,
    ClaimGiftCardComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    IonIntlTelInputModule,
    ReactiveFormsModule,
    ComponentsModule,
    SecureRoutingModule,
    NotificationSettingsComponent,
  ]
})
export class SecureModule { }
