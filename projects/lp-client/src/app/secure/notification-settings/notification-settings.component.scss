/* Modern Notification Settings Styles */

/* App Background */
.app-background {
  --background: var(--ion-color-base, #0072bc);
  position: relative;
}

/* Notification Settings Container - removed custom padding as page-wrapper handles it */

/* Notification Settings Content Container - padding removed, page-wrapper handles it */
.notification-settings-content {
  position: relative;
  z-index: 2;
}

/* Page Header - removed as header is now in lib-head-logo component */

/* Section Cards */
.section-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  animation: slideUp 0.6s ease-out;
  animation-fill-mode: both;
  overflow: hidden; // Prevent form fields from overflowing
  width: 100%;
  box-sizing: border-box;

  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
  &:nth-child(4) { animation-delay: 0.3s; }
  &:nth-child(5) { animation-delay: 0.4s; }

  p {
    color: var(--ion-color-medium);
    font-size: 14px;
    line-height: 1.6;
    margin: 0 0 12px 0;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #212121;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;

  ion-icon {
    font-size: 24px;
    color: var(--ion-color-primary, #FF6B35);
  }

  .section-toggle {
    margin-left: auto;
    --padding-start: 8px;
    --padding-end: 8px;
  }
}

.section-subtitle {
  color: var(--ion-color-medium);
  font-size: 14px;
  margin: -12px 0 16px 36px;
  line-height: 1.4;
}

/* Modern Toggle Items */
.section-card ion-item {
  --background: #f8f9fa;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --inner-padding-end: 0;
  --border-width: 0;
  --inner-border-width: 0;
  border-radius: 12px;
  margin-bottom: 12px;
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  ion-toggle {
    --handle-spacing: 2px;
  }

  ion-label {
    h2 {
      font-size: 16px;
      font-weight: 500;
      color: #212121;
      margin-bottom: 4px;
    }

    p {
      font-size: 14px;
      color: var(--ion-color-medium);
      margin: 0;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

  ion-spinner {
    margin-bottom: 1rem;
  }

  p {
    color: var(--ion-color-medium);
    margin: 0;
  }
}

// Topic subscriptions section
.topic-subscriptions-section {
  margin: 2rem 0;

  ion-card-header {
    padding-bottom: 0;

    ion-card-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 1.2rem;
      font-weight: 600;

      ion-icon {
        font-size: 1.4rem;
        color: var(--ion-color-primary);
      }
    }

    ion-card-subtitle {
      margin-top: 0.5rem;
      font-size: 0.9rem;
      opacity: 0.8;
    }
  }
}

.topics-loading {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0;
  color: var(--ion-color-medium);
  font-size: 0.9rem;

  ion-spinner {
    margin-right: 0.25rem;
  }
}

.topics-list {
  .topic-item {
    margin-bottom: 0.5rem;

    ion-item {
      --padding-start: 0;
      --padding-end: 0;
      --inner-padding-end: 0;

      ion-label {
        h3 {
          margin: 0 0 0.25rem 0;
          font-weight: 500;
          color: var(--ion-text-color);
        }

        p {
          margin: 0;
          font-size: 0.85rem;
          color: var(--ion-color-medium);
          line-height: 1.3;
        }
      }

      ion-checkbox {
        margin-right: 1rem;
      }
    }
  }
}

.custom-topic-section {
  margin-top: 1.5rem;

  .custom-topic-input {
    ion-item {
      --padding-start: 0;
      --padding-end: 0;
      margin-bottom: 1rem;

      ion-input {
        --padding-start: 0;
      }

      ion-button {
        margin-left: 0.5rem;
      }
    }
  }

  .custom-topic-help {
    p {
      margin: 0;
      
      small {
        color: var(--ion-color-medium);
        line-height: 1.4;
      }
    }
  }
}

.subscribed-topics-summary {
  margin: 1.5rem 0;
  padding: 1rem;
  background: var(--ion-color-light);
  border-radius: 8px;

  p {
    margin: 0 0 1rem 0;
    font-weight: 500;
    color: var(--ion-color-dark);
  }

  ion-chip {
    margin: 0.25rem 0.5rem 0.25rem 0;
  }
}

.no-subscriptions {
  padding: 1rem;
  text-align: center;
  color: var(--ion-color-medium);
  font-style: italic;

  p {
    margin: 0;
  }
}

// FCM Token section
.fcm-content {
  .token-description {
    margin: 0 0 1rem 0;
    color: var(--ion-color-medium);
    font-size: 0.9rem;
  }

  .token-display {
    .token-content {
      margin-top: 1rem;
      padding: 1rem;
      background: var(--ion-color-light);
      border-radius: 8px;
      border: 1px solid var(--ion-color-medium-tint);

      .token-text {
        font-family: 'Courier New', monospace;
        font-size: 0.75rem;
        background: var(--ion-color-step-50);
        padding: 0.75rem;
        border-radius: 4px;
        margin-bottom: 1rem;
        word-break: break-all;
        line-height: 1.4;
        color: var(--ion-color-dark);
      }

      ion-button {
        margin: 0.25rem 0.5rem 0.25rem 0;
      }
    }
  }

  .firebase-links {
    margin-top: 1.5rem;
    padding: 1rem;
    background: var(--ion-color-warning-tint);
    border-radius: 8px;

    p {
      margin: 0 0 0.5rem 0;
      font-weight: 500;
      color: var(--ion-color-warning-shade);
    }

    .firebase-link {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--ion-color-warning-shade);
      text-decoration: none;
      font-size: 0.9rem;

      &:hover {
        text-decoration: underline;
      }

      ion-icon {
        font-size: 1rem;
      }
    }
  }
}

.topic-subscriptions-disabled {
  margin: 2rem 0;

  ion-card-content {
    text-align: center;

    p {
      margin: 0 0 1rem 0;
      color: var(--ion-color-medium);
    }
  }
}

// Device Management section
.device-content {

  .device-actions {
    margin-bottom: 1rem;
    display: flex;
    gap: 0.5rem;
  }

  .device-loading {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 0;
    color: var(--ion-color-medium);
    font-size: 0.9rem;

    ion-spinner {
      margin-right: 0.25rem;
    }
  }

  .device-list {
    .device-item {
      margin-bottom: 0.5rem;

      ion-item {
        --padding-start: 0;
        --padding-end: 0;
        --inner-padding-end: 0;
        border-radius: 8px;
        margin-bottom: 0.5rem;

        ion-avatar {
          .device-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--ion-color-primary-tint);
            border-radius: 50%;

            ion-icon {
              font-size: 1.2rem;
              color: var(--ion-color-primary);
            }
          }
        }

        ion-label {
          h3 {
            margin: 0 0 0.25rem 0;
            font-weight: 500;
            color: var(--ion-text-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;

            ion-chip {
              font-size: 0.7rem;
              height: 1.5rem;
            }
          }

          p {
            margin: 0 0 0.25rem 0;
            font-size: 0.8rem;
            color: var(--ion-color-medium);
            line-height: 1.3;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        ion-button {
          margin-left: 0.25rem;

          &[color="danger"] {
            --color: var(--ion-color-danger);
          }

          &[color="medium"] {
            --color: var(--ion-color-medium);
          }
        }
      }
    }
  }

  .device-error {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--ion-color-danger-tint);
    border: 1px solid var(--ion-color-danger);
    border-radius: 8px;
    margin: 1rem 0;

    ion-icon {
      font-size: 1.5rem;
      color: var(--ion-color-danger);
      margin-top: 0.25rem;
      min-width: 1.5rem;
    }

    .error-content {
      flex: 1;

      h4 {
        margin: 0 0 0.5rem 0;
        color: var(--ion-color-danger-shade);
        font-size: 1rem;
        font-weight: 600;
      }

      p {
        margin: 0 0 1rem 0;
        color: var(--ion-color-danger-shade);
        font-size: 0.9rem;
        line-height: 1.4;
      }

      ion-button {
        --color: var(--ion-color-danger);
        --border-color: var(--ion-color-danger);
      }
    }
  }

  .no-devices {
    .empty-state {
      text-align: center;
      padding: 2rem 1rem;
      color: var(--ion-color-medium);

      ion-icon {
        margin-bottom: 1rem;
        opacity: 0.5;
      }

      h3 {
        margin: 0 0 0.5rem 0;
        font-size: 1.1rem;
        font-weight: 500;
      }

      p {
        margin: 0 0 1.5rem 0;
        font-size: 0.9rem;
        line-height: 1.4;
      }
    }
  }

  .device-info {
    margin-top: 1.5rem;
    padding: 1rem;
    background: var(--ion-color-light);
    border-radius: 8px;

    ion-note {
      p {
        margin: 0;
        font-size: 0.85rem;
        line-height: 1.4;
        color: var(--ion-color-medium);
      }
    }
  }
}

// Info section uses section-card styling

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .subscribed-topics-summary {
    background: var(--ion-color-dark-tint);

    p {
      color: var(--ion-color-light);
    }
  }

  .token-display .token-content {
    background: var(--ion-color-dark-shade);
    border-color: var(--ion-color-step-200);

    .token-text {
      background: var(--ion-color-step-100);
      color: var(--ion-color-light);
    }
  }

  .firebase-links {
    background: rgba(255, 204, 0, 0.1);
  }

  .device-management-section {
    .device-info {
      background: var(--ion-color-dark-shade);

      ion-note p {
        color: var(--ion-color-light-shade);
      }
    }

    .device-list .device-item ion-item ion-avatar .device-icon {
      background: var(--ion-color-primary-shade);
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .notification-settings-content {
    padding: 0 12px 20px;
  }

  .section-card {
    padding: 20px 16px;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 16px;
  }

  .token-display .token-content .token-text {
    font-size: 0.7rem;
    padding: 0.5rem;
  }
}

/* Override default Ionic styles */
ion-item {
  --min-height: 56px;
  --border-width: 0;
  --inner-border-width: 0;
  --border-color: transparent;
}

/* Global fix for all ion-items in notification settings */
.notification-settings-content {
  ion-item {
    --border-width: 0;
    --inner-border-width: 0;
    --border-color: transparent;
    --border-style: none;
  }
}

/* Communication Preferences Form Styles */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  width: 100%;
  overflow: hidden;
}

.form-field {
  position: relative;
  width: 100%;
  min-width: 0; // Critical for preventing overflow in grid items

  &.full-width {
    grid-column: 1 / -1;
  }
}

/* Modern Input Styling */
.modern-input {
  --background: #f8f9fa;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --inner-padding-end: 0;
  --border-width: 0;
  --inner-border-width: 0;
  border-radius: 12px;
  margin-bottom: 0;
  transition: all 0.3s ease;
  width: 100%;
  overflow: hidden;

  &::part(native) {
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
  }

  &.ion-focused::part(native) {
    border-color: var(--ion-color-primary, #FF6B35);
    background: white;
  }

  &.ion-invalid.ion-touched::part(native) {
    border-color: var(--ion-color-danger, #F44336);
  }

  ion-input,
  ion-select {
    --placeholder-opacity: 0.5;
    font-size: 16px;
    width: 100%;
    max-width: 100%;
  }

  .field-icon {
    font-size: 20px;
    color: #666;
    margin-right: 8px;
  }
}

/* Error Messages */
.error-message {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--ion-color-danger, #F44336);
  font-size: 12px;
  margin-top: 4px;
  padding: 0 16px;
  animation: fadeIn 0.3s ease;

  ion-icon {
    font-size: 14px;
    flex-shrink: 0;
  }
}


/* Action Section */
.action-section {
  margin-top: 32px;
}

.button-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.save-button {
  --background: var(--ion-color-primary, #FF6B35);
  --background-activated: var(--ion-color-primary-shade, #e85d2f);
  --background-hover: var(--ion-color-primary-tint, #ff7a49);
  --border-radius: 16px;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;

  ion-icon {
    font-size: 20px;
    margin-right: 8px;
  }

  &:hover:not([disabled]) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
  }

  &:active:not([disabled]) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
  }

  &[disabled] {
    --background: #e0e0e0;
    --color: #999;
    box-shadow: none;
    opacity: 0.6;
  }
}

.cancel-button {
  --border-radius: 16px;
  --border-color: #666;
  --border-width: 2px;
  --color: #666;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  text-transform: none;
  transition: all 0.3s ease;

  &:hover:not([disabled]) {
    --background: #f5f5f5;
  }

  &[disabled] {
    opacity: 0.6;
  }
}

/* Responsive Design for Communication Preferences */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .modern-input {
    ion-select {
      max-width: 100%;
      white-space: normal;
      
      &::part(label) {
        white-space: normal;
        overflow-wrap: break-word;
      }
    }
  }

  .button-group {
    grid-template-columns: 1fr;
    
    .save-button {
      order: 1;
    }
    
    .cancel-button {
      order: 2;
    }
  }
}

@media (max-width: 375px) {
  .modern-input {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .save-button,
  .cancel-button {
    height: 48px;
    font-size: 15px;
  }
}