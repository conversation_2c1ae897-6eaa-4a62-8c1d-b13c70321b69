import { Component, Injector, OnInit, ChangeDetectorRef } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  AbstractControlOptions,
} from '@angular/forms';
import {
  CustomValidators,
  LPMemberEntityTools,
  MemberProfile,
  MemberService,
  ValidationService,
  KeyCloakService,
  Address,
  LssConfig,
} from 'lp-client-api';
import { environment } from '../../../environments/environment';
import { Router } from '@angular/router';
import { AbstractFormComponent } from 'mobile-components';

@Component({
  selector: 'app-security',
  templateUrl: './security.component.html',
  styleUrls: ['./security.component.scss'],
})
export class SecurityComponent extends AbstractFormComponent<MemberProfile> {
  profile?: MemberProfile;
  profileForm: FormGroup;
  profileFormPassword: FormGroup;
  validated: boolean = false;
  validatedPassword: boolean = false;
  status_loading: boolean = true;

  constructor(
    injector: Injector,
    private memberService: MemberService,
    public override _formValidations: ValidationService,
    public override _formBuilder: FormBuilder,
    private kc: KeyCloakService,
    protected readonly router: Router,
    public lssConfig: LssConfig
  ) {
    super(injector);

    this.profileForm = this._formBuilder.group({
      pin: [
        '',
        [Validators.required, Validators.minLength(5), Validators.maxLength(5)],
      ],
    });

    this.profileFormPassword = this._formBuilder.group(
      {
        password: [
          '',
          Validators.compose([
            Validators.required,
            CustomValidators.passwordValidate(),
          ]),
        ],
        passwordConfirm: ['', [Validators.required]],
      },
      {
        validators: Validators.compose([
          CustomValidators.matchValue(
            'password',
            'Password',
            'passwordConfirm',
            'Confirm Password'
          ),
        ]),
      } as AbstractControlOptions
    );
  }
  environment = environment;

  ionViewWillEnter() {
    this.loading = true;

    // console.log('MER', MergedClass)
    this.showLoadingModal('Fetching updated Account').then(() => {
      this.memberService
        .getProfile(this.kc.lpUniueReference, true)
        .subscribe((data) => {
          this.dismissLoadingModal();
          this.profile = data;
        });
    });
  }

  override get form(): any {
    return this.profileForm.controls;
  }
  get isValid(): boolean {
    return this.profileForm.valid;
  }
  get isValidPassword(): boolean {
    return this.profileFormPassword.valid;
  }

  doPassword() {
    console.log('do Pass');
    this.kc.changePassword();
  }
  doLoad() {
    console.log('do load');
    if (!this.profileForm.valid) {
      this.profileForm.markAllAsTouched();
      return;
    }

    let payload = this.profileForm.value;

    console.log('payload', payload);

    this.showLoadingModal('Updating your account').then(() => {
      this.memberService
        .updatePin(this.kc.lpUniueReference, payload.pin)
        .subscribe({
          error: (error) => {
            console.log('error', error);
            console.log('error', error.message);

            this.dismissLoadingModal();
            this.presentToast({
              message: 'Oops, something went wrong!',
              color: 'danger',
              position: 'bottom',
            }).then();

            // this._formState = this._formStateType.fail;
            // this.error = error.error.detail;
          },
          next: (body) => {
            console.log('BOdy', body);
            this.dismissLoadingModal();
            this.presentToast({
              message: 'Your account has been updated',
              position: 'bottom',
            }).then();
            this.profileForm = body;
            this.router.navigate(['/app/account']);
          },
        });
    });
    // });
  }
}
