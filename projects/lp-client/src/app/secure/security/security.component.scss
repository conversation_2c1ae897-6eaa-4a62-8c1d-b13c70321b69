/* Modern Security Page Styles */

/* App Background */
.app-background {
  --background: var(--ion-color-base, #0072bc);
  height: 100%;
}

/* Security Container for Better Spacing */
.security-container {
  min-height: 100%;
  padding: 0;
  
  @media (min-width: 768px) {
    padding: 0 40px;
  }
  
  @media (min-width: 1024px) {
    padding: 0 40px;
  }
  
  @media (min-width: 1200px) {
    padding: 0 60px;
    max-width: 1600px;
    margin: 0 auto;
  }
}

/* Security Section */
.security-section {
  margin-top: -30px;
  padding: 0 20px 20px;
  position: relative;
  z-index: 2;
  animation: slideUp 0.6s ease-out;
}

/* Security Cards */
.security-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  animation: slideIn 0.6s ease-out;
  animation-fill-mode: both;
  
  &.pin-card { animation-delay: 0.1s; }
  &.password-card { animation-delay: 0.2s; }
  &.tips-card { animation-delay: 0.3s; }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  
  .card-icon {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    
    ion-icon {
      font-size: 28px;
      color: white;
    }
    
    &.pin {
      background: linear-gradient(135deg, #2196F3, #1976D2);
    }
    
    &.password {
      background: linear-gradient(135deg, #2196F3, #1976D2);
    }
    
    &.tips {
      background: linear-gradient(135deg, #2196F3, #1976D2);
    }
  }
  
  .card-info {
    flex: 1;
    
    h3 {
      margin: 0 0 4px 0;
      font-size: 20px;
      font-weight: 600;
      color: #212121;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: #666;
      line-height: 1.4;
    }
  }
}

/* Form Content */
.form-content {
  margin-top: 20px;
}

.input-container {
  margin-bottom: 20px;
}

.modern-input {
  --background: #f5f5f5;
  --padding-start: 16px;
  --padding-end: 16px;
  --inner-padding-end: 0;
  border-radius: 12px;
  margin-bottom: 8px;
  
  ion-icon {
    color: #666;
    margin-right: 12px;
    font-size: 20px;
  }
  
  ion-input {
    --color: #212121;
    --placeholder-color: #999;
    --padding-top: 16px;
    --padding-bottom: 16px;
    font-size: 16px;
    
    &[type="tel"] {
      letter-spacing: 4px;
      font-weight: 600;
    }
  }
  
  &:hover {
    --background: #eeeeee;
  }
  
  &.ion-focused {
    --background: white;
    box-shadow: 0 0 0 2px var(--ion-color-primary);
  }
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #F44336;
  font-size: 13px;
  padding: 8px 12px;
  background: rgba(244, 67, 54, 0.1);
  border-radius: 8px;
  animation: fadeIn 0.3s ease-out;
  
  ion-icon {
    font-size: 16px;
    flex-shrink: 0;
  }
}

/* Update Buttons */
.update-btn {
  --background: var(--ion-color-primary, #FF6B35);
  --border-radius: 12px;
  --box-shadow: 0 4px 12px rgba(255, 107, 53, 0.25);
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.5px;
  height: 48px;
  transition: all 0.3s ease;
  
  ion-icon {
    font-size: 20px;
    margin-right: 8px;
  }
  
  &.pin-btn {
    --background: #f5821f;
    --box-shadow: 0 4px 12px rgba(245, 130, 31, 0.3);
  }
  
  &.password-btn {
    --background: #f5821f;
    --box-shadow: 0 4px 12px rgba(245, 130, 31, 0.3);
  }
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    --box-shadow: 0 6px 20px rgba(245, 130, 31, 0.4);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    --background: #cccccc;
    --box-shadow: none;
    opacity: 0.6;
  }
}

/* Password Info */
.password-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: rgba(33, 150, 243, 0.1);
  border-radius: 12px;
  margin-bottom: 20px;
  font-size: 14px;
  color: #1976D2;
  line-height: 1.5;
  
  ion-icon {
    font-size: 20px;
    flex-shrink: 0;
  }
}

/* Security Tips */
.tips-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  
  .tip-icon {
    color: #4CAF50;
    font-size: 20px;
    flex-shrink: 0;
    margin-top: 2px;
  }
  
  span {
    font-size: 15px;
    color: #424242;
    line-height: 1.5;
  }
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 200px);
  animation: fadeIn 0.4s ease-out;
  
  ion-spinner {
    --color: white;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    color: white;
    margin: 0;
  }
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 375px) {
  .security-section {
    padding: 0 16px 16px;
  }
  
  .security-card {
    padding: 20px;
    margin-bottom: 12px;
  }
  
  .card-header {
    gap: 12px;
    
    .card-icon {
      width: 48px;
      height: 48px;
      
      ion-icon {
        font-size: 24px;
      }
    }
    
    .card-info {
      h3 {
        font-size: 18px;
      }
      
      p {
        font-size: 13px;
      }
    }
  }
  
  .update-btn {
    font-size: 15px;
    height: 44px;
    
    ion-icon {
      font-size: 18px;
    }
  }
  
  .tip-item {
    gap: 10px;
    
    .tip-icon {
      font-size: 18px;
    }
    
    span {
      font-size: 14px;
    }
  }
}