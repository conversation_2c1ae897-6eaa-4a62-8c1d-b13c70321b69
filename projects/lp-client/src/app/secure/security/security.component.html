<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo
      [names]="profile?.givenNames + ' ' + profile?.surname"
      type="security"
      [balance]="profile?.currentBalance"
      [src]="lssConfig.pages.landing.loggedinIcon"
    />
  </div>

    <!-- Security Settings Section -->
    <div class="security-section">
    <!-- PIN Management Card -->
    <div class="security-card pin-card">
      <div class="card-header">
        <div class="card-icon pin">
          <ion-icon name="keypad-outline"></ion-icon>
        </div>
        <div class="card-info">
          <h3>PIN Code</h3>
          <p>Secure your transactions with a 5-digit PIN</p>
        </div>
      </div>
      
      <form [formGroup]="profileForm" (ngSubmit)="doLoad()">
        <div class="form-content">
          <div class="input-container">
            <ion-item lines="none" class="modern-input">
              <ion-icon slot="start" name="lock-closed-outline"></ion-icon>
              <ion-input
                labelPlacement="floating"
                label="Enter New PIN"
                type="tel"
                formControlName="pin"
                maxlength="5"
                placeholder="• • • • •"
              ></ion-input>
            </ion-item>
            
            <div class="error-message" *ngIf="isFormComponentInvalid('pin')">
              <ion-icon name="information-circle-outline"></ion-icon>
              <span>PIN must be exactly 5 digits</span>
            </div>
          </div>
          
          <ion-button 
            expand="block" 
            class="update-btn pin-btn" 
            type="submit"
            [disabled]="!isValid"
          >
            <ion-icon slot="start" name="shield-checkmark-outline"></ion-icon>
            Update PIN
          </ion-button>
        </div>
      </form>
    </div>

    <!-- Password Management Card -->
    <div class="security-card password-card">
      <div class="card-header">
        <div class="card-icon password">
          <ion-icon name="key-outline"></ion-icon>
        </div>
        <div class="card-info">
          <h3>Password</h3>
          <p>Manage your account password</p>
        </div>
      </div>
      
      <form [formGroup]="profileFormPassword" (ngSubmit)="doPassword()">
        <div class="form-content">
          <p class="password-info">
            <ion-icon name="information-circle"></ion-icon>
            You'll be redirected to our secure password change portal
          </p>
          
          <ion-button 
            expand="block" 
            class="update-btn password-btn" 
            type="submit"
          >
            <ion-icon slot="start" name="lock-open-outline"></ion-icon>
            Change Password
          </ion-button>
        </div>
      </form>
    </div>

    <!-- Security Tips Card -->
    <div class="security-card tips-card">
      <div class="card-header">
        <div class="card-icon tips">
          <ion-icon name="bulb-outline"></ion-icon>
        </div>
        <div class="card-info">
          <h3>Security Tips</h3>
          <p>Keep your account safe</p>
        </div>
      </div>
      
      <div class="tips-content">
        <div class="tip-item">
          <ion-icon name="checkmark-circle" class="tip-icon"></ion-icon>
          <span>Never share your PIN with anyone</span>
        </div>
        <div class="tip-item">
          <ion-icon name="checkmark-circle" class="tip-icon"></ion-icon>
          <span>Use a unique PIN that's not your birthday</span>
        </div>
        <div class="tip-item">
          <ion-icon name="checkmark-circle" class="tip-icon"></ion-icon>
          <span>Change your password regularly</span>
        </div>
        <div class="tip-item">
          <ion-icon name="checkmark-circle" class="tip-icon"></ion-icon>
          <span>Enable two-factor authentication when available</span>
        </div>
      </div>
    </div>
  </div>

    <!-- Loading State -->
    <div class="loading-state" *ngIf="status_loading && !profile">
      <ion-spinner name="crescent"></ion-spinner>
      <p>Loading security settings...</p>
    </div>
</lib-page-wrapper>