import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { ToastController, AlertController } from '@ionic/angular';
import { Router } from '@angular/router';
import { LssConfig, MemberService, MemberProfile } from 'lp-client-api';

@Component({
  selector: 'app-claim-gift-card',
  templateUrl: './claim-gift-card.component.html',
  styleUrls: ['./claim-gift-card.component.scss']
})
export class ClaimGiftCardComponent implements OnInit {
  claimGiftCardForm: FormGroup;
  status_loading = false;
  profile: MemberProfile | null = null;

  constructor(
    private formBuilder: FormBuilder,
    private toastController: ToastController,
    private alertController: AlertController,
    private router: Router,
    public lssConfig: LssConfig,
    private memberService: MemberService
  ) {
    this.claimGiftCardForm = this.formBuilder.group({
      giftCardCode: ['', [Validators.required, Validators.minLength(8), this.giftCardCodeValidator]]
    });
  }

  ngOnInit() {
    // Subscribe to profile updates
    this.memberService.profileSubject.subscribe((data) => {
      if (data) {
        this.profile = data;
      }
    });
  }

  /**
   * Custom validator for gift card code format
   */
  giftCardCodeValidator(control: any) {
    const value = control.value;
    if (!value) return null;
    
    // Remove spaces and convert to uppercase for validation
    const cleanCode = value.replace(/\s/g, '').toUpperCase();
    
    // Basic format validation - adjust pattern as needed
    const pattern = /^[A-Z0-9]{8,16}$/;
    
    if (!pattern.test(cleanCode)) {
      return { invalidFormat: true };
    }
    
    return null;
  }

  /**
   * Format gift card code as user types
   */
  onGiftCardCodeInput(event: any) {
    let value = event.target.value.replace(/\s/g, '').toUpperCase();
    
    // Add spaces every 4 characters for better readability
    value = value.replace(/.{4}/g, '$& ').trim();
    
    // Update form control with formatted value
    this.claimGiftCardForm.patchValue({
      giftCardCode: value
    });
  }

  /**
   * Get error message for gift card code
   */
  getGiftCardCodeError(): string {
    const control = this.claimGiftCardForm.get('giftCardCode');
    if (control?.hasError('required')) {
      return 'Gift card code is required';
    }
    if (control?.hasError('minlength')) {
      return 'Gift card code must be at least 8 characters';
    }
    if (control?.hasError('invalidFormat')) {
      return 'Please enter a valid gift card code';
    }
    return '';
  }

  /**
   * Claim the gift card
   */
  async claimGiftCard() {
    if (this.claimGiftCardForm.valid) {
      this.status_loading = true;
      
      try {
        const giftCardCode = this.claimGiftCardForm.get('giftCardCode')?.value.replace(/\s/g, '');
        
        // Here you would typically call a service to claim the gift card
        // For now, we'll simulate the API call
        console.log('Claiming gift card with code:', giftCardCode);
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Show success alert
        await this.showSuccessAlert();
        
        // Reset form
        this.claimGiftCardForm.reset();
        
      } catch (error) {
        console.error('Error claiming gift card:', error);
        await this.showErrorAlert('Failed to claim gift card. Please check the code and try again.');
      } finally {
        this.status_loading = false;
      }
    } else {
      const toast = await this.toastController.create({
        message: 'Please enter a valid gift card code',
        duration: 3000,
        position: 'top',
        color: 'warning'
      });
      await toast.present();
    }
  }

  /**
   * Show success alert
   */
  async showSuccessAlert() {
    const alert = await this.alertController.create({
      header: 'Gift Card Claimed!',
      message: 'Your gift card has been successfully claimed and credited to your account.',
      buttons: [
        {
          text: 'View Balance',
          handler: () => {
            this.router.navigate(['/secure/dashboard']);
          }
        },
        {
          text: 'Claim Another',
          role: 'cancel'
        }
      ]
    });
    
    await alert.present();
  }

  /**
   * Show error alert
   */
  async showErrorAlert(message: string) {
    const alert = await this.alertController.create({
      header: 'Error',
      message: message,
      buttons: ['OK']
    });
    
    await alert.present();
  }

  /**
   * Clear the form
   */
  clearForm() {
    this.claimGiftCardForm.reset();
  }
}
