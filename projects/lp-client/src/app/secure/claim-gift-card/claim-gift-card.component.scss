/* Claim Gift Card Styles - Following Profile Component Pattern */

/* App Background */
.app-background {
  --background: var(--ion-color-base, #0072bc);
  position: relative;
}

/* Claim Gift Card Container for Better Spacing */
.claim-gift-card-container {
  min-height: 100%;
  padding: 0;
  
  @media (min-width: 768px) {
    padding: 0 40px;
  }
  
  @media (min-width: 1024px) {
    padding: 0 40px;
  }
  
  @media (min-width: 1200px) {
    padding: 0 60px;
    max-width: 1600px;
    margin: 0 auto;
  }
}

/* Profile Content Container */
.profile-content {
  padding: 0 16px 24px;
  margin-top: -30px;
  position: relative;
  z-index: 2;
}

/* Section Cards */
.section-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  animation: slideUp 0.6s ease-out;
  animation-fill-mode: both;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #212121;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 12px;

  ion-icon {
    font-size: 24px;
    color: var(--ion-color-primary, #FF6B35);
  }
}

/* Info Alert */
.info-alert {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 12px;
  margin-bottom: 24px;

  ion-icon {
    font-size: 20px;
    color: #2196f3;
    margin-top: 2px;
    flex-shrink: 0;
  }

  .info-content {
    flex: 1;

    p {
      margin: 0;
      color: #1976d2;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

/* Form Grid Layout */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.form-field {
  position: relative;

  &.full-width {
    grid-column: 1 / -1;
  }
}

/* Modern Input Styling */
.modern-input {
  --background: #f8f9fa;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --inner-padding-end: 0;
  --border-width: 0;
  --inner-border-width: 0;
  border-radius: 12px;
  margin-bottom: 0;
  transition: all 0.3s ease;

  &::part(native) {
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
  }

  &.ion-focused::part(native) {
    border-color: var(--ion-color-primary, #FF6B35);
    background: white;
  }

  &.ion-invalid.ion-touched::part(native) {
    border-color: var(--ion-color-danger, #F44336);
  }

  ion-input {
    --placeholder-opacity: 0.5;
    font-size: 16px;
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
  }

  .field-icon {
    font-size: 20px;
    color: #666;
    margin-right: 8px;
  }
}

/* Error Messages */
.error-message {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--ion-color-danger, #F44336);
  font-size: 12px;
  margin-top: 4px;
  padding: 0 16px;
  animation: fadeIn 0.3s ease;

  ion-icon {
    font-size: 14px;
    flex-shrink: 0;
  }
}

/* Help Text */
.help-text {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 12px;
  margin-top: 4px;
  padding: 0 16px;

  ion-icon {
    font-size: 14px;
    flex-shrink: 0;
  }
}

/* Action Section */
.action-section {
  margin-top: 32px;
  padding: 0 16px;
}

.claim-button {
  --background: var(--ion-color-primary, #FF6B35);
  --background-activated: var(--ion-color-primary-shade, #e85d2f);
  --background-hover: var(--ion-color-primary-tint, #ff7a49);
  --border-radius: 16px;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;

  ion-icon,
  ion-spinner {
    font-size: 20px;
    margin-right: 8px;
  }

  &:hover:not([disabled]) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
  }

  &:active:not([disabled]) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
  }

  &[disabled] {
    --background: #e0e0e0;
    --color: #999;
    box-shadow: none;
    opacity: 0.6;
  }
}

/* Info Section */
.info-section {
  margin-top: 32px;
}

.info-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  animation: slideUp 0.6s ease-out;
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

.info-title {
  font-size: 16px;
  font-weight: 600;
  color: #212121;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;

  ion-icon {
    font-size: 20px;
    color: var(--ion-color-primary, #FF6B35);
  }
}

.info-list {
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  ion-icon {
    font-size: 16px;
    color: #4caf50;
    margin-top: 2px;
    flex-shrink: 0;
  }

  p {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
  }
}

.contact-info {
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
  text-align: center;

  p {
    margin: 0 0 8px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;

    &:first-child {
      font-weight: 600;
      color: #212121;
    }

    &:last-of-type {
      margin-bottom: 16px;
    }
  }
}

.contact-button {
  --border-radius: 12px;
  --border-color: var(--ion-color-primary, #FF6B35);
  --border-width: 2px;
  --color: var(--ion-color-primary, #FF6B35);
  height: 40px;
  font-size: 14px;
  font-weight: 600;
  text-transform: none;
  transition: all 0.3s ease;

  ion-icon {
    font-size: 16px;
    margin-right: 6px;
  }

  &:hover:not([disabled]) {
    --background: var(--ion-color-primary-tint, #ff7a49);
    --color: white;
  }
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Override default Ionic styles */
ion-item {
  --min-height: 56px;
  --border-width: 0;
  --inner-border-width: 0;
  --border-color: transparent;
}

/* Fix for readonly inputs */
ion-input[readonly] {
  opacity: 0.7;
  
  &::part(native) {
    background: #f0f0f0;
  }
}

/* Global fix for all ion-items in claim gift card */
.profile-content {
  ion-item {
    --border-width: 0;
    --inner-border-width: 0;
    --border-color: transparent;
    --border-style: none;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 375px) {
  .profile-content {
    padding: 0 12px 20px;
  }

  .section-card,
  .info-card {
    padding: 20px 16px;
  }

  .section-title,
  .info-title {
    font-size: 16px;
    margin-bottom: 16px;
  }

  .modern-input {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .claim-button {
    height: 48px;
    font-size: 15px;
  }

  .info-alert {
    padding: 12px;
  }
}

/* Loading State */
.claim-button ion-spinner {
  color: white;
}

/* Success/Error Alert Customization - These would be applied globally */
.alert-wrapper {
  .alert-head {
    text-align: center;
  }
  
  .alert-message {
    text-align: center;
    font-size: 16px;
    line-height: 1.5;
  }
  
  .alert-button {
    font-weight: 600;
  }
}