<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo
      [names]="profile ? (profile.givenNames || '') + ' ' + (profile.surname || '') : ''"
      [membership]="profile?.newMembershipNumber"
      type="giftcard"
      [balance]="profile?.currentBalance"
      [src]="lssConfig.pages.landing.loggedinIcon"
    />
  </div>
  
  <!-- Claim Gift Card Content -->
  <div class="profile-content">
    <form [formGroup]="claimGiftCardForm">
      <!-- Claim Gift Card Section -->
      <div class="section-card">
        <h3 class="section-title">
          <ion-icon name="card-outline"></ion-icon>
          Claim Gift Card
        </h3>
        
        <!-- Info Alert -->
        <div class="info-alert">
          <ion-icon name="information-circle-outline"></ion-icon>
          <div class="info-content">
            <p>Enter the unique number found on your gift card to credit your account (case sensitive).</p>
          </div>
        </div>
        
        <div class="form-grid">
          <!-- Gift Card Code -->
          <div class="form-field full-width">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="card-outline" class="field-icon"></ion-icon>
              <ion-input
                label="Gift Card Code *"
                labelPlacement="floating"
                type="text"
                formControlName="giftCardCode"
                placeholder="Enter gift card code"
                maxlength="19"
                (ionInput)="onGiftCardCodeInput($event)"
                [readonly]="status_loading"
              ></ion-input>
            </ion-item>
            <div class="error-message" *ngIf="claimGiftCardForm.get('giftCardCode')?.invalid && claimGiftCardForm.get('giftCardCode')?.touched">
              <ion-icon name="alert-circle-outline"></ion-icon>
              <span>{{ getGiftCardCodeError() }}</span>
            </div>
            <div class="help-text">
              <ion-icon name="help-circle-outline"></ion-icon>
              <span>Example format: ABCD 1234 EFGH 5678</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-section">
        <ion-button 
          expand="block" 
          class="claim-button" 
          (click)="claimGiftCard()"
          [disabled]="status_loading || !claimGiftCardForm.valid"
        >
          <ion-spinner *ngIf="status_loading" name="crescent" size="small" slot="start"></ion-spinner>
          <ion-icon *ngIf="!status_loading" name="card-outline" slot="start"></ion-icon>
          {{ status_loading ? 'Claiming...' : 'Claim Gift Card' }}
        </ion-button>
      </div>
    </form>

    <!-- Additional Information -->
    <div class="info-section">
      <div class="info-card">
        <h4 class="info-title">
          <ion-icon name="help-circle-outline"></ion-icon>
          Need Help?
        </h4>
        <div class="info-list">
          <div class="info-item">
            <ion-icon name="checkmark-circle-outline"></ion-icon>
            <p>Gift card codes are case sensitive</p>
          </div>
          <div class="info-item">
            <ion-icon name="checkmark-circle-outline"></ion-icon>
            <p>Enter the code exactly as shown on your gift card</p>
          </div>
          <div class="info-item">
            <ion-icon name="checkmark-circle-outline"></ion-icon>
            <p>Codes typically contain 8-16 characters</p>
          </div>
          <div class="info-item">
            <ion-icon name="checkmark-circle-outline"></ion-icon>
            <p>Balance will be credited immediately upon successful claim</p>
          </div>
        </div>
        
        <div class="contact-info">
          <p><strong>Still having trouble?</strong></p>
          <p>Contact our support team for assistance with your gift card.</p>
          <ion-button 
            fill="outline" 
            size="small" 
            class="contact-button"
            [routerLink]="['/secure/contactus']"
          >
            <ion-icon name="mail-outline" slot="start"></ion-icon>
            Contact Support
          </ion-button>
        </div>
      </div>
    </div>
  </div>
</lib-page-wrapper>
