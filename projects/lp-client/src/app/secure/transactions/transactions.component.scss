/* Modern Transactions Styles */

/* Transactions Section */
.transactions-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Filter Container */
.filter-container {
  background: white;
  padding: 16px 20px;
  border-radius: 24px 24px 0 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  animation: slideDown 0.5s ease-out;
  flex-shrink: 0;
}

.filter-scroll {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

/* Filter Pills */
.filter-pill {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 18px;
  border: 2px solid #e0e0e0;
  border-radius: 24px;
  background: white;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  
  ion-icon {
    font-size: 18px;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &.active {
    background: var(--ion-color-primary, #FF6B35);
    color: white;
    border-color: var(--ion-color-primary, #FF6B35);
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
  }
  
  &.success.active {
    background: #28a745;
    border-color: #28a745;
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
  }
  
  &.danger.active {
    background: #dc3545;
    border-color: #dc3545;
    box-shadow: 0 4px 16px rgba(220, 53, 69, 0.3);
  }
  
  &.warning.active {
    background: #ffc107;
    border-color: #ffc107;
    color: #212121;
    box-shadow: 0 4px 16px rgba(255, 193, 7, 0.3);
  }
  
  &.dark.active {
    background: #343a40;
    border-color: #343a40;
    box-shadow: 0 4px 16px rgba(52, 58, 64, 0.3);
  }
}

/* Transactions Container */
.transactions-container {
  background: #f5f7fa;
  padding: 24px 20px;
  border-radius: 0 0 24px 24px;
  overflow-y: auto;
  flex: 1;
  -webkit-overflow-scrolling: touch;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  animation: fadeIn 0.6s ease-out;
  
  .empty-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 24px;
    background: linear-gradient(135deg, #e8ecf1 0%, #f5f7fa 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    ion-icon {
      font-size: 48px;
      color: #c0c8d0;
    }
  }
  
  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #212121;
    margin: 0 0 8px 0;
  }
  
  p {
    font-size: 16px;
    color: #666;
    margin: 0;
  }
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 60px 20px;
  animation: fadeIn 0.4s ease-out;
  
  ion-spinner {
    --color: var(--ion-color-primary, #FF6B35);
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    color: #666;
    margin: 0;
  }
}

/* Transactions List */
.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Transaction Card */
.transaction-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
  
  &.slide-in {
    animation: slideIn 0.5s ease-out forwards;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
}

/* Transaction Icon */
.transaction-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: rgba(255, 107, 53, 0.1);
  transition: transform 0.3s ease;
  
  ion-icon {
    font-size: 24px;
    color: var(--ion-color-primary, #FF6B35);
  }
  
  &.success {
    background: rgba(40, 167, 69, 0.1);
    
    ion-icon {
      color: #28a745;
    }
  }
  
  &.danger {
    background: rgba(220, 53, 69, 0.1);
    
    ion-icon {
      color: #dc3545;
    }
  }
  
  &.warning {
    background: rgba(255, 193, 7, 0.1);
    
    ion-icon {
      color: #ffc107;
    }
  }
  
  &.dark {
    background: rgba(52, 58, 64, 0.1);
    
    ion-icon {
      color: #343a40;
    }
  }
  
  &.tertiary {
    background: rgba(111, 66, 193, 0.1);
    
    ion-icon {
      color: #6f42c1;
    }
  }
  
  &.medium {
    background: rgba(108, 117, 125, 0.1);
    
    ion-icon {
      color: #6c757d;
    }
  }
}

/* Transaction Info */
.transaction-info {
  flex: 1;
  min-width: 0;
  
  .transaction-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #212121;
    }
    
    .reversal-badge {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      background: #343a40;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 600;
      
      ion-icon {
        font-size: 12px;
      }
    }
  }
  
  .transaction-description {
    margin: 0 0 6px 0;
    font-size: 14px;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .transaction-invoice {
    margin: 0 0 6px 0;
    font-size: 12px;
    color: #007bff;
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    
    ion-icon {
      font-size: 14px;
    }
  }
  
  .transaction-date {
    margin: 0;
    font-size: 12px;
    color: #999;
    display: flex;
    align-items: center;
    gap: 4px;
    
    ion-icon {
      font-size: 14px;
    }
  }
}

/* Transaction Amount */
.transaction-amount {
  text-align: right;
  
  .points {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 700;
    color: #212121;
    
    &.pcu-earned {
      color: #28a745;
    }
    
    &.pcu-spent {
      color: #dc3545;
    }
    
    &.pcu-refund {
      color: #ffc107;
    }
    
    &.pcu-reversal {
      color: #343a40;
    }
  }
  
  .currency {
    margin: 0;
    font-size: 14px;
    color: #666;
  }
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Filter animation when switching */
.transaction-card {
  &.filter-exit {
    animation: filterExit 0.3s ease-out forwards;
  }
  
  &.filter-enter {
    animation: filterEnter 0.3s ease-out forwards;
  }
}

@keyframes filterExit {
  from {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateX(-20px) scale(0.95);
  }
}

@keyframes filterEnter {
  from {
    opacity: 0;
    transform: translateX(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Responsive Design */
@media (max-width: 375px) {
  .filter-container {
    padding: 12px 16px;
  }
  
  .filter-scroll {
    gap: 8px;
  }
  
  .filter-pill {
    padding: 8px 14px;
    font-size: 13px;
    
    ion-icon {
      font-size: 16px;
    }
  }
  
  .transactions-container {
    padding: 20px 16px;
  }
  
  .transaction-card {
    padding: 16px;
    gap: 12px;
  }
  
  .transaction-icon {
    width: 40px;
    height: 40px;
    
    ion-icon {
      font-size: 20px;
    }
  }
  
  .transaction-info {
    .transaction-title h3 {
      font-size: 15px;
    }
    
    .transaction-description {
      font-size: 13px;
    }
    
    .transaction-invoice {
      font-size: 11px;
    }
    
    .transaction-date {
      font-size: 11px;
    }
  }
  
  .transaction-amount {
    .points {
      font-size: 16px;
    }
    
    .currency {
      font-size: 12px;
    }
  }
}