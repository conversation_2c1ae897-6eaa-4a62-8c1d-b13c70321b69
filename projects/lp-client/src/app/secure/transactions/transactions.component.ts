import { Component, Injector, OnInit } from '@angular/core';
import {
  MemberService,
  Statement,
  KeyCloakService,
  MemberProfile,
  LssConfig,
} from 'lp-client-api';
import { AbstractFormComponent } from 'mobile-components';
import { environment } from '../../../environments/environment';
@Component({
  selector: 'app-transactions',
  templateUrl: './transactions.component.html',
  styleUrls: ['./transactions.component.scss'],
})
export class TransactionsComponent extends AbstractFormComponent<Statement> {
  //loading = false;
  searchRender = false;
  statements: Statement[] = [];
  filteredStatements: Statement[] = [];
  selectedTransactionType: string = 'All';
  beginDate?: any;
  endDate?: Date;
  profile?: MemberProfile;

  constructor(
    injector: Injector,
    private memberService: MemberService,
    private kc: KeyCloakService,
    public lssConfig: LssConfig
  ) {
    super(injector);
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        if (this.profile) {
          this._formState = this._formStateType.read;
        }
      })
    );
  }
  environment = environment;

  override ionViewDidEnter(): void {
    super.ionViewDidEnter();
    this.loading = true;
    this.search();
  }

  search(retry?: boolean): void {
    if (!this.profile && !retry) {
      setTimeout(() => this.search(true), 500);
      return;
    }
    let limit: number;

    this.showLoadingModal('Fetching your latest transactions').then(() => {
      var d = new Date();
      // if (this._formState === this._formStateType.read) {
      //   this.beginDate = d.setDate(d.getDate() - 5);
      //   this.endDate = new Date();
      //   limit = 5; // Recent transactions limited to 5 records
      // } else {
      //   limit = 10; // Standard pagination to 10 records
      // }
      limit = 10; // Standard pagination to 10 records
      this.memberService
        .getTransactionHistory(
          this.kc.lpUniueReference,
          undefined,
          undefined,
          0,
          limit
        )
        .subscribe({
          error: (error) => {
            console.log('error', error);
            this.dismissLoadingModal();
            this.presentToast({
              message: 'Oops, something went wrong!',
              color: 'danger',
              position: 'middle',
            }).then();
          },
          next: (body) => {
            console.log('body', body);
            if (body !== undefined) {
              this.statements = body;
              // Log all unique transaction types to understand what reversal types exist
              const uniqueTypes = [...new Set(body.map(s => s.transactionType))];
              console.log('Available transaction types:', uniqueTypes);
              this.filterTransactions();
            }
            this.dismissLoadingModal();
          },
        });
    });
  }

  selectTransactionType(type: string): void {
    this.selectedTransactionType = type;
    this.filterTransactions();
  }

  filterTransactions(): void {
    if (this.selectedTransactionType === 'All') {
      this.filteredStatements = [...this.statements];
    } else if (this.selectedTransactionType === 'Reversals') {
      // Include all reversal-related transaction types
      this.filteredStatements = this.statements.filter(statement => 
        this.isReversalTransaction(statement)
      );
    } else {
      this.filteredStatements = this.statements.filter(
        statement => statement.transactionType === this.selectedTransactionType
      );
    }
  }

  isReversalTransaction(statement: Statement): boolean {
    // Check for explicit reversal/refund transaction types
    const reversalTypes = ['Refund', 'RefundRedemption', 'Reversal', 'Void', 'Cancel'];
    
    if (reversalTypes.includes(statement.transactionType || '')) {
      return true;
    }
    
    // Check for transactions with negative points (potential reversals)
    const hasNegativePoints = (statement.transactionPoints || 0) < 0;
    
    // Check for transactions with negative value (potential reversals)
    const hasNegativeValue = (statement.actValue || 0) < 0;
    
    return hasNegativePoints || hasNegativeValue;
  }

  getTransactionIcon(transactionType?: string): string {
    switch (transactionType) {
      case 'Accrual':
        return 'add-circle-outline';
      case 'Redemption':
        return 'remove-circle-outline';
      case 'Refund':
      case 'RefundRedemption':
        return 'refresh-circle-outline';
      case 'Reversal':
      case 'Void':
      case 'Cancel':
        return 'arrow-undo-circle-outline';
      case 'Token':
      case 'TokenRedemption':
        return 'gift-outline';
      default:
        return 'pricetags-outline';
    }
  }

  getTransactionColor(transactionType?: string): string {
    switch (transactionType) {
      case 'Accrual':
        return 'success';
      case 'Redemption':
        return 'danger';
      case 'Refund':
      case 'RefundRedemption':
        return 'warning';
      case 'Reversal':
      case 'Void':
      case 'Cancel':
        return 'dark';
      case 'Token':
      case 'TokenRedemption':
        return 'tertiary';
      default:
        return 'medium';
    }
  }

  getTransactionIconColor(transactionType?: string): string {
    switch (transactionType) {
      case 'Accrual':
        return '#28a745';
      case 'Redemption':
        return '#dc3545';
      case 'Refund':
      case 'RefundRedemption':
        return '#ffc107';
      case 'Reversal':
      case 'Void':
      case 'Cancel':
        return '#343a40';
      case 'Token':
      case 'TokenRedemption':
        return '#6f42c1';
      default:
        return '#6c757d';
    }
  }

  getTransactionPointsClass(transactionType?: string): string {
    switch (transactionType) {
      case 'Accrual':
        return 'pcu-earned';
      case 'Redemption':
        return 'pcu-spent';
      case 'Refund':
      case 'RefundRedemption':
        return 'pcu-refund';
      case 'Reversal':
      case 'Void':
      case 'Cancel':
        return 'pcu-reversal';
      default:
        return 'transaction-points';
    }
  }

  getTransactionPointsDisplay(statement: Statement): string {
    const points = statement.transactionPoints || 0;
    const transactionType = statement.transactionType;
    
    if (transactionType === 'Accrual' || transactionType === 'Refund') {
      return `+${Math.abs(points)}`;
    } else if (transactionType === 'Redemption' || transactionType === 'RefundRedemption') {
      return `-${Math.abs(points)}`;
    } else if (transactionType === 'Reversal' || transactionType === 'Void' || transactionType === 'Cancel') {
      // For reversals, show the actual sign of the points (could be + or -)
      return points >= 0 ? `+${points}` : `${points}`;
    }
    
    // For other transactions, show the actual value with appropriate sign
    return points >= 0 ? `+${points}` : `${points}`;
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';
    
    // Handle UTC timezone format like "2024-11-27T13:07:01Z[UTC]"
    let cleanDateString = dateString;
    if (dateString.includes('[UTC]')) {
      cleanDateString = dateString.replace(/\[UTC\]$/, '');
    }
    
    const date = new Date(cleanDateString);
    if (isNaN(date.getTime())) return '';
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric'
    };
    return date.toLocaleDateString('en-US', options);
  }

  formatCurrency(value: number): string {
    if (!value && value !== 0) return '0.00';
    return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
}
