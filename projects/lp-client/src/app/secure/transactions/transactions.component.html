<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo
      [names]="profile?.givenNames + ' ' + profile?.surname"
      type="transactions"
      [balance]="profile?.currentBalance"
      [src]="lssConfig.pages.landing.loggedinIcon"
    />
  </div>

  <!-- Transactions Section -->
  <div class="transactions-section">
    <!-- Filter Pills -->
    <div class="filter-container">
      <div class="filter-scroll">
        <button 
          class="filter-pill"
          [class.active]="selectedTransactionType === 'All'"
          (click)="selectTransactionType('All')">
          <ion-icon name="list-outline"></ion-icon>
          All
        </button>
        <button 
          class="filter-pill success"
          [class.active]="selectedTransactionType === 'Accrual'"
          (click)="selectTransactionType('Accrual')">
          <ion-icon name="add-circle-outline"></ion-icon>
          Earned
        </button>
        <button 
          class="filter-pill danger"
          [class.active]="selectedTransactionType === 'Redemption'"
          (click)="selectTransactionType('Redemption')">
          <ion-icon name="remove-circle-outline"></ion-icon>
          Spent
        </button>
        <button 
          class="filter-pill warning"
          [class.active]="selectedTransactionType === 'Refund'"
          (click)="selectTransactionType('Refund')">
          <ion-icon name="refresh-circle-outline"></ion-icon>
          Refund
        </button>
        <button 
          class="filter-pill warning"
          [class.active]="selectedTransactionType === 'RefundRedemption'"
          (click)="selectTransactionType('RefundRedemption')">
          <ion-icon name="refresh-outline"></ion-icon>
          Refund Redemption
        </button>
        <button 
          class="filter-pill dark"
          [class.active]="selectedTransactionType === 'Reversals'"
          (click)="selectTransactionType('Reversals')">
          <ion-icon name="arrow-undo-outline"></ion-icon>
          Reversals
        </button>
      </div>
    </div>
    
    <!-- Transactions List -->
    <div class="transactions-container">
      <!-- Empty State -->
      <div class="empty-state" *ngIf="filteredStatements.length === 0 && !loading">
        <div class="empty-icon">
          <ion-icon name="receipt-outline"></ion-icon>
        </div>
        <h3>No transactions found</h3>
        <p>{{ selectedTransactionType === 'All' ? 'You haven\'t made any transactions yet' : 'No ' + selectedTransactionType.toLowerCase() + ' transactions found' }}</p>
      </div>

      <!-- Loading State -->
      <div class="loading-state" *ngIf="loading">
        <ion-spinner name="crescent"></ion-spinner>
        <p>Loading transactions...</p>
      </div>

      <!-- Transactions List -->
      <div class="transactions-list" *ngIf="filteredStatements.length > 0 && !loading">
        <div
          class="transaction-card"
          [class.slide-in]="true"
          [style.animation-delay.ms]="i * 50"
          *ngFor="let statement of filteredStatements; let i = index">
          
          <!-- Transaction Icon -->
          <div class="transaction-icon" [class]="getTransactionColor(statement.transactionType)">
            <ion-icon [name]="getTransactionIcon(statement.transactionType)"></ion-icon>
          </div>
          
          <!-- Transaction Info -->
          <div class="transaction-info">
            <div class="transaction-title">
              <h3>{{ statement.transactionType }}</h3>
              <span class="reversal-badge" *ngIf="isReversalTransaction(statement)">
                <ion-icon name="arrow-undo-outline"></ion-icon>
                REV
              </span>
            </div>
            <p class="transaction-description">{{ statement.label }}</p>
            <p class="transaction-invoice" *ngIf="statement.invoiceNumber">
                <ion-icon name="document-text-outline"></ion-icon>
                {{ statement.invoiceNumber }}
            </p>
            <p class="transaction-date" *ngIf="statement.loadDate">
                <ion-icon name="calendar-outline"></ion-icon>
                {{ formatDate(statement.loadDate) || 'Invalid Date' }}
            </p>
          </div>
          
          <!-- Transaction Amount -->
          <div class="transaction-amount">
            <p class="points" [class]="getTransactionPointsClass(statement.transactionType)">
              {{ getTransactionPointsDisplay(statement) }} pts
            </p>
            <p class="currency" *ngIf="statement.actValue">
              R{{ formatCurrency(statement.actValue) }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</lib-page-wrapper>