import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastController } from '@ionic/angular';
import { Router } from '@angular/router';

@Component({
  selector: 'app-points-topup',
  templateUrl: './points-topup.component.html',
  styleUrls: ['./points-topup.component.scss']
})
export class PointsTopupComponent implements OnInit {
  topupForm: FormGroup;
  status_loading = false;
  
  pointsOptions = [
    { value: 500, label: '500 Points', price: 50 },
    { value: 1000, label: '1000 Points', price: 100 },
    { value: 2500, label: '2500 Points', price: 250 },
    { value: 5000, label: '5000 Points', price: 500 },
    { value: 10000, label: '10000 Points', price: 1000 },
  ];

  constructor(
    private formBuilder: FormBuilder,
    private toastController: ToastController,
    private router: Router
  ) {
    this.topupForm = this.formBuilder.group({
      purchaseType: ['points', Validators.required],
      pointsOption: ['', Validators.required],
      quantity: [1, [Validators.required, Validators.min(1), Validators.max(100)]]
    });
  }

  ngOnInit() {
    // Set default values
    this.topupForm.patchValue({
      purchaseType: 'points',
      pointsOption: 500
    });
  }

  get totalPoints(): number {
    const selectedOption = this.topupForm.get('pointsOption')?.value;
    const quantity = this.topupForm.get('quantity')?.value || 0;
    return selectedOption * quantity;
  }

  get totalPrice(): number {
    const selectedValue = this.topupForm.get('pointsOption')?.value;
    const quantity = this.topupForm.get('quantity')?.value || 0;
    const selectedOption = this.pointsOptions.find(opt => opt.value === selectedValue);
    return selectedOption ? selectedOption.price * quantity : 0;
  }

  async addToBasket() {
    if (this.topupForm.valid) {
      this.status_loading = true;
      
      try {
        // Here you would typically call a service to add the points to the basket
        // For now, we'll just show a success message
        
        const toast = await this.toastController.create({
          message: `${this.totalPoints} points added to basket`,
          duration: 3000,
          position: 'top',
          color: 'success'
        });
        await toast.present();
        
        // Navigate to basket or stay on page
        // this.router.navigate(['/secure/basket']);
        
      } catch (error) {
        const toast = await this.toastController.create({
          message: 'Error adding points to basket. Please try again.',
          duration: 3000,
          position: 'top',
          color: 'danger'
        });
        await toast.present();
      } finally {
        this.status_loading = false;
      }
    } else {
      const toast = await this.toastController.create({
        message: 'Please fill in all required fields',
        duration: 3000,
        position: 'top',
        color: 'warning'
      });
      await toast.present();
    }
  }

  clearForm() {
    this.topupForm.patchValue({
      pointsOption: 500,
      quantity: 1
    });
  }
}
