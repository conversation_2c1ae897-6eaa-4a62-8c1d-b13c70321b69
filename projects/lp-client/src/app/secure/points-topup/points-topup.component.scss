/* Points Top-Up Styles - Following Profile Component Pattern */

/* Profile Content Container */
.profile-content {
  width: 100%;
}

/* Section Cards */
.section-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  animation: slideUp 0.6s ease-out;
  animation-fill-mode: both;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #212121;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 12px;

  ion-icon {
    font-size: 24px;
    color: var(--ion-color-primary, #FF6B35);
  }
}

/* Form Grid Layout */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.form-field {
  position: relative;

  &.full-width {
    grid-column: 1 / -1;
  }
}

/* Modern Input Styling */
.modern-input {
  --background: #f8f9fa;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --inner-padding-end: 0;
  --border-width: 0;
  --inner-border-width: 0;
  border-radius: 12px;
  margin-bottom: 0;
  transition: all 0.3s ease;

  &::part(native) {
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
  }

  &.ion-focused::part(native) {
    border-color: var(--ion-color-primary, #FF6B35);
    background: white;
  }

  &.ion-invalid.ion-touched::part(native) {
    border-color: var(--ion-color-danger, #F44336);
  }

  ion-input,
  ion-select {
    --placeholder-opacity: 0.5;
    font-size: 16px;
  }

  .field-icon {
    font-size: 20px;
    color: #666;
    margin-right: 8px;
  }

  &.readonly-field {
    ion-input {
      opacity: 0.8;
    }

    &::part(native) {
      background: #f0f0f0;
    }
  }
}

/* Price Section */
.price-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .price-label {
    font-size: 16px;
    font-weight: 500;
    color: #666;
  }

  .price-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--ion-color-primary, #FF6B35);
  }
}

/* Action Section */
.action-section {
  margin-top: 32px;
}

.button-group {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 12px;
}

.add-button {
  --background: var(--ion-color-primary, #FF6B35);
  --background-activated: var(--ion-color-primary-shade, #e85d2f);
  --background-hover: var(--ion-color-primary-tint, #ff7a49);
  --border-radius: 16px;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;

  ion-icon {
    font-size: 20px;
    margin-right: 8px;
  }

  &:hover:not([disabled]) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
  }

  &:active:not([disabled]) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
  }

  &[disabled] {
    --background: #e0e0e0;
    --color: #999;
    box-shadow: none;
    opacity: 0.6;
  }
}

.clear-button {
  --border-radius: 16px;
  --border-color: #666;
  --border-width: 2px;
  --color: #666;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  text-transform: none;
  transition: all 0.3s ease;

  &:hover:not([disabled]) {
    --background: #f5f5f5;
  }

  &[disabled] {
    opacity: 0.6;
  }
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Override default Ionic styles */
ion-item {
  --min-height: 56px;
  --border-width: 0;
  --inner-border-width: 0;
  --border-color: transparent;
}

ion-select {
  max-width: 100%;
}

/* Fix for readonly inputs */
ion-input[readonly] {
  opacity: 0.7;
  
  &::part(native) {
    background: #f0f0f0;
  }
}

/* Global fix for all ion-items in points topup */
.profile-content {
  ion-item {
    --border-width: 0;
    --inner-border-width: 0;
    --border-color: transparent;
    --border-style: none;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .button-group {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 375px) {

  .section-card {
    padding: 20px 16px;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 16px;
  }

  .modern-input {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .add-button,
  .clear-button {
    height: 48px;
    font-size: 15px;
  }

  .price-value {
    font-size: 20px;
  }
}