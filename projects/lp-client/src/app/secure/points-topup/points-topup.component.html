<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo
      [names]="'Points Top-Up'"
      type="points"
      [balance]="0"
    />
  </div>
  
  <!-- Points Top-Up Content -->
  <div class="profile-content">
    <form [formGroup]="topupForm">
      <!-- Points Top-Up Section -->
      <div class="section-card">
        <h3 class="section-title">
          <ion-icon name="cart-outline"></ion-icon>
          Points Top-Up
        </h3>
        
        <div class="form-grid">
          <!-- Type of Purchase -->
          <div class="form-field full-width">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="pricetag-outline" class="field-icon"></ion-icon>
              <ion-input
                label="Type of Purchase *"
                labelPlacement="floating"
                value="Points Top-up"
                readonly
                type="text"
              ></ion-input>
            </ion-item>
          </div>

          <!-- Points Options -->
          <div class="form-field full-width">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="cash-outline" class="field-icon"></ion-icon>
              <ion-select
                label="Options *"
                labelPlacement="floating"
                formControlName="pointsOption"
                placeholder="Select points amount"
              >
                <ion-select-option
                  *ngFor="let option of pointsOptions"
                  [value]="option.value"
                >
                  {{ option.label }} - R{{ option.price }}
                </ion-select-option>
              </ion-select>
            </ion-item>
          </div>

          <!-- Quantity -->
          <div class="form-field">
            <ion-item class="modern-input">
              <ion-icon slot="start" name="layers-outline" class="field-icon"></ion-icon>
              <ion-input
                label="Quantity *"
                labelPlacement="floating"
                type="number"
                formControlName="quantity"
                min="1"
                max="100"
                [readonly]="status_loading"
              ></ion-input>
            </ion-item>
          </div>

          <!-- Total Points to be Purchased -->
          <div class="form-field">
            <ion-item class="modern-input readonly-field">
              <ion-icon slot="start" name="calculator-outline" class="field-icon"></ion-icon>
              <ion-input
                label="Total Points to be Purchased"
                labelPlacement="floating"
                [value]="totalPoints"
                readonly
                type="text"
              ></ion-input>
            </ion-item>
          </div>
        </div>

        <!-- Total Price -->
        <div class="price-section">
          <div class="price-label">Total Price</div>
          <div class="price-value">R {{ totalPrice.toFixed(2) }}</div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-section">
        <div class="button-group">
          <ion-button 
            expand="block" 
            class="add-button" 
            (click)="addToBasket()"
            [disabled]="status_loading || !topupForm.valid"
          >
            <ion-icon name="cart-outline" slot="start"></ion-icon>
            Add To Basket
          </ion-button>
          
          <ion-button 
            expand="block" 
            fill="outline"
            class="clear-button" 
            (click)="clearForm()"
            [disabled]="status_loading"
          >
            Clear
          </ion-button>
        </div>
      </div>
    </form>
  </div>
</lib-page-wrapper>
