import { Component, Injector, OnInit } from '@angular/core';
import { AbstractControlOptions, Validators } from '@angular/forms';
import {
  CustomValidators,
  KeyCloakService,
  LPMemberEntityTools,
  ContactForm,
  MemberService,
  MemberProfile,
  LPCode,
  LssConfig,
} from 'lp-client-api';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { AbstractFormComponent } from 'mobile-components';
import { environment } from '../../../environments/environment';
import moment from 'moment';
import { set } from 'lodash';

@Component({
  selector: 'app-profileremove',
  templateUrl: './profileremove.component.html',
  styleUrls: ['./profileremove.component.scss'],
})
export class ProfileremoveComponent
  extends AbstractFormComponent<ContactForm>
  implements OnInit
{
  error?: string;
  phone: any;
  contactForm?: ContactForm;
  profile: any;
  categories!: Observable<LPCode[]>;
  constructor(
    injector: Injector,
    protected readonly keyCloakService: KeyCloakService,
    private memberService: MemberService,
    protected readonly router: Router,
    public lssConfig: LssConfig
  ) {
    super(injector);
    this.generateForm();
  }
  environment = environment;
  isModalOpen = false;
  showError = false;
  showGoHome = false;

  ionViewWillEnter() {
    this.categories = this.getCodeList('CNCT');
    this.loading = true;
    this.memberService
      .getProfile(this.keyCloakService.lpUniueReference, true)
      .subscribe((data) => {
        this.profile = data;
        if (data) {
          let tel = null;
          if (data.personTelephone) {
            for (let i = 0; i < data.personTelephone.length; i++) {
              if (data.personTelephone[i].telephoneType == 'CELL')
                tel = data.personTelephone[i];
            }
            if (tel) {
              let pl = {
                internationalNumber:
                  tel.countryCode + ' ' + tel.telephoneNumber?.substring(1),
                nationalNumber: tel.telephoneNumber,
                isoCode: 'za',
                dialCode: tel.countryCode,
              };
              this._form.controls['phone'].patchValue(pl);
            }
          }
          this._form.controls['givenNames'].patchValue(data.givenNames);
          this._form.controls['surname'].patchValue(data.surname);
          this._form.controls['email'].patchValue(data.emailAddress);
        }
      });
  }
  setOpen(isOpen: boolean) {
    this.isModalOpen = isOpen;
  }
  ngOnInit(): void {
    this.showError = false;

    this.showLoadingModal('Loading..').then(() => {
      this.keyCloakService.authStatus.subscribe((value) => {
        if (value != null && value.eventName !== 'init') {
          if (value.eventName !== null && value.eventName === 'login') {
            if (this.keyCloakService.userProfile?.mustRegister) {
              this.contactForm = {} as ContactForm;
              this.contactForm.externalId =
                this.keyCloakService.userProfile.sub!;
              this.contactForm.emailAddress =
                this.keyCloakService.userProfile.email!;
              this.contactForm.givenNames =
                this.keyCloakService.userProfile.given_name!;
              this.contactForm.surname =
                this.keyCloakService.userProfile.family_name!;
              this._form.patchValue(this.contactForm);
            }
          }
          this.dismissLoadingModal();
        }
      });
    });
  }

  generateForm(): void {
    this._form = this._formBuilder.group({
      message: ['', [Validators.required]],
    } as AbstractControlOptions);
  }

  showTerms(): void {
    this._formState = this._formStateType.terms;
  }

  canSignup(): boolean {
    return this.isFormValid() && this.form.terms.value === true;
  }
  logout() {
    if (this.keyCloakService.authSuccess) {
      this.keyCloakService.keycloak?.logout();
    }
  }
  submit(): void {
    this.formData = this.getFormValues();
    console.log('form', this.formData);
    this.showError = false;

    let payload: any = this.formData;
    this.isModalOpen = false;

    console.log('payload', payload);
    console.log('phone', this.phone);
    console.log('profile', this.profile);
    let isSame = false;
    for (let i = 0; i < this.profile.personTelephone?.length; i++) {
      const inputNumber = this.phone.nationalNumber.replace(/\s/g, '');
      console.log('inputNumber', inputNumber);
      console.log(
        'profileNumber',
        this.profile?.personTelephone[i].telephoneNumber
      );
      if (this.profile?.personTelephone[i].telephoneNumber == inputNumber) {
        isSame = true;
      }
    }
    console.log('ISS SA', isSame);
    if (isSame) {
      this.showLoadingModal('Please wait while we delete your data!').then(
        () => {
          this.memberService
            .deleteAccount(this.keyCloakService.lpUniueReference, this.formData)
            .subscribe({
              error: (error: any) => {
                console.log('err contact us', error);
                this.dismissLoadingModal();
                this.presentToast({
                  message: 'Oops, something went wrong!',
                  color: 'danger',
                  position: 'bottom',
                }).then();

                this._formState = this._formStateType.fail;
                this.error = error.error.detail;
              },
              next: (body: any) => {
                console.log('body contact us', body);
                this.dismissLoadingModal();
                this.presentToast({
                  message: 'Your Account has been deleted',
                  position: 'bottom',
                });
                this.logout();
                setTimeout(() => {
                  this.router.navigate(['/']);
                }, 1000);
              },
            });
        }
      );
    } else {
      this.showError = true;
      console.log('showError', this.showError);
      this.isModalOpen = false;
    }
  }
}
