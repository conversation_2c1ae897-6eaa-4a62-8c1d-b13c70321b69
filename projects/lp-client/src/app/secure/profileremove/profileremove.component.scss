ion-textarea {
    min-height: 140px;
}

.logo {
    height: 150px
}
.logo-col {
    align-items: flex-end;
    --background: #FFFF
}
.contact-card {
    justify-content: center;
}
.contact {
    --background: var(--ion-color-primary);
    position: relative;
    height: 100vh;

}

.formbackground {
 margin:  10px
}

.app-background {
    --background: var(--ion-color-base)
}
.action-card {
    text-align: center;
	
	background-color: var(--ion-color-primary-shade);
	border-radius: 1rem;
	box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.6);
}
.card-background {
    background-color: #fff;
    width: 100vw;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.center {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.action-icon {
	position: relative;
	top: -25px;
    right: -23px;
	border-radius: 3rem;
	width: 4rem;
	height: 4rem;
	box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.4);
    text-align: center;
    padding: 25px 0;
	background-color: var(--ion-color-tertiary);

}
.points {
	--background: var(--ion-color-secondary);
    width: 100%;
    margin-left: 20px;
    height: 50px;

}

.icon-call {
    color: var(--ion-color-tertiary);
    margin-right: 5px;
    margin-top: 5px;
}

.submit {
    --background: var(--ion-color-primary);
}
.phone-call {
    color: var(--ion-color-primary-contrast);
    text-decoration: none;
    margin-left: 35px;

}

.flex {
    width: 100%;
    justify-content: space-between;
}

.pg-title {
    margin-top: 20px
}

.text-black {
    color: black;
    padding: 25px;
}

.text-red {
    color: red;
    padding: 25px;
}