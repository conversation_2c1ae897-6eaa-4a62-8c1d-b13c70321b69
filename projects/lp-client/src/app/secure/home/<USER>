import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { KeyCloakService, LogService } from 'lp-client-api';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
})
export class HomeComponent implements OnInit {
  constructor(
    private kc: KeyCloakService,
    private logService: LogService,
    private httpClient: HttpClient
  ) {}

  ngOnInit() {}

  extend() {
    this.httpClient
      .get(
        'https://rziaqa.loyaltyplus.aero/extsecure/loyaltyapi/1.0.0/member/2200001974/statementList',
        { params: { startDate: '2022-01-01', endDate: '2022-12-31' } }
      )
      .subscribe((data) => {
        console.log(data);
      });
    /*
    this.kc.keycloak?.updateToken(1800).then((data:any) => {
      console.log('Extend Success');
      console.log(data);
    }).catch((e) => {
      console.log('Extend Error');
      console.log(e);
    })
    */
  }
}
