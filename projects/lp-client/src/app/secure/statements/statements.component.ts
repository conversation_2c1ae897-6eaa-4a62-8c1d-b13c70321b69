import { Component, OnInit, Injector } from '@angular/core';
import { MemberService, MemberProfile, LssConfig } from 'lp-client-api';
import { AbstractFormComponent } from 'mobile-components';

export interface StatementItem {
  id: string;
  date: string;
  description: string;
  period?: string;
  type: 'monthly' | 'adhoc';
  downloadUrl?: string;
}

@Component({
  selector: 'app-statements',
  templateUrl: 'statements.component.html',
  styleUrls: ['statements.component.scss']
})
export class StatementsComponent extends AbstractFormComponent<StatementItem> {
  profile?: MemberProfile;
  statements: StatementItem[] = [];
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;
  paginatedStatements: StatementItem[] = [];

  constructor(
    injector: Injector,
    private memberService: MemberService,
    public lssConfig: LssConfig
  ) {
    super(injector);
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        if (this.profile) {
          this._formState = this._formStateType.read;
        }
      })
    );
  }

  override ionViewDidEnter(): void {
    super.ionViewDidEnter();
    this.loadStatements();
  }

  /**
   * Load statements from API or mock data
   */
  loadStatements(): void {
    this.loading = true;
    
    // Mock data - replace with actual API call
    setTimeout(() => {
      this.statements = [
        {
          id: '1',
          date: '31/12/2024',
          description: '2024 December',
          type: 'monthly'
        },
        {
          id: '2',
          date: '31/01/2025',
          description: '2025 January',
          type: 'monthly'
        },
        {
          id: '3',
          date: '28/02/2025',
          description: '2025 February',
          type: 'monthly'
        },
        {
          id: '4',
          date: '31/03/2025',
          description: '2025 March',
          type: 'monthly'
        },
        {
          id: '5',
          date: '30/04/2025',
          description: '2025 April',
          type: 'monthly'
        },
        {
          id: '6',
          date: '31/05/2025',
          description: '2025 May',
          type: 'monthly'
        },
        {
          id: '7',
          date: '30/06/2025',
          description: '2025 June',
          type: 'monthly'
        },
        {
          id: '8',
          date: '31/07/2025',
          description: 'Adhoc Statement',
          type: 'adhoc'
        }
      ];
      
      this.totalItems = this.statements.length;
      this.updatePagination();
      this.loading = false;
    }, 1000);
  }

  /**
   * Update pagination
   */
  updatePagination(): void {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedStatements = this.statements.slice(startIndex, endIndex);
  }

  /**
   * Go to specific page
   */
  goToPage(page: number): void {
    if (page >= 1 && page <= this.getTotalPages()) {
      this.currentPage = page;
      this.updatePagination();
    }
  }

  /**
   * Get total pages
   */
  getTotalPages(): number {
    return Math.ceil(this.totalItems / this.itemsPerPage);
  }

  /**
   * Get page numbers for pagination
   */
  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
    
    return pages;
  }

  /**
   * View statement
   */
  viewStatement(statement: StatementItem): void {
    // Here you would typically open/download the statement
    console.log('Viewing statement:', statement);
    
    // Mock implementation - you would replace this with actual functionality
    if (statement.downloadUrl) {
      window.open(statement.downloadUrl, '_blank');
    } else {
      // Show a message or redirect to statement viewer
      console.log('Statement viewer not implemented yet');
    }
  }

  /**
   * Get statement icon based on type
   */
  getStatementIcon(statement: StatementItem): string {
    return statement.type === 'adhoc' ? 'document-text-outline' : 'calendar-outline';
  }

  /**
   * Get statement type display name
   */
  getStatementTypeDisplay(statement: StatementItem): string {
    return statement.type === 'adhoc' ? 'Adhoc Statement' : 'Monthly Statement';
  }
}
