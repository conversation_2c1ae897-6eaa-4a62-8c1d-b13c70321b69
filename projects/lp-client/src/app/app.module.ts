import { APP_INITIALIZER, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
import { NotificationToastComponent } from './components/notification-toast/notification-toast.component';
import {
  InterceptorService,
  KeyCloakService,
  LogPublishersService,
  LogService,
  LssConfig,
} from 'lp-client-api';
import {
  FaIconLibrary,
  FontAwesomeModule,
} from '@fortawesome/angular-fontawesome';
import { fas } from '@fortawesome/free-solid-svg-icons';
import { environment } from '../environments/environment';
import { RouteReuseStrategy } from '@angular/router';
import {
  HttpClientModule,
  HTTP_INTERCEPTORS,
} from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ColorPickerModule } from 'ngx-color-picker';
import { ConfigService } from './services/config.service';
import { JsonDateAdapter } from './utils/json-date-adapter';

function initializeKeycloak(config: ConfigService) {
  return () => config.loadConfig();
}

@NgModule({
  declarations: [AppComponent],
  imports: [
    CommonModule,
    BrowserModule,
    BrowserAnimationsModule,
    FontAwesomeModule,
    HttpClientModule,
    IonicModule.forRoot(),
    AppRoutingModule,
    ColorPickerModule,
    NotificationToastComponent,
  ],
  providers: [
    { provide: 'environment', useValue: environment },
    {
      provide: APP_INITIALIZER,
      useFactory: initializeKeycloak,
      multi: true,
      deps: [ConfigService],
    },
    {
      provide: LssConfig,
      useFactory: (config: ConfigService) => {
        return config.sysConfig;
      },
      deps: [ConfigService],
    },
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: InterceptorService,
      multi: true,
      deps: [KeyCloakService],
    },
    LogService,
    LogPublishersService,
    JsonDateAdapter,
  ],
  bootstrap: [AppComponent],
})
export class AppModule {
  constructor(library: FaIconLibrary) {
    library.addIconPacks(fas);
  }
}
