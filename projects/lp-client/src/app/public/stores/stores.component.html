<lib-page-wrapper [containerSize]="'xl'" [hasBackground]="true" [layout]="'single'">
  <div header>
    <lib-head-logo
      [names]="profile?.givenNames + ' ' + profile?.surname"
      type="stores"
      [balance]="profile?.currentBalance"
      [src]="lssConfig.pages.landing.loggedinIcon"
    />
  </div>

  <!-- Stores Section -->
    <div class="stores-section">
    <!-- Map Container -->
    <div class="map-container">
      <google-map
        class="google-map"
        width="100%"
        height="100%"
        [options]="options"
        [center]="center"
        [zoom]="zoom"
        (boundsChanged)="boundsChanged()"
      >
        <map-marker-clusterer [imagePath]="markerClustererImagePath">
          <map-marker
            #marker="mapMarker"
            *ngFor="let marker of storeMarkers"
            [position]="marker.position"
            [label]="marker.label"
            [title]="marker.title"
            [options]="marker.options"
            (mapClick)="markerClick(marker)"
          ></map-marker>
        </map-marker-clusterer>
      </google-map>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
      <div class="filter-header" (click)="toggleFilter()">
        <div class="filter-title">
          <ion-icon name="filter-outline"></ion-icon>
          <span>Filter Stores</span>
        </div>
        <ion-icon 
          [name]="filterExpanded ? 'chevron-up-outline' : 'chevron-down-outline'"
          class="filter-toggle"
        ></ion-icon>
      </div>
      
      <div class="filter-content" [class.expanded]="filterExpanded">
        <form class="filter-form">
          <!-- Search Input -->
          <div class="input-group">
            <ion-icon name="search-outline" class="input-icon"></ion-icon>
            <ion-input
              placeholder="Search stores..."
              type="text"
              name="searchFilter"
              [(ngModel)]="searchFilter"
              class="modern-input"
            ></ion-input>
          </div>

          <!-- Province Select -->
          <div class="input-group">
            <ion-icon name="business-outline" class="input-icon"></ion-icon>
            <ion-select
              placeholder="Select Province"
              name="provinceFilter"
              [(ngModel)]="provinceFilter"
              (ionChange)="setProvinceFilter(provinceFilter ? provinceFilter : '')"
              class="modern-select"
              interface="popover"
              [interfaceOptions]="{cssClass: 'select-popover-upward', showBackdrop: false}"
            >
              <ion-select-option
                *ngFor="let province of provinces | async"
                [value]="province.value"
              >{{ province.label }}</ion-select-option>
            </ion-select>
          </div>

          <!-- City Select -->
          <div class="input-group">
            <ion-icon name="location-outline" class="input-icon"></ion-icon>
            <ion-select
              placeholder="Select City"
              name="cityFilter"
              [(ngModel)]="cityFilter"
              class="modern-select"
              interface="popover"
              [disabled]="!provinceFilter"
              [interfaceOptions]="{cssClass: 'select-popover-upward', showBackdrop: false}"
            >
              <ion-select-option
                *ngFor="let city of cities | async"
                [value]="city.value"
              >{{ city.label }}</ion-select-option>
            </ion-select>
          </div>

          <!-- Action Buttons -->
          <div class="filter-actions">
            <ion-button 
              expand="block" 
              (click)="filter(false)"
              class="filter-btn primary"
            >
              <ion-icon name="search-outline" slot="start"></ion-icon>
              Apply Filter
            </ion-button>
            <ion-button 
              expand="block" 
              fill="outline" 
              (click)="resetFilter()"
              class="filter-btn secondary"
            >
              <ion-icon name="refresh-outline" slot="start"></ion-icon>
              Reset
            </ion-button>
          </div>
        </form>
      </div>
    </div>

    <!-- Stores List -->
    <div class="stores-list-container">
      <div class="list-header">
        <h3>Stores Near You</h3>
        <span class="store-count" *ngIf="filteredStores.length > 0">
          {{ filteredStores.length }} stores found
        </span>
      </div>

      <!-- Loading State -->
      <div class="loading-state" *ngIf="loading">
        <ion-spinner name="crescent"></ion-spinner>
        <p>Finding stores...</p>
      </div>

      <!-- Empty State -->
      <div class="empty-state" *ngIf="!loading && filteredStores.length === 0">
        <div class="empty-icon">
          <ion-icon name="location-outline"></ion-icon>
        </div>
        <h3>No Stores Found</h3>
        <p *ngIf="!provinceFilter && !cityFilter">
          No stores in your immediate location
        </p>
        <p *ngIf="provinceFilter || cityFilter">
          No stores match your filter criteria
        </p>
      </div>

      <!-- Stores List -->
      <div class="stores-list" *ngIf="!loading && filteredStores.length > 0">
        <div
          class="store-card"
          *ngFor="let store of storeList | async; let i = index"
          (click)="setSelectedStore(store)"
          [style.animation-delay.ms]="i * 50"
        >
          <div class="store-icon">
            <ion-icon name="storefront-outline"></ion-icon>
          </div>
          
          <div class="store-info">
            <h4>{{ store.partnerName }}</h4>
            <p class="store-address" *ngIf="store.getAddressByType('PADR')">
              <ion-icon name="location-outline"></ion-icon>
              {{ store.getAddressByType("PADR")?.line1 }},
              {{ store.getAddressByType("PADR")?.suburbDesc }},
              {{ store.getAddressByType("PADR")?.provinceDesc }}
            </p>
          </div>
          
          <ion-icon name="chevron-forward-outline" class="store-arrow"></ion-icon>
        </div>
      </div>
    </div>
  </div>
</lib-page-wrapper>