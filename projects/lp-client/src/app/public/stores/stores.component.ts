import {
  Component,
  Injector,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  ChangeDetectorRef,
} from '@angular/core';
import { GoogleMap } from '@angular/google-maps';
import { Router } from '@angular/router';
import {
  Address,
  CodeItem,
  KeyCloakService,
  Partner,
  PartnerListRequest,
  PartnerService,
  SystemService,
  Telephone,
  MemberProfile,
  MemberService,
  HttpClientService,
  LssConfig,
} from 'lp-client-api';

import { AbstractComponent } from 'mobile-components';
// import { environment } from 'projects/lp-client/src/environments/environment';
import { BehaviorSubject, Observable, Subscription, map, toArray } from 'rxjs';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-stores',
  templateUrl: 'stores.component.html',
  styleUrls: ['stores.component.scss'],
})
export class StoresComponent extends AbstractComponent {
  profile?: MemberProfile;
  allStores: Partner[] = [];
  filteredStores: Partner[] = [];
  hashMap = new Map<string, null>();
  storeList: BehaviorSubject<Partner[]> = new BehaviorSubject<Partner[]>([]);
  storeMarkers: any[] = [];
  provinces!: Observable<CodeItem[]>;
  cities!: Observable<CodeItem[]>;
  @ViewChild(GoogleMap, { static: false }) map?: GoogleMap;
  center!: google.maps.LatLngLiteral;
  options: google.maps.MapOptions = {
    zoomControl: false,
    maxZoom: 18,
    minZoom: 4,
    streetViewControl: false,
  };
  zoom!: number;
  markerClustererImagePath =
    'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m';
  provinceFilter?: string | null = '';
  cityFilter?: string | null = '';
  partnerData?: Partner[];
  searchFilter: string = '';
  environment = environment;
  filterExpanded: boolean = false;

  constructor(
    injector: Injector,
    private kc: KeyCloakService,
    private partnerService: PartnerService,
    private _systemService: SystemService,
    protected readonly router: Router,
    private memberService: MemberService,
    private cd: ChangeDetectorRef,
    public lssConfig: LssConfig
  ) {
    super(injector);
  }

  ngOnInit() {
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        this.detectChanges();
      })
    );
    this.addGlobalSubscription(
      this.storeList.subscribe(() => {
        this.detectChanges();
      })
    );
    this.getProvinces();
  }
  /* Check if filters must be reset when entering the view */
  override ionViewDidEnter(): void {
    super.ionViewDidEnter();
    this.loading = true;
    if (this.filteredStores !== undefined && this.filteredStores.length > 0) {
      this.resetFilter();
    } else if (this.allStores.length == 0) {
      this.init();
    }
  }

  /* Initialise the map position and fetch all stores */
  private async init() {
    // User location
    navigator.geolocation.getCurrentPosition((position) => {
      // inital zoom is high to focus on the user location
      this.setMapCenter(
        position.coords.latitude,
        position.coords.longitude,
        12
      );
    });

    // Default location if user location is not provided
    if (this.center === undefined) {
      // default location, zoom is low to show entire country
      this.setMapCenter(
        environment.lssConfig.defaultLat,
        environment.lssConfig.defaultLng,
        4
      );
    }
    this.getStores();
  }

  /* Get all available stores */
  private getStores(): void {
    const partnerListRequest: PartnerListRequest = {
      memberCommObject: {
        apiId: environment.lssConfig.apiId,
      },
      limit: 1000,
      offset: 0,
    }; 
    this.showLoadingModal('Fetching store locations').then(() => {
      this.partnerService.getPartnersAll().subscribe({
        next: (result: any) => this.partnerListResponse(result),
        error: (e) => {
          this.dismissLoadingModal();
          this.presentToast({
            message: 'Error loading Store Details.',
            color: 'danger',
          }).then();
        },
      });
    });
  }
  private partnerListResponse(result: any) {
    if (result) {
      result.forEach((store: Partner) => {
        if (
          store.getAddressByType('PADR') &&
          store.getPartnerMoreByType('LOCATION')
        ) {
          this.allStores.push(store);
          let city = store.getAddressByType('PADR')?.city;
          this.hashMap.set(city ? city : '', null);
        }
      });
      this.resetFilter();
    }
    this.dismissLoadingModal();
  }
  /* Add a Store to the list of markers to be displayed on the map */
  private async addMarker(store: Partner) {
    // Do not add a marker for stores that do not have the correct data to be displayed on the map
    if (
      store.getPartnerMoreByType('LOCATION') !== undefined &&
      store.partnerName !== undefined &&
      store.partnerId !== undefined
    ) {
      this.storeMarkers.push({
        position: {
          lat: Number(
            store.getPartnerMoreByType('LOCATION')?.value.split(',')[0]
          ),
          lng: Number(
            store.getPartnerMoreByType('LOCATION')?.value.split(',')[1]
          ),
        },
        store: store, // Pass on the entire store object for use in store detail view
      });
    }
  }

  /* Handle click event on map marker */
  markerClick(marker: any) {
    this.setSelectedStore(marker['store']);
  }

  /* Handle map bound change to list stores that are within the updated bounds */
  boundsChanged() {
    if (
      (this.provinceFilter === undefined || this.provinceFilter === null) &&
      (this.cityFilter === undefined || this.cityFilter === null)
    ) {
      this.filter(true);
    } else {
      return;
    }
  }

  setMapCenter(lat: number, lng: number, zoom: number): void {
    this.center = {
      lat: lat,
      lng: lng,
    };
    this.zoom = zoom;
  }

  /* Navigate to the store detail for the selected store/marker */
  setSelectedStore(partner: Partner) {
    this.router.navigate(['/public/storedetail'], {
      state: { data: partner },
    });
  }

  /* Filter the list of stores */
  filter(filterEmpty: boolean): void {
    this.storeMarkers = []; // clear all markers
    // this.filteredStores = this.allStores; // Override filtered stores back to full store list
    if (filterEmpty) {
      // If filterEmpty then user is not filtering based on city/provice so filter stores to show those that are within map bounds
      let topBound: any = this.map?.getBounds()?.getNorthEast().lat();
      let bottomBound: any = this.map?.getBounds()?.getSouthWest().lat();
      let leftBound: any = this.map?.getBounds()?.getSouthWest().lng();
      let rightBound: any = this.map?.getBounds()?.getNorthEast().lng();

      this.filteredStores = this.allStores.filter((store) => {
        let locationSplit = store
          .getPartnerMoreByType('LOCATION')
          ?.value?.split(',') || ['', ''];

        var liesWithinLatBounds =
          topBound > Number(locationSplit[0]) &&
          bottomBound <= Number(locationSplit[0]);
        var liesWithinLngBounds =
          leftBound <= Number(locationSplit[1]) &&
          rightBound >= Number(locationSplit[1]);

        return liesWithinLatBounds && liesWithinLngBounds;
      });
    } else {
      this.cityFilter = this.cityFilter || '';
      this.provinceFilter = this.provinceFilter || '';
      if (this.provinceFilter.length > 0) {
        this.filteredStores = this.allStores.filter((partner) => {
          const adr: any = partner.getAddressByType('PADR');
          let isFilter = false;
          if (
            partner.partnerName
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.provinceDesc
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.cityDesc
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.line1
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.suburbDesc
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase())
          ) {
            isFilter = true;
          }
          console.log(adr, isFilter);

          if (adr && isFilter) {
            return (
              adr.province === this.provinceFilter &&
              (this.cityFilter === '' || adr.city === this.cityFilter)
            );
          }
          return false;
        });
      } else {
        this.filteredStores = this.allStores.filter((partner) => {
          const adr: any = partner.getAddressByType('PADR');
          let isFilter = false;
          if (
            partner.partnerName
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.provinceDesc
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.cityDesc
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.line1
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.suburbDesc
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase())
          ) {
            isFilter = true;
          }
          console.log(adr, isFilter);
          if (adr && isFilter) {
            return isFilter;
          }
          return false;
        });
      }
      if (this.filteredStores.length > 0) {
        this.setMapCenter(
          Number(
            this.filteredStores[0]
              .getPartnerMoreByType('LOCATION')
              ?.value.split(',')[0]
          ),
          Number(
            this.filteredStores[0]
              .getPartnerMoreByType('LOCATION')
              ?.value.split(',')[1]
          ),
          4
        );
      }
    }

    // Set map markers based on filtered stores
    for (var store of this.filteredStores) {
      this.addMarker(store);
    }
    this.storeList.next(this.filteredStores);
  }

  /* Reset any filtering of stores */
  resetFilter(): void {
    this.provinceFilter = null;
    this.cityFilter = null;
    navigator.geolocation.getCurrentPosition((position) => {
      this.setMapCenter(
        position.coords.latitude,
        position.coords.longitude,
        12
      );
    });
    this.filter(true);
  }

  /* Get list of provinces for filtering */
  getProvinces() {
    this.provinces = this._systemService.listProvice();
  }

  /* Set selected province filter value */
  setProvinceFilter(filter: string) {
    this.provinceFilter = filter;
    this.getCities(this.provinceFilter);
  }

  /* Get list of cities for filtering */
  getCities(filter: string) {
    this.cities = this._systemService
      .listCity('', filter)
      .pipe(
        map((data: CodeItem[]) =>
          data.filter((record) => this.hashMap.has(record.value))
        )
      );
  }

  /* Set selected city filter value */
  setCityFilter(filter: string) {
    this.cityFilter = filter;
  }

  /* Toggle filter expansion */
  toggleFilter() {
    this.filterExpanded = !this.filterExpanded;
  }
}
