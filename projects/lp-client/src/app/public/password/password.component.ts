import { Component, Injector, OnInit } from '@angular/core';
import { AbstractControlOptions, Validators } from '@angular/forms';
import { CustomValidators, LssConfig } from 'lp-client-api';
import { AbstractFormComponent } from 'mobile-components';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-password',
  templateUrl: 'password.component.html',
  styleUrls: ['password.component.scss'],
})
export class PasswordComponent
  extends AbstractFormComponent<any>
  implements OnInit
{
  constructor(injector: Injector) {
    super(injector);
    this.generateForm();
  }
  environment = environment;

  ngOnInit(): void {}

  generateForm(): void {
    if (this.isAuthenticated()) {
      this.setFormState(this._formStateType.update);
      this._form = this._formBuilder.group(
        {
          password: [
            '',
            Validators.compose([
              Validators.required,
              CustomValidators.passwordValidate(),
            ]),
          ],
          passwordConfirm: ['', [Validators.required]],
        },
        {
          validators: Validators.compose([
            CustomValidators.matchValue(
              'password',
              'Password',
              'passwordConfirm',
              'Confirm Password'
            ),
          ]),
        } as AbstractControlOptions
      );
    } else {
      this.setFormState(this._formStateType.reset);
      this._form = this._formBuilder.group({
        accountNumber: ['', [Validators.required]],
      });
    }
  }

  resetPassword(): void {
    console.log('reset');
  }
}
