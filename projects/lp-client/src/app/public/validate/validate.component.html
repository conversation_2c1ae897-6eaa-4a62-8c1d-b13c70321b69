<ion-header [translucent]="true">
  <ion-toolbar class="modern-toolbar">
    <ion-buttons slot="start">
      <ion-button class="back-button" [routerLink]="['/']">
        <ion-icon slot="icon-only" name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>OTP Verification</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content class="modern-otp-content">
  <div class="otp-header-section">
    <div class="otp-icon-wrapper">
      <ion-icon name="shield-checkmark-outline" class="otp-main-icon"></ion-icon>
    </div>
    <h2 class="otp-title">Verify Your Identity</h2>
    <p class="otp-subtitle">Enter your details to receive a verification code</p>
  </div>

  <div class="otp-form-card">
    <form [formGroup]="_form" class="otp-form">
      <div class="form-field animated slideUp" style="animation-delay: 0.1s">
        <div class="field-icon">
          <ion-icon name="card-outline"></ion-icon>
        </div>
        <ion-item lines="none" class="modern-input">
          <ion-input
            labelPlacement="floating"
            label="Card Number"
            type="text"
            formControlName="Cardnumber"
            placeholder="Enter your card number"
          ></ion-input>
        </ion-item>
      </div>

      <div class="form-field animated slideUp" style="animation-delay: 0.2s">
        <div class="field-icon">
          <ion-icon name="call-outline"></ion-icon>
        </div>
        <ion-item lines="none" class="modern-input phone-input">
          <ion-label position="floating">Mobile Number *</ion-label>
          <ion-intl-tel-input
            id="phone-number"
            labelPlacement="floating"
            label="Mobile Number *"
            name="phone-number"
            formControlName="phone"
            [enableAutoCountrySelect]="true"
            [selectFirstCountry]="lssConfig.telephone.selectFirstCountry"
            [preferredCountries]="lssConfig.telephone.preferredCountries"
          ></ion-intl-tel-input>
        </ion-item>
      </div>

      <div class="error-messages" *ngIf="(isFormComponentInvalid('phone') || (!isMyFormValid() && isMyPhoneValid()) || showError)">
        <div class="error-item animated fadeIn" *ngIf="isFormComponentInvalid('phone')">
          <ion-icon name="alert-circle-outline"></ion-icon>
          <span>5 digits needed when you spend points</span>
        </div>
        <div class="error-item animated fadeIn" *ngIf="!isMyFormValid() && isMyPhoneValid()">
          <ion-icon name="alert-circle-outline"></ion-icon>
          <span>Please fill in a valid mobile number</span>
        </div>
        <div class="error-item animated fadeIn" *ngIf="showError">
          <ion-icon name="alert-circle-outline"></ion-icon>
          <span>The provided OTP does not match the one associated with the details given</span>
        </div>
      </div>

      <div class="action-buttons animated slideUp" style="animation-delay: 0.3s">
        <ion-button 
          expand="block" 
          class="primary-button" 
          (click)="submit()" 
          [disabled]="!isMyFormValid()"
        >
          <ion-icon name="paper-plane-outline" slot="start"></ion-icon>
          Request OTP
        </ion-button>

        <div class="secondary-actions" *ngIf="showLoginButton">
          <ion-button 
            expand="block" 
            class="secondary-button" 
            (click)="login()"
          >
            <ion-icon name="log-in-outline" slot="start"></ion-icon>
            Login
          </ion-button>
          <ion-button 
            expand="block" 
            class="text-button" 
            (click)="lostPassword()"
          >
            <ion-icon name="key-outline" slot="start"></ion-icon>
            Forgot Password
          </ion-button>
        </div>
      </div>
    </form>
  </div>

  <!-- Regular HTML Modal -->
  <div class="otp-modal-overlay" [class.show]="isModalOpen" (click)="setOpen(false)">
    <div class="otp-modal-content" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h2>Enter Verification Code</h2>
        <button class="close-button" (click)="setOpen(false)">×</button>
      </div>
      
      <div class="modal-body">
        <div class="modal-icon-wrapper">
          <ion-icon name="mail-open-outline" class="modal-icon"></ion-icon>
        </div>
        <p class="modal-instruction">We've sent a verification code to your mobile number</p>
        
        <div class="otp-input-wrapper">
          <input
            type="text"
            [(ngModel)]="smsotp"
            placeholder="Enter OTP code"
            maxlength="6"
            class="otp-input"
          />
        </div>
        
        <button 
          class="confirm-button" 
          (click)="submitOtp()"
        >
          Verify Code
        </button>
        
        <p class="resend-text">Didn't receive code? <a href="#" (click)="submit()">Resend</a></p>
      </div>
    </div>
  </div>
</ion-content>
