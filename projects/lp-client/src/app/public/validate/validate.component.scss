// Modern OTP Component Styles

.modern-toolbar {
  --background: var(--ion-color-base-shade, #00569a);
  --color: white;
  
  ion-title {
    font-size: 18px;
    font-weight: 600;
  }
  
  .back-button {
    --color: white;
  }
}

.modern-otp-content {
  --background: var(--ion-color-base);
  
  .otp-header-section {
    background: var(--ion-color-base-shade);
    padding: 40px 20px 30px;
    text-align: center;
    border-bottom-left-radius: 30px;
    border-bottom-right-radius: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    
    .otp-icon-wrapper {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, var(--ion-color-primary, #FF6B35) 0%, #ff8659 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;
      animation: pulse 2s infinite;
      
      .otp-main-icon {
        font-size: 40px;
        color: white;
      }
    }
    
    .otp-title {
      font-size: 24px;
      font-weight: 700;
      color: white;
      margin-bottom: 8px;
    }
    
    .otp-subtitle {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
    }
  }
}

.otp-form-card {
  padding: 20px;
  
  .otp-form {
    max-width: 400px;
    margin: 0 auto;
  }
  
  .form-field {
    margin-bottom: 20px;
    position: relative;
    
    .field-icon {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      
      ion-icon {
        font-size: 22px;
        color: var(--ion-color-primary, #FF6B35);
      }
    }
    
    .modern-input {
      --background: white;
      --padding-start: 50px;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      
      ion-input {
        --padding-top: 12px;
        --padding-bottom: 12px;
        font-size: 16px;
      }
      
      &.phone-input {
        ion-intl-tel-input {
          padding-left: 0;
        }
      }
    }
  }
}

.error-messages {
  margin: 20px 0;
  
  .error-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #dc3545;
    font-size: 14px;
    margin-bottom: 8px;
    padding: 10px 16px;
    background: #fee;
    border-radius: 8px;
    
    ion-icon {
      font-size: 18px;
      flex-shrink: 0;
    }
  }
}

.action-buttons {
  margin-top: 30px;
  
  .primary-button {
    --background: var(--ion-color-primary, #FF6B35);
    --background-hover: #ff5722;
    --border-radius: 12px;
    height: 50px;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    --box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    
    &[disabled] {
      --background: #ccc;
      --box-shadow: none;
    }
  }
  
  .secondary-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    .secondary-button {
      --background: white;
      --color: var(--ion-color-primary, #FF6B35);
      --border-radius: 12px;
      --border-color: var(--ion-color-primary, #FF6B35);
      --border-width: 2px;
      --border-style: solid;
      height: 45px;
      font-weight: 600;
    }
    
    .text-button {
      --background: transparent;
      --color: #666;
      --border-radius: 12px;
      height: 40px;
      font-weight: 500;
      
      &:hover {
        --color: var(--ion-color-primary, #FF6B35);
      }
    }
  }
}

// Regular HTML Modal Styles
.otp-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  
  &.show {
    opacity: 1;
    visibility: visible;
  }
  
  .otp-modal-content {
    background: white;
    border-radius: 20px;
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
    
    .modal-header {
      background: var(--ion-color-primary, #FF6B35);
      color: white;
      padding: 20px;
      border-radius: 20px 20px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }
      
      .close-button {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        
        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }
    
    .modal-body {
      padding: 20px;
      
      .modal-icon-wrapper {
        width: 60px;
        height: 60px;
        background: rgba(255, 107, 53, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20px auto;
        
        .modal-icon {
          font-size: 30px;
          color: var(--ion-color-primary, #FF6B35);
        }
      }
      
      .modal-instruction {
        text-align: center;
        color: #666;
        font-size: 16px;
        margin-bottom: 30px;
      }
      
      .otp-input-wrapper {
        margin-bottom: 30px;
        
        .otp-input {
          width: 100%;
          background: #f8f9fa;
          border: 2px solid #e0e0e0;
          border-radius: 12px;
          padding: 15px 20px;
          font-size: 20px;
          letter-spacing: 8px;
          text-align: center;
          font-weight: 600;
          outline: none;
          color: #333;
          
          &:focus {
            border-color: var(--ion-color-primary, #FF6B35);
          }
        }
      }
      
      .confirm-button {
        background: var(--ion-color-primary, #FF6B35);
        color: white;
        border: none;
        border-radius: 12px;
        width: 100%;
        height: 50px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        margin-bottom: 20px;
        
        &:hover {
          background: #ff5722;
        }
      }
      
      .resend-text {
        text-align: center;
        color: #666;
        font-size: 14px;
        
        a {
          color: var(--ion-color-primary, #FF6B35);
          text-decoration: none;
          font-weight: 600;
        }
      }
    }
  }
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// Animations
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.4);
  }
  70% {
    box-shadow: 0 0 0 20px rgba(255, 107, 53, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0);
  }
}

.animated {
  opacity: 0;
  
  &.slideUp {
    animation: slideUp 0.5s ease-out forwards;
  }
  
  &.fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Responsive
@media (max-width: 380px) {
  .otp-header-section {
    padding: 30px 15px 20px;
    
    .otp-title {
      font-size: 20px;
    }
    
    .otp-subtitle {
      font-size: 14px;
    }
  }
}