import { Component, Injector, OnInit } from '@angular/core';
import {
  AbstractControlOptions,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import {
  CustomValidators,
  KeyCloakService,
  LPMemberEntityTools,
  MemberProfile,
  MemberService,
  LssConfig,
  CountryItem,
  SystemService,
} from 'lp-client-api';
import { AbstractFormComponent } from 'mobile-components';
import { environment } from '../../../environments/environment';
import { Router } from '@angular/router';
import { Observable, of } from 'rxjs';

@Component({
  selector: 'app-signup',
  templateUrl: 'signup.component.html',
  styleUrls: ['signup.component.scss'],
})
export class SignupComponent
  extends AbstractFormComponent<MemberProfile>
  implements OnInit
{
  error?: string;
  memberProfile?: MemberProfile;
  myDate?: string;
  favourite: any = {};
  minLength: number = 9;
  startForm: any = {};
  phoneTogether: any = '+27';
  idType: string = 'nationalId'; // Default to national ID
  countries: Observable<CountryItem[]> = of([]); // Initialize the countries observable with a default empty value
  selectedStore: any;

  constructor(
    injector: Injector,
    protected readonly keyCloakService: KeyCloakService,
    private memberService: MemberService,
    private router: Router,
    public lssConfig: LssConfig,
    public override _systemService: SystemService  // Fix modifier order - public before override
  ) {
    super(injector);
    this.generateForm();
    // Initialize countries observable in constructor
    this.countries = this._systemService.listCountries('');
  }
  environment = environment;
  ngOnInit(): void {
    this.showLoadingModal('Loading..').then(() => {
      this.addGlobalSubscription(
        this.keyCloakService.authStatus.subscribe((value) => {
          if (value != null && value.eventName !== 'init') {
            if (value.eventName !== null && value.eventName === 'login') {
              if (this.keyCloakService.userProfile?.mustRegister) {
                this.memberProfile = {} as MemberProfile;
                this.memberProfile.externalId =
                  this.keyCloakService.userProfile.sub!;
                this.memberProfile.emailAddress =
                  this.keyCloakService.userProfile.email!;
                this.memberProfile.givenNames =
                  this.keyCloakService.userProfile.given_name!;
                this.memberProfile.surname =
                  this.keyCloakService.userProfile.family_name!;
                console.log('this.memberProfile', this.memberProfile);
                this._form.patchValue(this.memberProfile);
              }
            }
            this.dismissLoadingModal();
          } else {
            this.logout();
            this.dismissLoadingModal();
          }
        })
      );
    });
    this.startForm.phone = this.lssConfig.memberPhone;
    this.phoneTogether =
      this.lssConfig.memberPhone.dialCode +
      this.lssConfig.memberPhone.nationalNumber;
    if (this.lssConfig.memberCard)
      this.startForm.membershipNumber = this.lssConfig.memberCard;

    console.log('this.memberProfile', this.startForm);

    this._form.patchValue(this.startForm);
  }
  logout() {
    if (this.keyCloakService.authSuccess) {
      this.keyCloakService.keycloak?.logout();
    }
  }

  generateForm(): void {
    this._form = this._formBuilder.group(
      {
        givenNames: ['', [Validators.required]],
        surname: ['', [Validators.required]],
        title: [''],
        gender: [''],
        pin: ['', [Validators.minLength(5), Validators.maxLength(5)]],

        birthDate: ['', []],
        nationalIdNum: [
          '',
          [
            Validators.pattern('^[0-9]*$'),
            Validators.minLength(13),
            Validators.maxLength(13),
          ],
        ],
        passortNum: [
          '',
          [
            Validators.minLength(6),
            Validators.maxLength(9),
          ],
        ],
        membershipNumber: [
          '',
          [
            Validators.pattern('^[0-9]*$'),
            Validators.minLength(16),
            Validators.maxLength(16),
          ],
        ],
        phone: [
          LPMemberEntityTools.getIONTelephoneFromLPTel(
            LPMemberEntityTools.getEmptyPhone('CELL')
          ),
          [Validators.required],
        ],
        emailAddress: [
          '',
          Validators.compose([
            Validators.required,
            Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+.[a-z]{2,4}$'),
          ]),
        ],
        password: [
          '',
          Validators.compose([
            Validators.required,
            CustomValidators.passwordValidate(),
          ]),
        ],
        passwordConfirm: ['', [Validators.required]],
        terms: [false, Validators.requiredTrue],
        issueCountry: ['', this.idType === 'passport' ? Validators.required : null],
        expiryDate: ['', this.idType === 'passport' ? Validators.required : null],
      },
      {
        validators: Validators.compose([
          CustomValidators.matchValue(
            'password',
            'Password',
            'passwordConfirm',
            'Confirm Password'
          ),
        ]),
      } as AbstractControlOptions
    );
  }

  showTerms(): void {
    this._formState = this._formStateType.terms;
  }

  canSignup(): boolean {
    return this.isFormValid() && this.form.terms.value === true && this.hasValidId();
  }

  hasValidId(): boolean {
    const nationalId = this._form.get('nationalIdNum')?.value;
    const passortNum = this._form.get('passortNum')?.value;
    
    if (this.idType === 'nationalId') {
      return nationalId && nationalId.length === 13;
    } else if (this.idType === 'passport') {
      return passortNum && passortNum.length >= 6 && passortNum.length <= 9;
    }
    
    return false;
  }

  updateAddress(selectedStore: any) {
    this.selectedStore = selectedStore;
  }

  todaysDate12YearsAgo() {
    let date: any = new Date();
    date.setFullYear(date.getFullYear() - 12);
    //format date to yyyy-mm-dd
    date = date.toISOString().split('T')[0];
    return date;
  }

  // Add method to get today's date for min value in date input
  todaysDate(): string {
    return new Date().toISOString().split('T')[0];
  }

  signUp(): void {
    this.formData = this.getFormValues();
    let payload: any = this.formData;
    console.log('payload', payload);
    
    // Clear the ID field that's not being used
    if (this.idType === 'nationalId') {
      payload.passortNum = '';
      // Remove expiryDate and issueCountry for ID signups
      delete payload.expiryDate;
      delete payload.issueCountry;
    } else if (this.idType === 'passport') {
      payload.nationalIdNum = '';
    }
    
    if (payload.phone) {
      payload.personTelephone = [
        {
          telephoneType: 'CELL',
          countryCode: payload.phone.dialCode ? payload.phone.dialCode : '',
          telephoneCode: '',
          telephoneNumber: payload.phone.nationalNumber
            ? payload.phone.nationalNumber
            : '',
          telephoneExtension: '',
          phoneable: '',
          smsable: '',
        },
      ];
    }
    console.log('personTelephone', payload.personTelephone);

    if (this.selectedStore) {
      payload.preferenceList = [
        {
          level1: 'PART',
          level2: this.selectedStore.partnerId,
        },
      ];
    }

    if (payload.phone) delete payload.phone;

    if (payload.birthDate) payload.birthDate = payload.birthDate + 'T00:00:00';
    else if (payload.birthDate === '') delete payload.birthDate;
    console.log('payload', payload);

    if (this.formData) {
      this.showLoadingModal('Please wait while we sign you up!').then(() => {
        this.memberService.register(payload).subscribe({
          error: (error) => {
            this.dismissLoadingModal();
            this._formState = this._formStateType.fail;
            this.error = error.error.detail;
            console.log(error.error.detail);
            this.presentToast({
              message: error.error.detail,
              color: 'danger',
              position: 'bottom',
            }).then();
          },
          next: (body) => {
            console.log('body', body);
            this.dismissLoadingModal();
            this._formState = this._formStateType.pass;
            this.formData = body;
            this.presentToast({
              message: 'Your account has been created',
              position: 'bottom',
            }).then();

            this.router.navigate(['/app/home']);
          },
        });
      });
    }
  }
}
