.signup {
    .state-before {
        
    }

    .state-success {
        background-image: url('~/src/assets/images/signup.png');
        background-size: cover;
        background-position: center;
        height: 100%;
        display: flex;
        font-weight: 300;
    
       
    }
}
ion-modal {
    --width: 290px;
    --height: 392px;
    --border-radius: 8px;
  }

  ion-modal ion-datetime {
    height: 392px;
  }
  ::-webkit-calendar-picker-indicator {
    filter: invert(1);
}
.center {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    //auto x;
    margin: auto 0;
}
.formbackground {
    --background: var(--ion-color-base-shade);
    color: var(--ion-color-base-contrast);
    height: 70vh
   }
.success-text {
    text-align: center;
    flex-grow: 1;
    align-self: flex-end;
    margin-bottom: 20%;
    color: var(--ion-color-primary);
    ion-button {
        --background: var(--ion-color-primary);
        --color: var(--ion-color-primary-contrast);
        --border-radius:64px;
    }
}

.title {
    background: var(--ion-color-primary);
    color: var(--ion-color-primary-contrast);
    height: 100px;
}

.app-background {
    --background: var(--ion-color-base)
}

.header-backgrounds {
    background: var(--ion-color-base-shade);
    display: flex;
    justify-content: space-between
}

.logo {
    height: 80px;
    //margin auto x
    margin: auto 0;
}

.w-full {
    width: 100%;
}

.logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.app {
    --background: var(--ion-color-primary);
}
.p-0 {
    padding: 0 !important;
    --padding-start: 0px !important;
    width: 100%;
}

.save {
	--background: var(--ion-color-primary);

}
.header-button {
    // color: var(--ion-color-primary-contrast);
}

#time-button {
    display: none !important;
}
// .toolbar-content {
// height: 100%;
// width: 100%;
//   display: flex;
//   align-items: center;
//   justify-content: center;
// }
ion-popover { --width: 288px; --offset-y: -42px; color: var(--ion-color-primary); }

ion-title {
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 90px 1px;
    width: 100%;
    height: 100%;
    text-align: center;
  }

.terms-link {
    display: flex;
    justify-content: center;
    padding-block-start: 4px;
    padding-block-end: 24px;
    font-size: 1.1rem;
}