.app-background {
    --background: var(--ion-color-base);
    --overflow: hidden;
  }
  
  ion-content {
    --overflow: hidden;
  }
  .logo {
    width: 100vw;
    margin-top: 50px
    img {
      width: 70vw;
    }
  }
  .action-card {
    text-align: center;
    background-color: var(--ion-color-primary-shade);
    border-radius: 1rem;
    box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.6);
  }
  .card-background {
    background-color: #fff;
    width: 100vw;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .center {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .icon {
      color:  var(--ion-color-primary-shade);
      font-size: 18px
  }
  .action-icon {
    position: relative;
    top: -25px;
    right: -23px;
    border-radius: 3rem;
    width: 4rem;
    height: 4rem;
    box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.4);
    text-align: center;
    padding: 25px 0;
    background-color: var(--ion-color-tertiary);
  }
  .front-text {
    color: var(--ion-color-primary-contrast);
    text-align: center;
    font-size: 36px;
  }
  
  .points {
    background-color: var(--ion-color-secondary);
    width: 100%;
    height: 75px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
  }
  .centerpoints {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .title-shortcut {
    margin-left: 20px;
    margin-bottom: 45px;
    margin-top: 70px;
  }
  .content {
      margin-top: 50px
  }
  .contents {
    margin-top: 30px
}
  .action-name {
    position: relative;
  
    top: -20px;
  }
  
  .landing_old {
    // background-image: url("~/src/assets/images/landing.jfif");
    background-size: cover;
    background-position: center;
    --background: transparent;
    .header {
      text-align: center;
  
      .logo {
        position: relative;
        padding-bottom: 30px;
      }
  
      img {
        margin: auto;
        display: block;
      }
    }
  
    ion-card {
      box-shadow: none;
      border-radius: 20px;
  
      h1 {
        font-weight: bold;
        font-size: 20px;
        text-align: center;
        margin-bottom: 5px;
      }
  
      p {
        text-align: center;
        font-size: 16px;
        margin-bottom: 45px;
      }
  
      .line-text {
        padding: 2px;
        margin-top: 20px;
        position: relative;
  
        .line {
          height: 1px;
          background: #858585;
          display: block;
        }
  
        .text {
          position: absolute;
          top: -8px;
          left: 50%;
          transform: translateX(-50%);
          background: #fff;
          padding: 0 15px;
          font-size: 10px;
          font-weight: 500;
        }
  
        //@media (prefers-color-scheme: dark) {
        //    .text {
        //        background: #1e1e1e;
        //    }
        //}
      }
  
      .socials {
        margin-top: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
  
        ion-button {
          margin: 0 5px;
          --padding-start: 12px;
          --padding-end: 12px;
  
          ion-icon {
            font-size: 20px;
          }
  
          a {
            color: #fff;
          }
        }
  
        .btn-facebook {
          --background: #567aa3;
        }
  
        .btn-twitter {
          --background: #48cff5;
        }
  
        .btn-linkedin {
          --background: #0077b5;
        }
      }
    }
  }
  
  ion-toolbar {
    display: none;
  }
  .landing2 {
    --background: var(--ion-color-base);
    padding: 40px;
  }
  .landing {
    --background: var(--ion-color-base);
  padding: 20px;
    position: relative;
    height: 100vh;
  
    .logo {
      position: absolute;
      width: 100vw;
      top: 4%;
      -ms-transform: translateY(-4%);
      transform: translateY(-4%);
      display: flex;
      justify-content: center;
  
      img {
        width: 50vw;
        max-width: 300px;
      }
    }
  
    .content {
      position: absolute;
      width: 100vw;
      top: 50%;
      -ms-transform: translateY(-50%);
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;
  
      h2 {
        color: #fff;
        margin-bottom: 6px;
      }
  
      h4 {
        color: #fff;
        margin-top: 0;
        margin-bottom: 14px;
        font-weight: 300;
      }
  
      ion-button {
        width: 80vw;
      }
    }
  
    .socials {
      position: absolute;
      width: 100vw;
      bottom: 3%;
      -ms-transform: translateY(3%);
      transform: translateY(3%);
      display: flex;
      justify-content: space-evenly;
      align-items: center;
  
      ion-button {
        --background: transparent;
        --box-shadow: none;
      }
  
      ion-icon {
        color: #fff;
        font-size: 2.4em;
      }
    }
  }
  