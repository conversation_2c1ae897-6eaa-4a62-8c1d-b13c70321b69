import { Component, Injector, ChangeDetectorRef } from '@angular/core';
import { AbstractComponent } from 'mobile-components';
import {
  KeyCloakService,
  MemberProfile,
  MemberService,
  LssConfig,
} from 'lp-client-api';
import { Router } from '@angular/router';
@Component({
  selector: 'app-landing',
  templateUrl: 'landing.component.html',
  styleUrls: ['landing.component.scss'],
})
export class LandingComponent extends AbstractComponent {
  profile?: MemberProfile;
  constructor(
    injector: Injector,
    public kc: KeyCloakService,
    private memberService: MemberService,
    protected readonly router: Router,
    private cd: ChangeDetectorRef,
    public lssConfig: LssConfig
  ) {
    super(injector);
  }
  ngOnInit() {
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data: any) => {
        this.profile = data;
        if (this.profile) this.getBalance();
        this.detectChanges();
      })
    );
  }

  getBalance() {
    this.memberService
      .memberBalance(this.kc.lpUniueReference)
      .subscribe((data: any) => {
        this.profile = { ...this.profile, ...data };
        console.log(this.profile);
        this.detectChanges();
      });
  }
}
