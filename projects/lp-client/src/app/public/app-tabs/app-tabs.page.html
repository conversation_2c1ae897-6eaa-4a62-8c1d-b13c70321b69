<ion-tabs>
  <ion-tab-bar slot="bottom" mode="ios" class="modern-tab-bar" style="display: none !important;">
    <ion-tab-button tab="home" #home class="tab-button">
      <div class="tab-icon-wrapper" [class.selected]="home.selected">
        <ion-icon [name]="home.selected ? 'home' : 'home-outline'"></ion-icon>
      </div>
      <ion-label>Home</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="stores" #stores class="tab-button">
      <div class="tab-icon-wrapper" [class.selected]="stores.selected">
        <ion-icon [name]="stores.selected ? 'location' : 'location-outline'"></ion-icon>
      </div>
      <ion-label>Stores</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="transactions" #transactions class="tab-button">
      <div class="tab-icon-wrapper" [class.selected]="transactions.selected">
        <ion-icon [name]="transactions.selected ? 'receipt' : 'receipt-outline'"></ion-icon>
      </div>
      <ion-label>History</ion-label>
    </ion-tab-button>    

    <ion-tab-button tab="virtualcard" #virtualcard class="tab-button">
      <div class="tab-icon-wrapper" [class.selected]="virtualcard.selected">
        <ion-icon [name]="virtualcard.selected ? 'card' : 'card-outline'"></ion-icon>
      </div>
      <ion-label>Card</ion-label>
    </ion-tab-button>
    
    <ion-tab-button tab="account" #account class="tab-button">
      <div class="tab-icon-wrapper" [class.selected]="account.selected">
        <ion-icon [name]="account.selected ? 'person' : 'person-outline'"></ion-icon>
      </div>
      <ion-label>Profile</ion-label>
    </ion-tab-button> 
  </ion-tab-bar>
</ion-tabs>