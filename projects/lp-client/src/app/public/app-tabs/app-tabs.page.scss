/* Modern Tab Bar Component Styles */

// Hide the tab bar completely
ion-tab-bar {
  display: none !important;
  visibility: hidden !important;
  height: 0 !important;
  opacity: 0 !important;
}

.modern-tab-bar {
  display: none !important;
  
  .tab-button {
    .tab-icon-wrapper {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 12px;
      transition: all 0.3s ease;
      margin: 0 auto 4px;
      
      ion-icon {
        font-size: 24px;
        transition: all 0.3s ease;
      }
      
      &.selected {
        background: rgba(255, 107, 53, 0.1);
        
        ion-icon {
          color: var(--ion-color-primary, #FF6B35);
          transform: scale(1.1);
        }
      }
    }
  }
}