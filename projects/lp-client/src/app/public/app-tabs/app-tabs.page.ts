import { Component, Optional } from '@angular/core';
import { Router } from '@angular/router';
import { OnDestroy, OnInit } from '@angular/core';
import { IonRouterOutlet, Platform } from '@ionic/angular';
import { App } from '@capacitor/app';

@Component({
  selector: 'app-tabs',
  templateUrl: 'app-tabs.page.html',
  styleUrls: ['app-tabs.page.scss']
})
export class AppTabsPage implements OnInit, OnDestroy {

  constructor(protected readonly router: Router,
              private platform: Platform,
              @Optional() private routerOutlet: IonRouterOutlet) { 
    this.hardwareBackButton(); // Handle hardware back button to exit the application
  }

  ngOnInit(): void { }

  ngOnDestroy(): void { }

  hardwareBackButton(): void {
    this.platform.backButton.subscribeWithPriority(-1, () => {
      if (!this.routerOutlet.canGoBack()) {
        if (this.router.url.includes('/app/home')) {
          App.exitApp();
        } else {
          this.router.navigate(['/app/home']);
        }        
      }
    });
  }

}
