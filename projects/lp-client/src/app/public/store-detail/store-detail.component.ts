import { Component, <PERSON><PERSON><PERSON><PERSON>, ViewChild, Injector } from '@angular/core';
import { Router } from '@angular/router';
import { Partner, LssConfig, MemberService, MemberProfile } from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';

@Component({
  selector: 'app-store-detail',
  templateUrl: 'store-detail.component.html',
  styleUrls: ['store-detail.component.scss'],
})
export class StoreDetailComponent extends AbstractComponent {
  store?: Partner;
  storeMarkers: any[] = [];
  center: google.maps.LatLngLiteral = { lat: -26.2041, lng: 28.0473 }; // Default center
  options: google.maps.MapOptions = {
    zoomControl: false,
    maxZoom: 18,
    minZoom: 4,
    streetViewControl: false,
  };
  zoom: number = 15;
  profile?: MemberProfile;

  constructor(
    injector: Injector,
    private router: Router,
    private memberService: MemberService,
    public lssConfig: LssConfig
  ) {
    super(injector);
  }

  ionViewWillEnter(): void {
    const data = history.state.data;
    if (data === undefined) {
      this.router.navigate(['/public/stores']); // Navigate back to the store listing in the case of no store selected
    } else {
      this.store = new Partner({
        partnerId: data.partnerId,
        partnerName: data.partnerName,
      });
      console.log('store', this.store);
      this.store.address = data.address;
      this.store.telephone = data.telephone;
      this.store.operatingHours = data.operatingHours;
      this.store.partnerMore = data.partnerMore;
      console.log(
        'LOCATION : ' + this.store.getPartnerMoreByType('LOCATION')?.value
      );
      this.detectChanges(); // Force change detection
    }
  }

  ngOnInit() {
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        this.detectChanges();
      })
    );
  }

  override ionViewDidEnter(): void {
    super.ionViewDidEnter();
    if (this.store) {
      this.showStoreLocation();
    }
  }

  override ionViewDidLeave(): void {
    this.storeMarkers = [];
  }

  private async showStoreLocation() {
    if (this.store && this.store.getPartnerMoreByType('LOCATION')) {
      const locationValue = this.store.getPartnerMoreByType('LOCATION')?.value;
      if (locationValue) {
        const coords = locationValue.split(',');
        this.center = {
          lat: Number(coords[0]),
          lng: Number(coords[1]),
        };

        this.storeMarkers = []; // Clear existing markers
        this.storeMarkers.push({
          position: {
            lat: this.center.lat,
            lng: this.center.lng,
          },
        });
        
        this.detectChanges();
      }
    }
  }

  formatDay(day: string): string {
    // Format day to shorter version if needed
    const dayMap: { [key: string]: string } = {
      'Monday': 'Mon',
      'Tuesday': 'Tue',
      'Wednesday': 'Wed',
      'Thursday': 'Thu',
      'Friday': 'Fri',
      'Saturday': 'Sat',
      'Sunday': 'Sun'
    };
    return dayMap[day] || day;
  }

  isToday(day: string): boolean {
    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });
    return today === day;
  }
}
