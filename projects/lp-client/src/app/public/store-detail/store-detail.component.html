<ion-content class="app-background">
  <!-- Modern Header -->
  <lib-head-logo
    [names]="profile?.givenNames + ' ' + profile?.surname"
    type="store-detail"
    [storeName]="store?.partnerName"
    [balance]="profile?.currentBalance"
    [src]="lssConfig.pages.landing.loggedinIcon"
  />

  <!-- Store Detail Section -->
  <div class="store-detail-section" *ngIf="store">
    <!-- Map Container -->
    <div class="map-container">
      <google-map
        class="detail-map"
        width="100%"
        height="100%"
        [options]="options"
        [center]="center"
        [zoom]="zoom"
      >
        <map-marker
          *ngFor="let marker of storeMarkers"
          [position]="marker.position"
        ></map-marker>
      </google-map>
      
      <!-- Get Directions Button Overlay -->
      <div class="map-overlay">
        <a
          [href]="'https://www.google.com/maps/dir/?api=1&destination=' + store.getPartnerMoreByType('LOCATION')?.value"
          target="_blank"
          class="directions-btn"
        >
          <ion-icon name="navigate-outline"></ion-icon>
          Get Directions
        </a>
      </div>
    </div>

    <!-- Store Information -->
    <div class="store-info-container">
      <!-- Address Card -->
      <div class="info-card" *ngIf="store.getAddressByType('PADR')">
        <div class="info-header">
          <div class="info-icon">
            <ion-icon name="location-outline"></ion-icon>
          </div>
          <h3>Store Address</h3>
        </div>
        <div class="info-content">
          <p class="address-line">{{ store.getAddressByType("PADR")?.line1 }}</p>
          <p class="address-line" *ngIf="store.getAddressByType('PADR')?.line2">
            {{ store.getAddressByType("PADR")?.line2 }}
          </p>
          <p class="address-line">
            {{ store.getAddressByType("PADR")?.suburbDesc }}, 
            {{ store.getAddressByType("PADR")?.cityDesc }}
          </p>
          <p class="address-line">
            {{ store.getAddressByType("PADR")?.provinceDesc }}
            {{ store.getAddressByType("PADR")?.postCode }}
          </p>
        </div>
      </div>

      <!-- Contact Card -->
      <div class="info-card" *ngIf="store.getTelephoneByType('WORK')">
        <div class="info-header">
          <div class="info-icon contact">
            <ion-icon name="call-outline"></ion-icon>
          </div>
          <h3>Contact Information</h3>
        </div>
        <div class="info-content">
          <a 
            [href]="'tel:' + store.getTelephoneByType('WORK')?.telephoneNumber" 
            class="phone-link"
          >
            <ion-icon name="call"></ion-icon>
            {{ store.getTelephoneByType("WORK")?.telephoneNumber }}
          </a>
        </div>
      </div>

      <!-- Trading Hours Card -->
      <div class="info-card" *ngIf="store.operatingHours && store.operatingHours.length > 0">
        <div class="info-header">
          <div class="info-icon hours">
            <ion-icon name="time-outline"></ion-icon>
          </div>
          <h3>Trading Hours</h3>
        </div>
        <div class="info-content">
          <div class="hours-grid">
            <div 
              class="hours-item" 
              *ngFor="let operatingHours of store.operatingHours"
              [class.today]="isToday(operatingHours.dayOfWeek)"
            >
              <span class="day">{{ formatDay(operatingHours.dayOfWeek) }}</span>
              <span class="time">{{ operatingHours.description }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <a 
          [href]="'tel:' + store.getTelephoneByType('WORK')?.telephoneNumber" 
          class="action-btn call"
          *ngIf="store.getTelephoneByType('WORK')"
        >
          <ion-icon name="call"></ion-icon>
          <span>Call Store</span>
        </a>
        <a
          [href]="'https://www.google.com/maps/dir/?api=1&destination=' + store.getPartnerMoreByType('LOCATION')?.value"
          target="_blank"
          class="action-btn directions"
        >
          <ion-icon name="navigate"></ion-icon>
          <span>Get Directions</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-state" *ngIf="!store">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading store details...</p>
  </div>
</ion-content>