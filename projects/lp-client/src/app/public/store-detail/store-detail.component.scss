/* Modern Store Detail Styles */

/* App Background */
.app-background {
  --background: var(--ion-color-base, #0072bc);
  height: 100%;
}

/* Store Detail Section */
.store-detail-section {
  margin-top: -30px;
  position: relative;
  z-index: 2;
  animation: slideUp 0.6s ease-out;
}

/* Map Container */
.map-container {
  height: 35vh;
  position: relative;
  border-radius: 24px;
  overflow: hidden;
  margin: 0 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  animation: fadeIn 0.8s ease-out;
  
  .detail-map {
    width: 100%;
    height: 100%;
  }
}

/* Map Overlay */
.map-overlay {
  position: absolute;
  bottom: 16px;
  right: 16px;
  z-index: 10;
  
  .directions-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--ion-color-primary, #FF6B35);
    color: white;
    padding: 12px 20px;
    border-radius: 24px;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
    transition: all 0.3s ease;
    
    ion-icon {
      font-size: 18px;
    }
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

/* Store Info Container */
.store-info-container {
  padding: 20px;
  background: var(--ion-color-base, #0072bc);
  min-height: calc(100vh - 35vh - 200px);
}

/* Info Cards */
.info-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  animation: slideIn 0.6s ease-out;
  animation-fill-mode: both;
  
  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
}

.info-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  
  .info-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 107, 53, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    ion-icon {
      font-size: 24px;
      color: var(--ion-color-primary, #FF6B35);
    }
    
    &.contact {
      background: rgba(40, 167, 69, 0.1);
      
      ion-icon {
        color: #28a745;
      }
    }
    
    &.hours {
      background: rgba(23, 162, 184, 0.1);
      
      ion-icon {
        color: #17a2b8;
      }
    }
  }
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #212121;
  }
}

.info-content {
  .address-line {
    margin: 0 0 4px 0;
    font-size: 15px;
    color: #666;
    line-height: 1.5;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .phone-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #28a745;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    
    ion-icon {
      font-size: 18px;
    }
    
    &:hover {
      color: #218838;
      transform: translateX(2px);
    }
  }
}

/* Hours Grid */
.hours-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.hours-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .day {
    font-weight: 600;
    color: #666;
    font-size: 14px;
    min-width: 40px;
  }
  
  .time {
    font-size: 14px;
    color: #666;
    text-align: right;
  }
  
  &.today {
    .day {
      color: var(--ion-color-primary, #FF6B35);
    }
    
    .time {
      color: #212121;
      font-weight: 600;
    }
  }
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-top: 24px;
  padding: 0 20px 24px;
  background: var(--ion-color-base, #0072bc);
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  animation: fadeIn 0.8s ease-out;
  animation-delay: 0.4s;
  animation-fill-mode: both;
  
  ion-icon {
    font-size: 20px;
  }
  
  &.call {
    background: #28a745;
    color: white;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    
    &:hover {
      background: #218838;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }
  }
  
  &.directions {
    background: var(--ion-color-primary, #FF6B35);
    color: white;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
    
    &:hover {
      background: var(--ion-color-primary-shade);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
    }
  }
  
  &:active {
    transform: translateY(0);
  }
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 200px);
  animation: fadeIn 0.4s ease-out;
  
  ion-spinner {
    --color: var(--ion-color-primary, #FF6B35);
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    color: white;
    margin: 0;
  }
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 375px) {
  .map-container {
    height: 30vh;
    margin: 0 16px;
  }
  
  .map-overlay {
    bottom: 12px;
    right: 12px;
    
    .directions-btn {
      padding: 10px 16px;
      font-size: 13px;
      
      ion-icon {
        font-size: 16px;
      }
    }
  }
  
  .store-info-container {
    padding: 16px;
  }
  
  .info-card {
    padding: 16px;
    margin-bottom: 12px;
  }
  
  .info-header {
    gap: 12px;
    margin-bottom: 12px;
    
    .info-icon {
      width: 40px;
      height: 40px;
      
      ion-icon {
        font-size: 20px;
      }
    }
    
    h3 {
      font-size: 16px;
    }
  }
  
  .info-content {
    .address-line {
      font-size: 14px;
    }
    
    .phone-link {
      font-size: 15px;
    }
  }
  
  .hours-item {
    .day, .time {
      font-size: 13px;
    }
  }
  
  .quick-actions {
    padding: 0 16px 20px;
    gap: 10px;
  }
  
  .action-btn {
    padding: 14px;
    font-size: 13px;
    
    ion-icon {
      font-size: 18px;
    }
  }
}