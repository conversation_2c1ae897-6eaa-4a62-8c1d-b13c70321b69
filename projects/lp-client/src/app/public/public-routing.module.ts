import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AppTabsPage } from './app-tabs/app-tabs.page';
import { LandingComponent } from './landing/landing.component';
import { NotificationsComponent } from './notifications/notifications.component';
import { PasswordComponent } from './password/password.component';
import { SignupComponent } from './signup/signup.component';
import { StoreDetailComponent } from './store-detail/store-detail.component';
import { StoresComponent } from './stores/stores.component';
import { AuthGuardService } from 'lp-client-api';
import { OtpComponent } from './otp/otp.component';
import { ValidateComponent } from './validate/validate.component';

const routes: Routes = [
  {
    path: '',
    component: AppTabsPage, // This is the default non-authenticated route
  },
  {
    path: 'app',
    component: AppTabsPage, // This is the default non-authenticated route
  },
  {
    path: 'landing',
    component: LandingComponent,
  },
  {
    path: 'signup',
    component: SignupComponent,
  },
  {
    path: 'password',
    component: PasswordComponent,
    pathMatch: 'full',
  },
  {
    path: 'notifications',
    component: NotificationsComponent,
    pathMatch: 'full',
  },
  {
    path: 'stores',
    component: StoresComponent,
    pathMatch: 'full',
  },
  {
    path: 'storedetail',
    component: StoreDetailComponent,
    pathMatch: 'full',
  },
  {
    path: 'otp',
    component: OtpComponent,
    pathMatch: 'full',
  },
  {
    path: 'validate',
    component: ValidateComponent,
    pathMatch: 'full',
  },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PublicRoutingModule {}
