// Modern Notifications Component Styles

.modern-notifications-content {
  --background: var(--ion-color-base, #0072bc);
}

.notifications-header {
  background: linear-gradient(135deg, var(--ion-color-primary, #FF6B35) 0%, #ff8659 100%);
  padding: 40px 20px 30px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  
  .page-title {
    font-size: 28px;
    font-weight: 700;
    color: white;
    margin: 0 0 8px 0;
  }
  
  .page-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
  }
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  min-height: 60vh;
  
  .empty-icon-wrapper {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    
    .empty-icon {
      font-size: 60px;
      color: white;
    }
  }
  
  .empty-title {
    font-size: 24px;
    font-weight: 600;
    color: white;
    margin: 0 0 12px 0;
  }
  
  .empty-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
    margin: 0;
    max-width: 280px;
  }
}

// Notifications List
.notifications-list {
  padding: 20px;
  padding-bottom: 80px;
}

.notification-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  
  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 1);
  }
  
  .notification-header {
    display: flex;
    align-items: center;
    padding: 16px;
    cursor: pointer;
    
    .notification-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 134, 89, 0.1) 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      flex-shrink: 0;
      
      ion-icon {
        font-size: 24px;
        color: var(--ion-color-primary, #FF6B35);
      }
    }
    
    .notification-info {
      flex: 1;
      
      .notification-title {
        font-size: 16px;
        font-weight: 600;
        color: #1a1a1a;
        margin: 0 0 4px 0;
      }
      
      .notification-time {
        font-size: 14px;
        color: #6c757d;
        margin: 0;
      }
    }
    
    .expand-icon {
      font-size: 20px;
      color: #6c757d;
      transition: transform 0.3s ease;
      
      &.expanded {
        transform: rotate(180deg);
      }
    }
  }
  
  .notification-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    
    &.show {
      max-height: 300px;
    }
    
    .notification-message {
      padding: 0 16px 16px 80px;
      color: #495057;
      font-size: 15px;
      line-height: 1.6;
      margin: 0;
    }
    
    .mark-read-button {
      margin-left: 64px;
      margin-bottom: 16px;
      --color: var(--ion-color-primary, #FF6B35);
      font-weight: 600;
      
      ion-icon {
        margin-right: 4px;
      }
    }
  }
}

// Animations
.animated {
  opacity: 0;
  
  &.fadeIn {
    animation: fadeIn 0.5s ease-out forwards;
  }
  
  &.slideUp {
    animation: slideUp 0.5s ease-out forwards;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive
@media (max-width: 380px) {
  .notifications-header {
    padding: 30px 15px 24px;
    
    .page-title {
      font-size: 24px;
    }
    
    .page-subtitle {
      font-size: 14px;
    }
  }
  
  .notification-card {
    .notification-header {
      padding: 12px;
      
      .notification-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
        
        ion-icon {
          font-size: 20px;
        }
      }
      
      .notification-info {
        .notification-title {
          font-size: 15px;
        }
        
        .notification-time {
          font-size: 13px;
        }
      }
    }
    
    .notification-content {
      .notification-message {
        padding-left: 64px;
        font-size: 14px;
      }
      
      .mark-read-button {
        margin-left: 52px;
      }
    }
  }
}