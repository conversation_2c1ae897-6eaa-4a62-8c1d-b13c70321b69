:root {
  --nt-bg: #fff;
  --nt-text: #1a1a1a;
  --nt-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  --nt-accent: #FF6B35;
  --nt-secondary: #6c757d;
  --nt-card-radius: 16px;
  --nt-padding: 20px;
  --nt-max-width: 360px;
}

@media (prefers-color-scheme: dark) {
  :root,
  .dark {
    --nt-bg: #141517;
    --nt-text: #fff;
    --nt-shadow: 0 2px 14px rgba(0,0,0,0.35);
    --nt-accent: #ff8659;
    --nt-secondary: #adb5bd;
  }
}

.custom-notification-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: var(--nt-max-width);
  margin: 0 auto;
  width: 100%;
  background: var(--nt-bg);
  color: var(--nt-text);
  
  @media (max-width: 480px) {
    max-width: 100%;
    margin: 0 8px;
    border-radius: 12px;
  }
}

.custom-notification-card {
  background: var(--nt-bg);
  color: var(--nt-text);
  border-radius: var(--nt-card-radius);
  box-shadow: var(--nt-shadow);
  margin-bottom: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.2s, transform 0.2s;

  &.has-image, &.has-media {
    flex-direction: row;
    align-items: center;
    .notification-media {
      margin-right: 16px;
      border-radius: 8px;
      width: 48px;
      height: 48px;
      object-fit: cover;
    }
  }

  &.has-actions {
    .notification-actions {
      margin-top: 12px;
      display: flex;
      gap: 8px;
      justify-content: flex-end;
    }
  }

  &:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
    transform: translateY(-2px);
  }

  .notification-header {
    padding: 16px;
    display: flex;
    align-items: center;
    .notification-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--nt-text);
      margin-right: auto;
    }
    .notification-time {
      font-size: 14px;
      color: var(--nt-secondary);
    }
  }

  .notification-content {
    padding: 0 16px 16px 16px;
    color: var(--nt-secondary);
    font-size: 15px;
    line-height: 1.6;
    &.has-media {
      padding-left: 0;
    }
  }

  .notification-progress {
    height: 4px;
    background: var(--nt-secondary);
    border-radius: 2px;
    margin: 0 16px 12px 16px;
    overflow: hidden;
    .progress-bar {
      display: block;
      height: 100%;
      width: 0%;
      background: linear-gradient(90deg, var(--nt-accent), var(--nt-accent), #ff8659 80%);
      animation: progress-width 2s linear forwards;
    }
  }

  .notification-actions {
    padding: 0 16px 16px 16px;
    
    .nt-btn {
      border: none;
      outline: none;
      background: var(--nt-accent);
      color: #fff;
      border-radius: 8px;
      padding: 8px 16px;
      font-weight: 600;
      box-shadow: 0 1px 4px rgba(0,0,0,0.08);
      transition: background 0.2s;
      &.secondary {
        background: var(--nt-secondary);
        color: var(--nt-text);
      }
      &:hover {
        background: #ff8659;
      }
    }
  }
}

@keyframes progress-width {
  from { width: 0%; }
  to { width: 100%; }
}

// High contrast (accessibility)
@media (forced-colors: active) {
  .custom-notification-card, .custom-notification-list {
    border: 2px solid CanvasText;
    background: Canvas !important;
    color: CanvasText !important;
    box-shadow: none !important;
  }
  .nt-btn {
    background: Highlight !important;
    color: HighlightText !important;
  }
}

