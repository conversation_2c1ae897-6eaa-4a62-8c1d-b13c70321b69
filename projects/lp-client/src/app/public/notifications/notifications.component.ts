import { Component, OnInit } from '@angular/core';
import {
  MemberService,
  Statement,
  KeyCloakService,
  MemberProfile,
} from 'lp-client-api';
// import { AbstractFormComponent } from 'mobile-components';
import { environment } from '../../../environments/environment';
@Component({
  selector: 'app-notifications',
  templateUrl: 'notifications.component.html',
  styleUrls: ['notifications.component.scss'],
})
// extends AbstractFormComponent<Statement>
export class NotificationsComponent implements OnInit {
  //loading = false;
  searchRender = false;
  notifications: any = [];
  count?: any = 0;
  beginDate?: any;
  endDate?: Date;
  profile?: MemberProfile;

  constructor(
    private memberService: MemberService,
    private kc: KeyCloakService
  ) {}
  environment = environment;

  ngOnInit(): void {
    this.loadMember();
  }

  ionViewDidEnter(): void {
    this.search();
  }

  search(): void {
    this.memberService.getNotifications(this.kc.lpUniueReference).subscribe({
      error: (error) => {
        console.log(error.error.detail);
      },
      next: (body) => {
        console.log('body', body);
        if (body !== undefined) {
          this.notifications = body;
        }
        // this.dismissLoadingModal();
      },
    });
    // });
  }

  notiRead(notificationId: any) {
    console.log('not', notificationId);
    this.memberService
      .readNotification(this.kc.lpUniueReference, notificationId)
      .subscribe({
        error: (error: any) => {
          console.log(error.error.detail);
        },
        next: (body: any) => {
          console.log('body', body);
          let index = this.notifications.findIndex(
            (item: any) => item.noteSeq === notificationId
          );
          this.notifications.splice(index, 1);
          if (body !== undefined) {
            this.count = body;
          }
          // this.dismissLoadingModal();
        },
      });
  }

  toggleNotification(notification: any) {
    notification.expanded = !notification.expanded;
  }

  getNotification(notificationId: any) {
    this.memberService
      .getNotification(this.kc.lpUniueReference, notificationId)
      .subscribe({
        error: (error: any) => {
          console.log(error.error.detail);
        },
        next: (body: any) => {
          console.log('body', body);
          if (body !== undefined) {
            this.count = body;
          }
          // this.dismissLoadingModal();
        },
      });
  }

  async loadMember() {
    // this.showLoadingModal('Fetching your account').then(() => {
    this.memberService
      .getProfile(this.kc.lpUniueReference)
      .subscribe((data: MemberProfile) => {
        this.profile = data;
        // this.dismissLoadingModal();
      });
    // });
  }
}
