import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { forkJoin } from 'rxjs';
import { App, AppInfo } from '@capacitor/app';
import { GetResult, Preferences } from '@capacitor/preferences';
import { firstValueFrom } from 'rxjs';
import {
  KeyCloakService,
  LssConfig,
  DeviceDetails,
  deepMerge,
} from 'lp-client-api';
import { deepmerge } from 'deepmerge-ts';
import { Device, DeviceInfo } from '@capacitor/device';
import { environment } from '../../environments/environment';

/**
 *
 * This is an application specific service so it will not reside in the normal API project.
 */
@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  private config: LssConfig;
  private _client: string = '';
  private _env: string = '';
  private _realm: string = '';
  private _isProd: boolean = false;
  private _configUrl: string =
    'https://connect.loyaltyplus.aero/public/loyaltyapi/1.0.0/mobile/config';

  constructor(
    private httpClient: HttpClient,
    private keycloak: KeyCloakService,
    @Inject('environment') private env: any
  ) {
    this.config = JSON.parse(JSON.stringify(this.env.lssConfig));
  }

  get sysConfig(): LssConfig {
    return this.config;
  }

  loadConfig() {
    return forkJoin([this.loadConfigInternal()]);
  }

  private async getAppVersion() {
    try {
      let adata = await App.getInfo();
      if (adata && adata.version) {
        return adata;
      }
    } catch (e) {}
    return {
      version: this.config.appVersion,
      id: this.config.appCode ? this.config.appCode : 'DEFAULT',
    };
  }
  private setClientEnv(host: string) {
    this._env = host.split('.')[0];
    if (this._env.endsWith('prep')) {
      this._env = 'prep';
    } else if (this._env.endsWith('qa')) {
      this._env = 'qa';
    } else if (this._env.endsWith('dev')) {
      this._env = 'dev';
    } else {
      this._env = '';
    }
  }
  private cleanClient() {
    if (this._client === 'mica') {
      this._client = 'rmic';
    }
  }
  private setClient(host: string) {
    this._client = host.split('.')[0].replace(this._env, '');
    this.cleanClient();
    if (this._client === 'rmic') {
      this._realm = 'Mica';
    } else if (this._client === 'ffz1') {
      this._realm = 'DemoZ1';
    } else if (this._client === 'ffz1dev') {
      this._realm = 'DemoZ1';
    } else if (this._client === 'ffz1qa') {
      this._realm = 'DemoZ1';
    }
  }
  private setClientForDevice(appId: string) {
    let idSplit = appId.split('.');
    if (this.env.client) {
      this._client = this.env.client;
    } else {
      this._client = idSplit[idSplit.length - 1];
    }
    this.cleanClient();
  }
  private setHostname(hostnameParm: string) {
    console.log('This is on a server : ', this.config.hostName);
    this.setClientEnv(hostnameParm);
    this.setClient(hostnameParm);
    let hostname: string = hostnameParm || 'unknown';
    this.config.appBaseUrl = this.config.appBaseUrl?.replace(
      '{hostname}',
      hostname
    );
    this.config.apiBaseUrl = this.config.apiBaseUrl?.replace(
      '{hostname}',
      hostname
    );
    this.config.identityBaseUrl = this.config.apiBaseUrl?.replace(
      '{identityBaseUrl}',
      hostname
    );
    if (this.config.authConfig) {
      this.config.authConfig.url = this.config.authConfig.url?.replace(
        '{env}',
        this._env
      );
      this.config.authConfig.issuer = this.config.authConfig.issuer
        ?.replace('{env}', this._env)
        .replace('{client}', this._realm);
      this.config.authConfig.realm = this.config.authConfig.realm?.replace(
        '{client}',
        this._realm
      );
    }
  }
  private doWebWork(): boolean {
    if (this.config.hostName) {
      this.setHostname(this.config.hostName);
      return this._env.length == 0;
    }
    return false;
  }
  private async setAuthConfig() {
    if (this.config.authConfig) {
      const initOptions = this.config.authConfig.initOptions;
      if (initOptions) {
        let data = await Preferences.get({ key: 'login' });
        if (data.value) {
          const tokenDetails = JSON.parse(data.value);
          initOptions.token = tokenDetails.token;
          initOptions.refreshToken = tokenDetails.refreshToken;
        }
        console.log('Keycloak init : ', this.config.authConfig);
        this.keycloak.init({
          config: {
            url: this.config.authConfig.url,
            realm: this.config.authConfig.realm,
            clientId: this.config.authConfig.clientId,
          },
          initOptions,
        });
      }
    }
  }

  private buildConfigToSend(
    storredConfig: GetResult,
    deviceInfo: DeviceInfo,
    info: AppInfo | { version: string; id: string },
    identifier: string,
    lang: string
  ) {
    let configLoad = storredConfig.value
      ? JSON.parse(storredConfig.value)
      : { version: 0 };
    if (deviceInfo.platform && deviceInfo.platform === 'web') {
      this.config.hostName = window.location.href.split('/')[2];
      //localhost must run a client specific config
      if (!this.config.hostName.startsWith('localhost')) {
        this._isProd = this.doWebWork();
      } else {
        this._isProd = false;
        if (this.env.client) {
          this._env = this.env.env;
          this.setClient(this.env.client);
        } else {
          //default if no client specified in the config and this is localhost.
          this.setHostname('rmicdev.loyaltyplus.aero');
        }
      }
    } else {
      //this needs to change for the app.
      this.config.hostName = window.location.href.split('/')[2];
      this._env = this.env.env;
      this.setClientForDevice(info.id);
      this._isProd = this.env.production;
    }
    //let configLoad: any = {"version":0};
    if (!this._isProd) {
      //this is not a live app connect to dev.
      this._configUrl = `https://connect-dev.loyaltyplus.aero/public/loyaltyapi/1.0.0/mobile/config`;
    }
    configLoad.deviceDetails = {
      appVersion: info.version,
      appId: this._client,
      confVersion: configLoad.version,
      operatingSystem: deviceInfo.operatingSystem,
      osVersion: deviceInfo.osVersion,
      platform: deviceInfo.platform,
      webViewVersion: deviceInfo.webViewVersion,
      deviceId: identifier,
      languageCode: lang,
      env: this._env,
    };
    return configLoad;
  }
  private async loadRemoteConfig() {
    let [storredConfig, info, deviceInfo, lang, deviceId] = await Promise.all([
      Preferences.get({ key: 'config' }),
      this.getAppVersion(),
      Device.getInfo(),
      Device.getLanguageCode(),
      Device.getId(),
    ]);
    this._isProd = info.version.endsWith('0');
    let configLoad = this.buildConfigToSend(
      storredConfig,
      deviceInfo,
      info,
      deviceId.identifier,
      lang.value
    );

    // Check if we're running on localhost - if so, skip remote config entirely
    const isLocalhost = deviceInfo.platform === 'web' && 
                       window.location.hostname === 'localhost';
    
    if (isLocalhost) {
      console.log('🏠 Running on localhost - using local configuration only');
      // Reset configLoad to only contain device details, no cached config
      configLoad = {
        version: 1,
        deviceDetails: configLoad.deviceDetails
      };
    } else {
      try {
        const response: HttpResponse<any> = await firstValueFrom(
          this.httpClient.post(this._configUrl, configLoad.deviceDetails, {
            observe: 'response',
          })
        );
        if (response.status === 200) {
          response.body.deviceDetails = configLoad.deviceDetails;
          response.body.deviceDetails.confVersion = response.body.version;
          configLoad = response.body;
          Preferences.set({
            key: 'config',
            value: JSON.stringify(response.body),
          }).then();
        }
      } catch (error) {
        console.warn('Failed to load remote config, using local environment config:', error);
        // Use local config when remote config fails
        configLoad.version = 1; // Set version to non-zero to proceed
      }
    }
    if (configLoad.version !== 0) {
      //override the config settins we need.
    } else {
      //there is a massive problem and we do not have config in the system. Stop user from using.
    }
    try {
      if (configLoad['pages']) {
        delete this.config.pages;
      }
      if (configLoad['navigation']) {
        delete this.config.navigation;
      }
      this.config = deepmerge(this.config, configLoad);
    } catch (er1) {
      console.error('Error using default deepmerge!', er1);
      try {
        this.config = deepMerge(this.config, configLoad) as LssConfig;
      } catch (er2) {
        console.error('Error using lp deepmerge!', er2);
      }
    }
  }

  private async loadConfigInternal(): Promise<LssConfig> {
    await this.loadRemoteConfig();
    console.log('Load config Done : ' + this.config.apiBaseUrl);
    await this.setAuthConfig();

    return this.config;
  }
}
