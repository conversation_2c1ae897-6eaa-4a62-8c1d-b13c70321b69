import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, from } from 'rxjs';
import { Http } from '@capacitor-community/http';
import { Capacitor } from '@capacitor/core';

@Injectable({
  providedIn: 'root'
})
export class CapacitorHttpService {
  
  constructor(private httpClient: HttpClient) {}

  /**
   * Performs HTTP request using native HTTP on mobile platforms to bypass CORS
   */
  request(method: string, url: string, options?: any): Observable<any> {
    // Use native HTTP only on iOS to bypass CORS
    if (Capacitor.getPlatform() === 'ios') {
      return from(this.nativeRequest(method, url, options));
    }
    
    // Use Angular HttpClient for web and Android
    switch (method.toLowerCase()) {
      case 'get':
        return this.httpClient.get(url, options);
      case 'post':
        return this.httpClient.post(url, options?.body, options);
      case 'put':
        return this.httpClient.put(url, options?.body, options);
      case 'delete':
        return this.httpClient.delete(url, options);
      default:
        return this.httpClient.request(method, url, options);
    }
  }

  private async nativeRequest(method: string, url: string, options?: any): Promise<any> {
    const headers = this.convertHeaders(options?.headers);
    const params = this.convertParams(options?.params);
    
    try {
      const response = await Http.request({
        method: method.toUpperCase(),
        url: url,
        headers: headers,
        params: params,
        data: options?.body,
        responseType: 'json'
      });
      
      // Convert native response to match Angular HttpClient response format
      return {
        ...response.data,
        status: response.status,
        headers: response.headers
      };
    } catch (error: any) {
      // Convert native error to match Angular HttpClient error format
      throw {
        status: error.status || 0,
        statusText: error.error || 'Unknown Error',
        error: error.data || error.error,
        message: error.message || `Http failure response for ${url}: ${error.status || 0} ${error.error || 'Unknown Error'}`,
        url: url
      };
    }
  }

  private convertHeaders(headers?: HttpHeaders): any {
    if (!headers) return {};
    
    const result: any = {};
    headers.keys().forEach(key => {
      const values = headers.getAll(key);
      if (values && values.length > 0) {
        result[key] = values.join(', ');
      }
    });
    return result;
  }

  private convertParams(params?: HttpParams): any {
    if (!params) return {};
    
    const result: any = {};
    params.keys().forEach(key => {
      const values = params.getAll(key);
      if (values && values.length > 0) {
        result[key] = values.length === 1 ? values[0] : values;
      }
    });
    return result;
  }
}