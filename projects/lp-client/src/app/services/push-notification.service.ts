import { Injectable, NgZone } from '@angular/core';
import { Platform } from '@ionic/angular';
import { Router } from '@angular/router';
import { PushNotifications } from '@capacitor/push-notifications';
import { Preferences } from '@capacitor/preferences';
import { MemberService, FirebaseMemberService, FirebaseTokenResponse, DeviceTokenResponse } from 'lp-client-api';
import { BehaviorSubject, Observable } from 'rxjs';
import { environment } from '../../environments/environment';

// For web
import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';

// Import notification models and services from the mobile-components library
// Define interfaces locally since mobile-components import is not available
export type SafeHtml = string; // Placeholder until proper SafeHtml type is integrated

export interface NotificationToastPayload {
  id: string;
  title?: string;
  body?: string | string[];
  imageUrl?: string;
  richHtml?: SafeHtml;
  mediaUrl?: string;
  actions?: { label: string; deepLink: string }[];
  durationMs?: number;
  data?: any;
}

interface NotificationPreferences {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  enabled?: boolean;
  groups?: any;
  sounds?: boolean;
  vibration?: boolean;
  quietHours?: any;
}

interface NotificationGroup {
  id: string;
  name: string;
  description: string;
  defaultEnabled: boolean;
  icon: string;
}

@Injectable({
  providedIn: 'root'
})
export class PushNotificationService {
  private deviceToken: string | null = null;
  private notificationSubject = new BehaviorSubject<NotificationToastPayload | null>(null);
  public notifications$ = this.notificationSubject.asObservable();
  private listenersInitialized = false;

  /**
   * Show a notification toast manually with a provided payload
   * @param payload Notification payload conforming to NotificationToastPayload structure
   */
  public show(payload: NotificationToastPayload) {
    this.notificationSubject.next(payload);
  }

  /**
   * Check if the notification body is an array
   * @param body The body to check
   * @returns true if body is an array, false otherwise
   */
  public static isArray(body: string | string[] | undefined): body is string[] {
    return Array.isArray(body);
  }

  /**
   * Render notification body consistently as a string
   * Handles both string and string[] types
   * @param body The body to render
   * @returns A properly formatted string
   */
  public static renderBody(body: string | string[] | undefined): string {
    if (!body) return '';
    if (Array.isArray(body)) {
      return body.join('\n');
    }
    return body;
  }

  // Default notification groups
  private defaultGroups: NotificationGroup[] = [
    {
      id: 'general',
      name: 'General Notifications',
      description: 'Important updates and announcements',
      defaultEnabled: true,
      icon: 'notifications-outline'
    },
    {
      id: 'promotions',
      name: 'Promotions & Offers',
      description: 'Special offers, discounts and promotions',
      defaultEnabled: true,
      icon: 'gift-outline'
    },
    {
      id: 'transactions',
      name: 'Transactions',
      description: 'Updates about your points and transactions',
      defaultEnabled: true,
      icon: 'card-outline'
    },
    {
      id: 'games',
      name: 'Games',
      description: 'Game-related notifications and rewards',
      defaultEnabled: true,
      icon: 'game-controller-outline'
    }
  ];

  // Add a flag to track authentication status
  private isAuthenticated = false;

  // Firebase instances
  private firebaseApp: any = null;
  private firebaseMessaging: any = null;

  // Add a public getter for defaultGroups
  public get notificationGroups(): NotificationGroup[] {
    return this.defaultGroups;
  }

  /**
   * Get the current FCM registration token
   * @returns The FCM token or null if not available
   */
  public async getCurrentFCMToken(): Promise<string | null> {
    try {
      // First check if we have a stored token
      let token = localStorage.getItem('fcm_registration_token');
      
      if (token) {
        console.log('📋 Retrieved stored FCM token:', token);
        return token;
      }

      // If no stored token, try to get a new one
      if (this.firebaseMessaging && this.firebaseConfig.vapidKey) {
        console.log('🔄 Getting fresh FCM token...');
        token = await this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey);
        return token;
      }

      // If Firebase not initialized, check if we can initialize it
      if (!this.platform.is('capacitor') && Notification.permission === 'granted') {
        console.log('🚀 Initializing Firebase to get FCM token...');
        await this.safeInitializeFirebase();
        if (this.firebaseMessaging && this.firebaseConfig.vapidKey) {
          token = await this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey);
          return token;
        }
      }

      console.log('❌ No FCM token available');
      return null;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  }

  constructor(
    private platform: Platform,
    private memberService: MemberService,
    private firebaseMemberService: FirebaseMemberService,
    private zone: NgZone,
    private router: Router
  ) {
    console.log('🔥 APP: PushNotificationService constructor');
    console.log('🔥 APP: Platform is capacitor:', this.platform.is('capacitor'));
    console.log('🔥 APP: ServiceWorker available:', 'serviceWorker' in navigator);
    
    // No automatic subscription to auth events to avoid interfering with login flow
    
    // Listen for service worker messages (for Firebase Console notifications)
    this.setupServiceWorkerMessageListener();
  }

  // Add method to be called after authentication
  public setAuthenticated(status: boolean): void {
    this.isAuthenticated = status;

    // If we're authenticated and on web platform, initialize Firebase
    if (this.isAuthenticated && !this.platform.is('capacitor')) {
      this.initializeWeb();
    }
  }

  /**
   * Initialize push notifications
   * This is safe to call at app startup
   */
  async initialize(): Promise<void> {
    console.log('Initializing push notifications service');

    // Check if permissions were previously granted
    await this.checkAndRestorePermissions();

    // For native platforms, initialize immediately
    if (this.platform.is('capacitor')) {
      await this.initializeNative();
    } else {
      // For web, we'll initialize later with initializeAfterLogin
      console.log('Web platform detected, push notifications will be initialized after login is complete');
    }
  }

  /**
   * Check if notification permissions were previously granted and restore state
   */
  private async checkAndRestorePermissions(): Promise<void> {
    try {
      console.log('🔍 Checking for previous notification permissions...');
      
      // Check browser permission
      const browserPermission = Notification.permission === 'granted';
      
      // Check our stored preferences
      const storedPrefs = await this.getPreferences();
      
      // Check localStorage backup
      const permissionGranted = localStorage.getItem('notification_permission_granted') === 'true';
      
      console.log('Browser permission:', browserPermission);
      console.log('Stored preferences enabled:', storedPrefs.enabled);
      console.log('localStorage backup:', permissionGranted);
      
      // If browser permission is granted but our preferences say disabled, fix it
      if (browserPermission && (!storedPrefs.enabled || permissionGranted)) {
        console.log('🔧 Fixing notification preferences - browser permission granted but preferences disabled');
        storedPrefs.enabled = true;
        await this.savePreferences(storedPrefs);
        console.log('✅ Notification preferences restored');
      }
      
      // If permissions are enabled, try to initialize Firebase early
      if (browserPermission && storedPrefs.enabled && this.isAuthenticated) {
        console.log('🚀 Auto-initializing Firebase with existing permissions');
        setTimeout(() => {
          this.initializeWeb().catch(err => 
            console.log('Auto-initialization failed (non-critical):', err)
          );
        }, 2000);
      }
    } catch (error) {
      console.error('Error checking/restoring permissions:', error);
    }
  }

  /**
   * Set up service worker message listener to handle Firebase Console notifications
   * This ensures that notifications sent from Firebase Console appear in the app
   */
  private setupServiceWorkerMessageListener(): void {
    console.log('🔥 APP: Setting up service worker message listener');
    if (!this.platform.is('capacitor') && 'serviceWorker' in navigator) {
      console.log('🔥 APP: Adding service worker message listener');
      navigator.serviceWorker.addEventListener('message', (event) => {
        console.log('🔥 APP: Service worker message received:', event.data);
        
        if (event.data.type === 'FIREBASE_NOTIFICATION') {
          console.log('🔥 APP: Processing Firebase Console notification in app');
          
          // Process the notification through the normal handling flow
          this.zone.run(() => {
            this.handleWebNotification(event.data.payload);
          });
        }
      });
      console.log('🔥 APP: Service worker message listener added');
    } else {
      console.log('🔥 APP: Skipping SW listener - capacitor:', this.platform.is('capacitor'), 'SW available:', 'serviceWorker' in navigator);
    }
  }

  /**
   * Initialize push notifications after login is complete
   * This should be called explicitly after the login process is fully complete
   */
  async initializeAfterLogin(): Promise<void> {
    console.log('Initializing push notifications after login');

    if (!this.platform.is('capacitor')) {
      // Only for web platform
      console.log('Setting authenticated status for web push notifications');
      this.setAuthenticated(true);
    }
    
    // Retry token linking now that user is authenticated
    // Use a small delay to ensure member profile is fully loaded
    setTimeout(async () => {
      await this.retryTokenLinking();
    }, 1000);
  }

  /**
   * Request notification permission and initialize token if granted
   * This should be called explicitly by user action, not during authentication
   *
   * This implementation avoids service worker registration until after permission is granted
   */
  async requestPermission(): Promise<boolean> {
    console.log('Requesting push notification permission');
    let permissionGranted = false;

    if (this.platform.is('capacitor')) {
      const result = await PushNotifications.requestPermissions();
      permissionGranted = result.receive === 'granted';
      
      // CRITICAL: Register for push notifications after permission is granted
      if (permissionGranted) {
        console.log('🔥 Permission granted on iOS/Android! Registering for push notifications...');
        
        // Ensure listeners are set up before registering
        if (!this.listenersInitialized) {
          console.log('⚠️ Listeners not initialized, setting them up now...');
          await this.initializeNative();
        }
        
        try {
          await PushNotifications.register();
          console.log('✅ Push notification registration initiated');
        } catch (error) {
          console.error('❌ Error registering for push notifications:', error);
        }
      }
    } else {
      try {
        // First check if permission is already granted - if so, just get token
        if (Notification.permission === 'granted') {
          console.log('Notification permission already granted');
          permissionGranted = true;
          
          // Only initialize Firebase if we don't already have messaging
          if (!this.firebaseMessaging) {
            try {
              await this.safeInitializeFirebase();
            } catch (firebaseError) {
              console.error('Error initializing Firebase for granted permission:', firebaseError);
            }
          }

          // Get token if we have Firebase initialized
          if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
            setTimeout(() => {
              if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
                this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey)
                  .catch((err: any) => console.error('Error getting token with granted permission:', err));
              }
            }, 1000);
          }

          return true;
        }

        // If permission not granted, request it WITHOUT initializing Firebase first
        console.log('Requesting notification permission from browser');
        
        const permission = await Notification.requestPermission();
        permissionGranted = permission === 'granted';
        
        console.log('Permission request result:', permission);

        // Only if permission was granted, then initialize Firebase
        if (permissionGranted) {
          console.log('Permission granted, initializing Firebase...');
          
          try {
            await this.safeInitializeFirebase();
            
            // Get token after Firebase is initialized
            if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
              setTimeout(() => {
                if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
                  this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey)
                    .catch((err: any) => console.error('Error getting token after permission granted:', err));
                }
              }, 1000);
            }
          } catch (firebaseError) {
            console.error('Error initializing Firebase after permission granted:', firebaseError);
            // Still return true since permission was granted
          }
        }
      } catch (permError) {
          console.error('Error during permission request:', permError);
          
          // Multiple fallbacks to check permission state
          
          // Fallback 1: Check directly
          try {
            const permState = Notification.permission;
            permissionGranted = permState === 'granted' as NotificationPermission;
            console.log('Fallback permission check result:', permState);
          } catch (fallbackError) {
            console.error('Error in fallback permission check:', fallbackError);
            
            // Fallback 2: Try creating a notification test (will fail silently if not permitted)
            try {
              if ('Notification' in window) {
                // Create with try-catch to avoid errors
                try { new Notification('Test', { silent: true }); permissionGranted = true; } 
                catch (e) { permissionGranted = false; }
              }
            } catch (testError) {
              console.error('Error in notification test:', testError);
              permissionGranted = false;
            }
          }
        }

        // If permission granted, get the token
        try {
          if (permissionGranted) {
            console.log('Permission granted, initializing token');
            
            // Double-check Firebase initialization
            if (!this.firebaseMessaging) {
              try {
                console.log('Re-initializing Firebase after permission grant');
                await this.safeInitializeFirebase();
              } catch (reinitError) {
                console.error('Error re-initializing Firebase after permission grant:', reinitError);
                // Continue anyway, we'll try to get messaging
              }
            }
            
            // Final check if messaging is available
            if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
              // Get token using the existing Firebase messaging instance
              // Use a longer timeout to avoid immediate page refreshs
              setTimeout(async () => {
                if (this.firebaseMessaging && this.firebaseConfig?.vapidKey) {
                  await this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey);
                }
              }, 5000);
            } else {
              console.error('Firebase messaging not available after permission grant');
            }
          }
      } catch (error) {
        console.error('Error in notification permission process:', error);
        permissionGranted = false;
      } finally {
        // Clean up
        localStorage.removeItem('notification_permission_timestamp');
      }
    }

    // Track permission result
    try {
      console.log('📊 Would track permission change:', permissionGranted);
    } catch (analyticsError) {
      console.error('Error tracking permission change:', analyticsError);
    }

    // CRITICAL: Immediately save preferences if permission granted
    if (permissionGranted) {
      try {
        console.log('🔥 Permission granted! Saving preferences immediately...');
        const currentPrefs = await this.getPreferences();
        currentPrefs.enabled = true;
        await this.savePreferences(currentPrefs);
        console.log('✅ Preferences saved successfully after permission grant');
        
        // Also store in localStorage as backup
        localStorage.setItem('notification_permission_granted', 'true');
        localStorage.setItem('notification_permission_timestamp', Date.now().toString());
      } catch (saveError) {
        console.error('❌ Error saving preferences after permission grant:', saveError);
      }
    }

    return permissionGranted;
  }

  /**
   * Handle web notification received from Firebase
   * @param payload Firebase notification payload
   */
  private handleWebNotification(payload: any): void {
    console.log('Handling web notification:', payload);
    
    try {
// Convert Firebase/APNS payload to rich NotificationToastPayload format
      const notificationData: NotificationToastPayload = {
        id: payload.data?.id || 'notification-' + Date.now(),
        title: payload.notification?.title || payload.data?.title || 'New Notification',
        body: payload.notification?.body || payload.data?.body || '',
        imageUrl: payload.notification?.image || payload.data?.imageUrl || undefined,
        richHtml: payload.data?.richHtml,
        mediaUrl: payload.data?.mediaUrl,
        actions: payload.data?.actions,
        durationMs: payload.data?.durationMs,
        data: payload.data || {}
      };
      
      // Extract action URL if present
      if (payload.data?.actionUrl) {
        notificationData.data.actionUrl = payload.data.actionUrl;
      }
      
      // Add important fields for tracking
      if (payload.data?.id) {
        notificationData.data.id = payload.data.id;
      }
      
      if (payload.data?.group) {
        notificationData.data.group = payload.data.group;
      }

      // Track notification delivery within zone to ensure proper Angular context
      if (payload.data?.id) {
        this.zone.run(() => {
          console.log('📊 Would track notification delivery for ID:', payload.data?.id);
        });
      }

      // Emit the notification to be displayed within Angular zone
      // This ensures proper change detection and UI updates
      this.zone.run(() => {
        this.notificationSubject.next(notificationData);
      });
      
      // Play notification sound if available and app is in foreground
      this.playNotificationSound();
      
      // Create a browser notification if we're in background
      if (document.visibilityState !== 'visible') {
        this.createBrowserNotification(notificationData);
      }
    } catch (error) {
      console.error('Error handling web notification:', error);
    }
  }
  
  /**
   * Helper method to create a browser notification when app is in background
   * @param data notification data to display
   */
  private createBrowserNotification(data: NotificationToastPayload): void {
    // Check browser notification permission
    if (Notification.permission !== 'granted') {
      return;
    }
    
    try {
      const options: NotificationOptions = {
        body: PushNotificationService.renderBody(data.body),
        icon: '/assets/icons/icon-192x192.png', // Default icon
        badge: '/assets/icons/badge-96x96.png',  // Default badge
        // Note: image is not a standard property in NotificationOptions
        // so we use a valid property instead
        data: {
          ...data.data,
          imageUrl: data.imageUrl // Store image URL in data
        },
        requireInteraction: true,
        silent: false
      };
      
      const notification = new Notification(data.title || 'Notification', options);
      
      // Handle click on the notification
      notification.onclick = () => {
        console.log('Browser notification clicked:', data);
        
        // Focus window
        window.focus();
        
        // Handle action URL if present
        if (data.data?.actionUrl) {
          this.zone.run(() => {
            this.router.navigateByUrl(data.data.actionUrl);
          });
        }
        
        // Track notification open
        if (data.data?.id) {
          this.zone.run(() => {
            console.log('📊 Would track notification open for ID:', data.data?.id);
          });
        }
        
        // Close the notification
        notification.close();
      };
    } catch (error) {
      console.error('Error creating browser notification:', error);
    }
  }
  
  /**
   * Play a notification sound if available
   */
  private playNotificationSound(): void {
    try {
      const audio = new Audio('/assets/sounds/notification.mp3');
      audio.volume = 0.5;
      audio.play().catch(error => {
        // Browsers often block autoplay, so we just log this
        console.log('Could not play notification sound:', error);
      });
    } catch (error) {
      console.log('Error playing notification sound:', error);
    }
  }

  /**
   * Handle notifications received on native platforms
   * @param notification Capacitor notification
   */
  private handleNotificationReceived(notification: any): void {
    console.log('Handling native notification:', notification);
    
    try {
// Convert native/capacitor notification to NotificationToastPayload
      const notificationData: NotificationToastPayload = {
        id: notification.data?.id || 'notification-' + Date.now(),
        title: notification.title || notification.data?.title || '',
        body: notification.body || notification.data?.body || '',
        imageUrl: notification.data?.imageUrl,
        richHtml: notification.data?.richHtml,
        mediaUrl: notification.data?.mediaUrl,
        actions: notification.data?.actions,
        durationMs: notification.data?.durationMs,
        data: notification.data || {}
      };

      // Track notification delivery
      if (notification.data?.id) {
        console.log('📊 Would track notification delivery for ID:', notification.data.id);
      }

      // Emit the notification to be displayed
      this.notificationSubject.next(notificationData);
    } catch (error) {
      console.error('Error handling native notification:', error);
    }
  }

  /**
   * Handle notification actions performed on native platforms
   * @param action Capacitor notification action
   */
  private handleNotificationAction(action: any): void {
    console.log('Handling notification action:', action);
    
    try {
      const notification = action.notification;
      
      // Track notification open
      if (notification.data?.id) {
        console.log('📊 Would track notification open for ID:', notification.data.id);
      }

      // Handle action - typically navigation
      if (notification.data?.actionUrl) {
        this.zone.run(() => {
          this.router.navigateByUrl(notification.data.actionUrl);
        });
      }
    } catch (error) {
      console.error('Error handling notification action:', error);
    }
  }

  /**
   * Safely initialize Firebase without causing page refreshes
   * This method ensures Firebase is only initialized once and handles
   * the service worker registration in a way that doesn't disrupt authentication
   */
  private async safeInitializeFirebase(): Promise<void> {
    // If Firebase is already initialized, don't do it again
    if (this.firebaseApp && this.firebaseMessaging) {
      console.log('Firebase already initialized, reusing existing instance');
      return;
    }

    // Store original methods but don't override them aggressively
    console.log('🔧 Starting safe Firebase initialization...');

    try {
      console.log('Safely initializing Firebase');
      
      // Store auth state before we proceed with potentially disruptive operations
      const authStateBeforeInit = this.storeCurrentAuthTokens();

      // Conservative service worker registration - only if not already registered
      if ('serviceWorker' in navigator) {
        try {
          // Check if firebase-messaging-sw.js is already registered
          const registrations = await navigator.serviceWorker.getRegistrations();
          const isFirebaseSwRegistered = registrations.some(
            reg => reg.scope.includes(location.origin) && 
                  (reg.active?.scriptURL.includes('firebase-messaging-sw.js'))
          );

          if (!isFirebaseSwRegistered) {
            console.log('Firebase service worker not found, registering without page refresh...');
            
            // Add event listener to prevent automatic refresh on controllerchange
            const preventRefresh = () => {
              console.log('Service worker controller change detected - not refreshing');
            };
            
            navigator.serviceWorker.addEventListener('controllerchange', preventRefresh, { once: true });
            
            // Register the service worker
            const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
            console.log('Firebase service worker registered:', registration.scope);
            
            // Wait for it to be ready without triggering refresh
            await navigator.serviceWorker.ready;
            console.log('Firebase service worker is ready');
            
            // Remove the event listener
            navigator.serviceWorker.removeEventListener('controllerchange', preventRefresh);
            
          } else {
            console.log('Firebase service worker already registered');
          }
        } catch (swError) {
          console.error('Error with service worker registration:', swError);
          // Continue without service worker if it fails
        }
      }

      // Initialize Firebase with controlled service worker registration
      try {
        console.log('Creating Firebase app instance');
        this.firebaseApp = initializeApp(this.firebaseConfig);
      } catch (firebaseInitError: any) {
        // Handle case where app is already initialized
        if (firebaseInitError.code === 'app/duplicate-app') {
          console.log('Firebase app already exists, retrieving existing instance');
          const { getApp } = await import('firebase/app');
          try {
            this.firebaseApp = getApp();
          } catch (getAppError) {
            console.error('Error retrieving existing Firebase app:', getAppError);
            // Fallback: Create a new app with a unique name
            const { initializeApp: initApp } = await import('firebase/app');
            this.firebaseApp = initApp(this.firebaseConfig, 'dynamicApp' + Date.now());
          }
        } else {
          console.error('Firebase initialization error:', firebaseInitError);
          throw firebaseInitError;
        }
      }
      
      // Use a longer timeout before getting messaging to allow service worker to stabilize
      await new Promise(resolve => setTimeout(resolve, 2500));
      
      try {
        console.log('Getting Firebase messaging instance');
        this.firebaseMessaging = getMessaging(this.firebaseApp);
        
        // Listen for foreground messages
        onMessage(this.firebaseMessaging, (payload) => {
          console.log('🔥 APP: Message received in FOREGROUND (onMessage):', payload);
          console.log('🔥 APP: This is likely a Firebase Console notification!');
          this.handleWebNotification(payload);
        });
      } catch (messagingError) {
        console.error('Error getting Firebase messaging:', messagingError);
        throw messagingError;
      }

      console.log('Firebase initialized successfully');
      
      // Verify auth integrity after initialization is complete
      this.verifyAuthenticationIntegrity(authStateBeforeInit);
    } catch (error) {
      console.error('Error initializing Firebase:', error);

      // If there was an error, clear the instances to allow retry
      this.firebaseApp = null;
      this.firebaseMessaging = null;
      throw error; // Re-throw the error for better error handling upstream
    } finally {
      console.log('✅ Firebase initialization process completed');
    }
  }

  async registerToken(token: string): Promise<void> {
    console.log('Registering device token:', token);
    this.deviceToken = token;

    // Determine platform
    let platform: 'ios' | 'android' | 'web' = 'web';
    if (this.platform.is('ios')) {
      platform = 'ios';
    } else if (this.platform.is('android')) {
      platform = 'android';
    }

    // Register with backend (device token registration)
    this.firebaseMemberService.registerDeviceToken(token, platform).subscribe({
      next: (response: DeviceTokenResponse) => {
        if (response.success) {
          console.log('📱 Device token registered successfully:', response);
        } else {
          console.warn('⚠️ Device token registration failed:', response.error);
        }
      },
      error: (error: any) => console.error('❌ Error registering device token:', error)
    });


    console.log('-----------')
    console.log(this.memberService)
    console.log('-----------')
   

    // Link Firebase token with member account only if member is available
    const memberProfile = this.memberService.profileSubject.value;
    const memberId = memberProfile?.newMembershipNumber;

    
    if (memberId && memberProfile) {
      this.firebaseMemberService.linkFirebaseToken(token, memberId).subscribe({
        next: (response: FirebaseTokenResponse) => {
          if (response.success) {
            console.log('🔥 Firebase token linked successfully:', response);
          } else {
            console.warn('⚠️ Firebase token linking failed:', response.error);
          }
        },
        error: (error: any) => console.error('❌ Error linking Firebase token:', error)
      });
    } else {
      console.warn('⚠️ Skipping Firebase token linking - member not authenticated or profile not loaded');
      console.log('📊 Member ID:', memberId, 'Profile:', memberProfile ? 'Available' : 'Not Available');
    }
  }

  private async initializeNative(): Promise<void> {
    console.log('Initializing push notifications for native platform');
    try {
      // IMPORTANT: Set up listeners BEFORE any registration attempts
      console.log('🎯 Setting up push notification listeners...');
      
      PushNotifications.addListener('registration', (token: any) => {
        console.log('🔥 Push registration success, token: ' + token.value);
        console.log('🔥 Token length:', token.value.length);
        this.registerToken(token.value);
      });

      PushNotifications.addListener('registrationError', (error: any) => {
        console.error('❌ Error on registration: ' + JSON.stringify(error));
      });

      PushNotifications.addListener('pushNotificationReceived', (notification: any) => {
        console.log('📱 Push notification received: ' + JSON.stringify(notification));
        this.handleNotificationReceived(notification);
      });

      PushNotifications.addListener('pushNotificationActionPerformed', (action: any) => {
        console.log('👆 Push notification action performed: ' + JSON.stringify(action));
        this.handleNotificationAction(action);
      });
      
      console.log('✅ Push notification listeners set up successfully');
      this.listenersInitialized = true;
      
      // Check if already registered (in case permission was previously granted)
      const permissionStatus = await PushNotifications.checkPermissions();
      if (permissionStatus.receive === 'granted') {
        console.log('📱 Permission already granted, checking registration status...');
        // Note: We don't call register() here as it will be called when user toggles the setting
      }
    } catch (error) {
      console.error('Error initializing push notifications for native platform', error);
    }
  }

  // Store Firebase config from environment with fallback
  private firebaseConfig = {
    apiKey: (environment as any).firebase?.apiKey || "AIzaSyAseqL6KTURHxDYvK7Wygl0ViRIDS1OM3c",
    authDomain: (environment as any).firebase?.authDomain || "loyalty-test-project-58bcb.firebaseapp.com",
    projectId: (environment as any).firebase?.projectId || "loyalty-test-project-58bcb",
    storageBucket: (environment as any).firebase?.storageBucket || "loyalty-test-project-58bcb.firebasestorage.app",
    messagingSenderId: (environment as any).firebase?.messagingSenderId || "798238467180",
    appId: (environment as any).firebase?.appId || "1:798238467180:web:ddab11e43f82af1de86449",
    measurementId: (environment as any).firebase?.measurementId || "G-8CCCGX5VTK",
    vapidKey: (environment as any).firebase?.vapidKey || "BE9BCB1mD6jOEL5wGM46BK4eVsVOTtuq4YmKEACpDqXJymeXQG7O2d8QBpN0NE9FBzsrZ3FnySi7unGKbqdQ1yQ",
    serverKey: 'YOUR_FIREBASE_SERVER_KEY_HERE' // Get this from Firebase Console > Project Settings > Cloud Messaging
  };

  // Helper methods for token management and Firebase operations
  private async getFirebaseToken(messaging: any, vapidKey: string): Promise<string | null> {
    try {
      console.log('🎯 Getting Firebase token...');
      const token = await getToken(messaging, { vapidKey });
      
      if (token) {
        console.log('✅ Firebase token obtained successfully:', token.substring(0, 20) + '...');
        
        // Store token for later use
        localStorage.setItem('fcm_registration_token', token);
        
        // Register with backend
        await this.registerToken(token);
        
        return token;
      } else {
        console.log('❌ No Firebase token available');
        return null;
      }
    } catch (error: any) {
      console.error('Error getting Firebase token:', error);
      
      if (error.code === 'messaging/permission-blocked') {
        console.log('Push messaging is blocked by the user.');
      } else if (error.code === 'messaging/notification-permission-required') {
        console.log('Permission has not been granted to show notifications.');
      }
      
      return null;
    }
  }

  private storeCurrentAuthTokens(): any {
    // This would store current auth tokens - implement based on your auth system
    return {
      timestamp: Date.now(),
      url: window.location.href
    };
  }

  private verifyAuthenticationIntegrity(beforeState: any): void {
    console.log('Verifying authentication integrity after Firebase init');
    // Implementation would depend on your auth system
  }

  // Initialize for web platform only
  private async initializeWeb(): Promise<void> {
    console.log('Initializing push notifications for web platform');
    
    // Check if notifications are already enabled
    if (Notification.permission === 'granted') {
      try {
        await this.safeInitializeFirebase();
        
        // Try to get existing token
        if (this.firebaseMessaging && this.firebaseConfig.vapidKey) {
          await this.getFirebaseToken(this.firebaseMessaging, this.firebaseConfig.vapidKey);
        }
      } catch (error) {
        console.error('Error initializing web notifications:', error);
      }
    } else {
      console.log('Notifications not yet permitted for web platform');
    }
  }

  /**
   * Retry linking Firebase token after member authentication
   */
  async retryTokenLinking(): Promise<void> {
    console.log('🔄 Retrying Firebase token linking after authentication');
    
    const memberProfile = this.memberService.profileSubject.value;
    const memberId = memberProfile?.newMembershipNumber || memberProfile?.mpacc || memberProfile?.uniqueId;
    
    if (!memberId || !memberProfile) {
      console.warn('⚠️ Cannot retry token linking - member still not authenticated');
      return;
    }
    
    // Get current FCM token
    const currentToken = await this.getCurrentFCMToken();
    if (currentToken) {
      this.firebaseMemberService.linkFirebaseToken(currentToken, memberId).subscribe({
        next: (response: FirebaseTokenResponse) => {
          if (response.success) {
            console.log('🔥 Firebase token linked successfully on retry:', response);
          } else {
            console.warn('⚠️ Firebase token linking failed on retry:', response.error);
          }
        },
        error: (error: any) => console.error('❌ Error linking Firebase token on retry:', error)
      });
    } else {
      console.warn('⚠️ No FCM token available for retry linking');
    }
  }

  /**
   * Get user notification preferences
   */
  async getPreferences(): Promise<NotificationPreferences> {
    try {
      const stored = await Preferences.get({ key: 'notification_preferences' });
      if (stored.value) {
        const preferences = JSON.parse(stored.value) as NotificationPreferences;
        
        // Ensure all required properties exist with defaults
        return {
          emailNotifications: preferences.emailNotifications ?? false,
          pushNotifications: preferences.pushNotifications ?? false,
          smsNotifications: preferences.smsNotifications ?? false,
          enabled: preferences.enabled ?? false,
          groups: preferences.groups ?? this.defaultGroups.reduce((acc, group) => {
            acc[group.id] = group.defaultEnabled;
            return acc;
          }, {} as Record<string, boolean>),
          sounds: preferences.sounds ?? true,
          vibration: preferences.vibration ?? true,
          quietHours: preferences.quietHours ?? {
            enabled: false,
            start: '22:00',
            end: '07:00'
          }
        };
      }
    } catch (error) {
      console.error('Error loading notification preferences:', error);
    }

    // Return default preferences
    return {
      emailNotifications: false,
      pushNotifications: false,
      smsNotifications: false,
      enabled: false,
      groups: this.defaultGroups.reduce((acc, group) => {
        acc[group.id] = group.defaultEnabled;
        return acc;
      }, {} as Record<string, boolean>),
      sounds: true,
      vibration: true,
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '07:00'
      }
    };
  }

  /**
   * Save user notification preferences
   */
  async savePreferences(preferences: NotificationPreferences): Promise<void> {
    try {
      await Preferences.set({
        key: 'notification_preferences',
        value: JSON.stringify(preferences)
      });
      console.log('Notification preferences saved successfully');
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      throw error;
    }
  }

  /**
   * Subscribe to Firebase topic via backend API
   */
  async subscribeToTopicViaBackend(token: string, topicId: string): Promise<boolean> {
    try {
      // Implementation would call your backend API
      console.log(`Subscribing ${token.substring(0, 10)}... to topic: ${topicId}`);
      
      // For now, return true - implement actual API call
      return true;
    } catch (error) {
      console.error(`Error subscribing to topic ${topicId}:`, error);
      return false;
    }
  }

  /**
   * Unsubscribe from Firebase topic via backend API
   */
  async unsubscribeFromTopicViaBackend(token: string, topicId: string): Promise<boolean> {
    try {
      // Implementation would call your backend API
      console.log(`Unsubscribing ${token.substring(0, 10)}... from topic: ${topicId}`);
      
      // For now, return true - implement actual API call
      return true;
    } catch (error) {
      console.error(`Error unsubscribing from topic ${topicId}:`, error);
      return false;
    }
  }
}