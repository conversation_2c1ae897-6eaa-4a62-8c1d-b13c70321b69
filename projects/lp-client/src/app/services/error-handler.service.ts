import { Injectable } from '@angular/core';
import { AlertController } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlerService {
  constructor(private alertController: AlertController) {}
  
  /**
   * Handle API errors with user-friendly messages
   */
  async handleApiError(error: any): Promise<void> {
    console.error('API Error:', error);
    
    let errorMessage = 'An unexpected error occurred. Please try again.';
    
    // Check for specific error types
    if (error && error.error && error.error.detail) {
      errorMessage = error.error.detail;
    }
    
    // Check for deserialization errors
    if (errorMessage.includes('Unable to deserialize') || 
        errorMessage.includes('JsonDateAdaptor')) {
      errorMessage = 'There was a problem processing your information. Please try again or contact support.';
    }
    
    const alert = await this.alertController.create({
      header: 'Error',
      message: errorMessage,
      buttons: ['OK']
    });
    
    await alert.present();
  }
}