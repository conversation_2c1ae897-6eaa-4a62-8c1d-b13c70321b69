import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ApiTestingService {
  constructor(private http: HttpClient) {}
  
  /**
   * Test API endpoints for data type compatibility
   * Can be run in development environments
   */
  async testApiEndpoints(): Promise<void> {
    if (environment.production) return;
    
    console.log('Testing API endpoints for data compatibility...');
    
    // Test specific endpoints that use date fields
    this.testEndpoint('/api/signup', { testData: true });
    // Add other endpoints as needed
  }
  
  private async testEndpoint(endpoint: string, testData: any): Promise<void> {
    try {
      const response = await this.http.get(`${environment.lssConfig.apiBaseUrl}${endpoint}`).toPromise();
      console.log(`Endpoint ${endpoint} test:`, response);
      
      // Check for date fields
      this.validateDateFields(response);
    } catch (error) {
      console.error(`Endpoint ${endpoint} test failed:`, error);
    }
  }
  
  private validateDateFields(data: any): void {
    // Recursively check for date fields
    if (!data) return;
    
    if (typeof data === 'object') {
      Object.keys(data).forEach(key => {
        if (key.toLowerCase().includes('date') || 
            key.toLowerCase().includes('expiry')) {
          console.log(`Validating date field: ${key}`, data[key]);
          try {
            new Date(data[key]);
          } catch (error) {
            console.error(`Invalid date format in field ${key}:`, error);
          }
        }
        
        if (typeof data[key] === 'object') {
          this.validateDateFields(data[key]);
        }
      });
    }
  }
}