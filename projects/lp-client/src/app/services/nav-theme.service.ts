import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NavThemeService {
  private currentThemeSubject = new BehaviorSubject<string>('default');
  public currentTheme$ = this.currentThemeSubject.asObservable();

  constructor() {}

  /**
   * Set the current theme
   * @param theme Theme name
   */
  setTheme(theme: string): void {
    this.currentThemeSubject.next(theme);
  }

  /**
   * Get the current theme
   * @returns Current theme name
   */
  getCurrentTheme(): string {
    return this.currentThemeSubject.value;
  }

  /**
   * Get the CSS class for a theme
   * @param theme Theme name
   * @returns CSS class name
   */
  getThemeClass(theme: string): string {
    switch (theme) {
      case 'dark':
        return 'theme-dark';
      case 'light':
        return 'theme-light';
      case 'blue':
        return 'theme-blue';
      default:
        return 'theme-default';
    }
  }
}