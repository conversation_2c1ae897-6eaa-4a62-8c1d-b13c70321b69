# NotificationToast Component

The `NotificationToast` component is a sophisticated notification system that displays rich, interactive toast messages. It supports text, images, HTML content, audio, video, and multiple action buttons with smooth animations and theming support.

## Features

- ✨ **Rich Content**: Support for text, images, HTML, audio, and video
- 🎯 **Interactive Actions**: Multiple action buttons with custom deep links
- 🎨 **Theming**: Light/dark mode support with CSS custom properties
- ⚡ **Animations**: Smooth slide-in animations with reduced motion support
- 📱 **Responsive**: Adapts to different screen sizes and devices
- ♿ **Accessible**: ARIA support and keyboard navigation
- ⏱️ **Auto-dismiss**: Configurable duration with pause on hover
- 🎛️ **Customizable**: Extensive styling and configuration options

## Installation & Setup

### 1. Import Required Modules

Ensure `BrowserAnimationsModule` is imported in your app module:

```typescript
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NotificationToastComponent } from './components/notification-toast/notification-toast.component';

@NgModule({
  imports: [
    BrowserAnimationsModule, // Required for animations
    NotificationToastComponent // Standalone component
  ],
  // ...
})
export class AppModule {}
```

### 2. Add Component to Template

Add the component to your app root template:

```html
<app-notification-toast></app-notification-toast>
```

## Usage

### Basic Notification

```typescript
import { PushNotificationService } from './services/push-notification.service';

constructor(private pushNotificationService: PushNotificationService) {}

showBasicNotification() {
  this.pushNotificationService.show({
    id: 'basic-1',
    title: 'New Message',
    body: 'You have received a new message.',
    durationMs: 5000
  });
}
```

### Image Notification

```typescript
showImageNotification() {
  this.pushNotificationService.show({
    id: 'image-1',
    title: 'Photo Uploaded',
    body: 'Your photo has been successfully uploaded.',
    imageUrl: 'https://example.com/image.jpg',
    durationMs: 7000
  });
}
```

### Multiple Actions

```typescript
showActionNotification() {
  this.pushNotificationService.show({
    id: 'action-1',
    title: 'Friend Request',
    body: 'John Doe wants to connect with you.',
    data: {
      actions: [
        { label: 'Accept', deepLink: '/friends/accept/123' },
        { label: 'Decline', deepLink: '/friends/decline/123' },
        { label: 'View Profile', deepLink: '/profile/123' }
      ]
    },
    durationMs: 10000
  });
}
```

### Rich HTML Content

```typescript
showRichNotification() {
  this.pushNotificationService.show({
    id: 'rich-1',
    title: 'Weekly Summary',
    richHtml: `
      <div style="padding: 10px;">
        <p><strong>Great week!</strong></p>
        <ul>
          <li>✅ 5 tasks completed</li>
          <li>📧 12 emails sent</li>
          <li>🎯 Goals achieved</li>
        </ul>
      </div>
    `,
    durationMs: 8000
  });
}
```

### Media Notifications

```typescript
// Audio notification
showAudioNotification() {
  this.pushNotificationService.show({
    id: 'audio-1',
    title: 'Voice Message',
    body: 'You have a new voice message.',
    mediaUrl: 'https://example.com/audio.mp3',
    durationMs: 15000
  });
}

// Video notification
showVideoNotification() {
  this.pushNotificationService.show({
    id: 'video-1',
    title: 'Video Call',
    body: 'Incoming video call from Sarah.',
    mediaUrl: 'https://example.com/video.mp4',
    durationMs: 20000
  });
}
```

## NotificationToastPayload Interface

```typescript
interface NotificationToastPayload {
  id: string;                    // Unique identifier
  title?: string;                // Notification title
  body?: string | string[];      // Main content (string or array for multi-line)
  imageUrl?: string;             // Image URL
  richHtml?: SafeHtml;          // Rich HTML content
  mediaUrl?: string;            // Audio/video URL
  actions?: Action[];           // Legacy action format
  durationMs?: number;          // Auto-dismiss duration (default: 7000ms)
  data?: {                      // Additional data
    actions?: Action[];         // Action buttons
    [key: string]: any;        // Custom data
  };
}

interface Action {
  label: string;                // Button text
  deepLink: string;            // Navigation URL or deep link
}
```

## Customization

### CSS Classes

The component uses these CSS classes for styling:

- `.notification-toast` - Main container
- `.close-button` - Close button (ion-icon)
- `.notification-title` - Title text
- `.notification-body` - Body content
  - `.notification-body.short` - Short text styling
  - `.notification-body.long` - Long text styling
- `.notification-image` - Image content
- `.rich-content` - Rich HTML container
- `.media-content` - Audio/video container
- `.actions` - Action buttons container
- `.action-button` - Individual action buttons
- `.progress` - Progress bar

### Custom Styling Example

```css
/* Custom notification styling */
.notification-toast {
  --toast-background: #ffffff;
  --toast-border-radius: 16px;
  --toast-shadow: 0 8px 32px rgba(0,0,0,0.12);
  --toast-max-width: 420px;
  --toast-padding: 20px;
}

/* Custom action button styling */
.action-button {
  --button-background: #007bff;
  --button-color: white;
  --button-border-radius: 8px;
  --button-padding: 8px 16px;
  --button-font-size: 14px;
  --button-font-weight: 500;
}

.action-button:hover {
  --button-background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}
```

## Theming

### CSS Custom Properties

The component uses CSS custom properties for theming:

```css
:root {
  /* Light theme */
  --ion-text-color: #1a1a1a;
  --ion-color-medium: #92949c;
  --ion-color-medium-shade: #6b7280;
  --ion-color-primary: #3880ff;
  --ion-color-primary-shade: #3171e0;
  --ion-color-dark: #222428;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Dark theme */
    --ion-text-color: #ffffff;
    --ion-color-medium: #a1a1aa;
    --ion-color-medium-shade: #9ca3af;
    --ion-color-primary: #4f46e5;
    --ion-color-primary-shade: #4338ca;
    --ion-color-dark: #f3f4f6;
  }
}
```

### Custom Theme Example

```css
/* Brand-specific theme */
.notification-toast {
  --toast-primary-color: #6366f1;
  --toast-secondary-color: #ec4899;
  --toast-success-color: #10b981;
  --toast-warning-color: #f59e0b;
  --toast-error-color: #ef4444;
}

/* Type-specific styling */
.notification-toast[data-type="success"] {
  border-left: 4px solid var(--toast-success-color);
}

.notification-toast[data-type="error"] {
  border-left: 4px solid var(--toast-error-color);
}
```

## Animation Timings

### Default Animation Configuration

```typescript
// Enter animation
transition(':enter', [
  style({ opacity: 0, transform: 'translateX(100%) scale(0.9)' }),
  animate(
    '600ms cubic-bezier(.18,.89,.32,1.28)', // Bounce-back easing
    style({ opacity: 1, transform: 'translateX(0) scale(1)' })
  ),
])

// Exit animation
transition(':leave', [
  animate(
    '400ms ease-in', // Smooth exit
    style({ opacity: 0, transform: 'translateX(120%) scale(0.9)' })
  )
])
```

### Reduced Motion Support

```typescript
// Reduced motion animations
transition(':enter', [
  style({ opacity: 0 }),
  animate('200ms ease', style({ opacity: 1 }))
])

transition(':leave', [
  animate('200ms ease', style({ opacity: 0 }))
])
```

### Custom Animation Timings

You can customize animation timings by extending the component:

```typescript
@Component({
  // ... other config
  animations: [
    trigger('customToastState', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-100px)' }),
        animate('800ms cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          style({ opacity: 1, transform: 'translateY(0)' })
        ),
      ]),
      // ... exit animation
    ])
  ]
})
```

## Accessibility

### ARIA Support

The component includes proper ARIA attributes:

```html
<div class="notification-toast" 
     role="alert" 
     aria-live="polite"
     aria-atomic="true">
  <!-- content -->
</div>
```

### Keyboard Navigation

- `Escape` key dismisses the notification
- Action buttons are keyboard focusable
- Screen reader compatible

### Focus Management

```typescript
// Auto-focus first action button when multiple actions present
if (this.notification?.data?.actions?.length) {
  setTimeout(() => {
    const firstButton = document.querySelector('.action-button');
    (firstButton as HTMLElement)?.focus();
  }, 100);
}
```

## Advanced Features

### Pause on Hover

Notifications automatically pause their auto-dismiss timer when hovered:

```typescript
onHover() {
  if (this.autoHideTimeout && !this.timerPaused) {
    clearTimeout(this.autoHideTimeout);
    const timeElapsed = Date.now() - this.lastStartTimestamp;
    this.remainingTime = this.remainingTime - timeElapsed;
    this.timerPaused = true;
  }
}
```

### Progress Bar

Visual progress indicator shows remaining time:

```css
.progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #5cbbf6;
  animation: progress-bar-width linear forwards;
}

@keyframes progress-bar-width {
  from { width: 100%; }
  to { width: 0%; }
}
```

### Multi-line Text Support

Supports both string and string array for body content:

```typescript
// Single string
body: 'Single line message'

// Multi-line array
body: [
  'Line 1: System update completed',
  'Line 2: All services restored',
  'Line 3: Thank you for your patience'
]
```


## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+
- iOS Safari 13+
- Android Chrome 80+

## Performance Considerations

- Uses `OnPush` change detection strategy
- Lazy loads media content
- Efficient memory cleanup on destroy
- Optimized animations with `will-change`
- RequestAnimationFrame for smooth interactions

## Troubleshooting

### Common Issues

1. **Animations not working**: Ensure `BrowserAnimationsModule` is imported
2. **Media not loading**: Check CORS headers for external media URLs
3. **Actions not working**: Verify `Router` is properly injected
4. **Styling issues**: Check CSS custom property support

### Debug Mode

Enable debug logging:

```typescript
// In service
console.log('📊 Notification shown:', payload);
console.log('📊 Action clicked:', actionUrl);
console.log('📊 Notification dismissed:', notificationId);
```

