import { Compo<PERSON>, <PERSON><PERSON>nit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { <PERSON>Sanitizer, SafeHtml, SafeUrl } from '@angular/platform-browser';
import { PushNotificationService, NotificationToastPayload } from '../../services/push-notification.service';
// Define interface locally since mobile-components import is not available

import { trigger, transition, style, animate, AnimationTriggerMetadata, keyframes } from '@angular/animations';

@Component({
  selector: 'app-notification-toast',
  standalone: true,
  imports: [CommonModule, IonicModule],
  templateUrl: './notification-toast.component.html',
  animations: [
    trigger('toastState', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(100%) scale(0.9)' }),
        animate(
          '600ms cubic-bezier(.18,.89,.32,1.28)',
          style({ opacity: 1, transform: 'translateX(0) scale(1)' })
        ),
      ]),
      transition(':leave', [
        animate(
          '400ms ease-in',
          style({ opacity: 0, transform: 'translateX(120%) scale(0.9)' })
        )
      ])
    ]),
    trigger('toastStateReducedMotion', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('200ms ease', style({ opacity: 1 }))
      ]),
      transition(':leave', [
        animate('200ms ease', style({ opacity: 0 }))
      ])
    ])
  ],
  styles: [`
    .notification-toast {
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      padding: 20px;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.12), 0 2px 8px rgba(0,0,0,0.08);
      z-index: 1000;
      min-width: 380px;
      max-width: 420px;
      border: 1px solid rgba(0,0,0,0.06);
      backdrop-filter: blur(8px);
    }

    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .close-button {
      position: absolute;
      top: 16px;
      right: 16px;
      font-size: 20px;
      cursor: pointer;
      color: var(--ion-color-medium, #92949c);
      transition: color 0.2s ease;
      z-index: 10;
    }

    .close-button:hover {
      color: var(--ion-color-dark, #222428);
    }

    .notification-title {
      margin: 0 24px 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--ion-text-color, #1a1a1a);
      line-height: 1.3;
    }

    .notification-body {
      margin: 0 24px 12px 0;
      color: var(--ion-color-medium-shade, #6b7280);
      line-height: 1.5;
    }

    .notification-body.short {
      font-size: 15px;
    }

    .notification-body.long pre {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      white-space: pre-wrap;
      word-wrap: break-word;
      margin: 0;
      line-height: 1.4;
    }

    .notification-image {
      width: 100%;
      border-radius: 12px;
      margin-bottom: 16px;
      max-height: 180px;
      object-fit: cover;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .rich-content {
      margin: 0 24px 12px 0;
      font-size: 14px;
      line-height: 1.5;
    }

    .rich-content p {
      margin: 0 0 8px 0;
    }

    .rich-content ul {
      margin: 8px 0;
      padding-left: 20px;
    }

    .rich-content li {
      margin-bottom: 4px;
    }

    .media-content {
      margin: 12px 24px 12px 0;
    }

    .notification-video,
    .notification-audio {
      width: 100%;
      border-radius: 8px;
      outline: none;
    }

    .notification-video {
      max-height: 200px;
    }

    .actions {
      display: flex;
      gap: 8px;
      margin: 16px 24px 0 0;
      flex-wrap: wrap;
    }

    .action-button {
      padding: 8px 16px;
      border: none;
      border-radius: 8px;
      background: var(--ion-color-primary, #3880ff);
      color: white;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      outline: none;
    }

    .action-button:hover {
      background: var(--ion-color-primary-shade, #3171e0);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(56, 128, 255, 0.3);
    }

    .action-button:active {
      transform: translateY(0);
    }

    .progress {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 3px;
      background-color: #5cbbf6;
      width: 100%;
      animation: progress-bar-width linear forwards;
      animation-name: progress-bar-width;
    }

    .progress.reduced-motion {
      animation: none !important;
      width: 0% !important;
      transition: none !important;
    }

    @keyframes progress-bar-width {
      from {
        width: 100%;
      }
      to {
        width: 0%;
      }
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%); opacity: 0;
      }
      to {
        transform: translateX(0); opacity: 1;
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .notification-toast {
        background: #1c1c1e;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      }
      .progress {
        background-color: #4582e6;
      }
    }

    @media (prefers-reduced-motion: reduce) {
      .progress {
        animation: none !important;
        transition: none !important;
        width: 0% !important;
      }
    }
  `]
})
export class NotificationToastComponent implements OnInit, OnDestroy {
  notification: NotificationToastPayload | null = null;
  private subscription: Subscription | null = null;
  private autoHideTimeout: any = null;
  state: any = '';
  // To handle pausing on hover/focus
  private timerPaused = false;
  private remainingTime = 0;
  private lastStartTimestamp = 0;

  get reducedMotion() {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    }
    return false;
  }

  constructor(
    private pushNotificationService: PushNotificationService,
    private router: Router,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit() {
    this.subscription = this.pushNotificationService.notifications$.subscribe(notification => {
      if (notification) {
        // Clear any existing timeout
        if (this.autoHideTimeout) {
          clearTimeout(this.autoHideTimeout);
        }
        this.notification = notification;
        this.state = this.reducedMotion ? 'reduced' : 'standard';
        const duration = notification.durationMs || notification.data?.durationMs || 7000;
        // Timer metadata
        this.remainingTime = duration;
        this.lastStartTimestamp = Date.now();
        this.setAutoHideTimer(duration);
      }
    });
  }

  setAutoHideTimer(duration: number) {
    if (this.autoHideTimeout) {
      clearTimeout(this.autoHideTimeout);
    }
    this.autoHideTimeout = setTimeout(() => {
      this.dismissNotification();
    }, duration);
    this.lastStartTimestamp = Date.now();
  }

  onHover() {
    if (this.autoHideTimeout && !this.timerPaused) {
      clearTimeout(this.autoHideTimeout);
      // pause the timer
      const timeElapsed = Date.now() - this.lastStartTimestamp;
      this.remainingTime = this.remainingTime - timeElapsed;
      this.timerPaused = true;
    }
  }
  onLeave() {
    if (this.timerPaused && this.remainingTime > 0) {
      this.setAutoHideTimer(this.remainingTime);
      this.timerPaused = false;
    }
  }

  /**
   * Dismiss the notification
   */
  dismissNotification() {
    if (this.notification?.data?.id) {
      // Track notification dismissal
      console.log('📊 Would track notification dismissal for ID:', this.notification.data.id, 'group:', this.notification.data?.group);
    }

    this.notification = null;
    if (this.autoHideTimeout) {
      clearTimeout(this.autoHideTimeout);
      this.autoHideTimeout = null;
    }
  }

  /**
   * Handle notification action click (single default action for backward compatibility)
   */
  handleAction() {
    if (this.notification?.data?.action || this.notification?.data?.actionUrl) {
      const url = this.notification?.data?.action || this.notification?.data?.actionUrl;
      if (url) {
        // Could be a native deep link or an Angular route
        if (url.startsWith('http') || url.startsWith('app://') || url.startsWith('intent://') || url.startsWith('deeplink:')) {
          window.open(url, '_blank');
        } else {
          this.router.navigateByUrl(url);
        }
        this.dismissNotification();
        return;
      }
    }

    // If actions array exists, handle first one as default
    if (this.notification?.actions?.length) {
      this.onAction(this.notification.actions[0].deepLink);
      return;
    }

    // If actions exist on data
    if (this.notification?.data?.actions?.length) {
      this.onAction(this.notification.data.actions[0].deepLink);
      return;
    }
  }

  /**
   * Handle an action button click
   */
  onAction(deepLink: string) {
    if (!deepLink) return;
    if (deepLink.startsWith('http') || deepLink.startsWith('app://') || deepLink.startsWith('intent://') || deepLink.startsWith('deeplink:')) {
      window.open(deepLink, '_blank');
    } else {
      this.router.navigateByUrl(deepLink);
    }
    this.dismissNotification();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }

    if (this.autoHideTimeout) {
      clearTimeout(this.autoHideTimeout);
    }
  }

  isShortText(text: string | string[] | undefined): boolean {
    if (Array.isArray(text)) {
      // If body is array, never short text (render as long or join as needed)
      return false;
    }
    return !!text && text.length < 100;
  }

  isLongText(text: string | string[] | undefined): boolean {
    if (Array.isArray(text)) {
      // If body is array, always treat as long text
      return true;
    }
    return !!text && text.length >= 100;
  }

  sanitizeHtml(html: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  isVideo(url: string | undefined): boolean {
    if (!url) return false;
    return /\.(mp4|webm|ogg)$/i.test(url);
  }

  isAudio(url: string | undefined): boolean {
    if (!url) return false;
    return /\.(mp3|wav|ogg)$/i.test(url);
  }

  sanitizeMedia(url: string | undefined): SafeUrl {
    return url ? this.sanitizer.bypassSecurityTrustResourceUrl(url) : '';
  }

  /**
   * Helper method to render notification body consistently as a string
   * Handles both string and string[] types
   */
  renderBody(body: string | string[] | undefined): string {
    return PushNotificationService.renderBody(body);
  }

  /**
   * Check if the notification body is an array
   */
  isArray(body: string | string[] | undefined): boolean {
    return PushNotificationService.isArray(body);
  }
}
