<div 
  class="toast notification-toast"
  [@toastState]="!reducedMotion ? 'visible' : null" 
  [@toastStateReducedMotion]="reducedMotion ? 'visible' : null" 
  *ngIf="notification"
  (mouseenter)="onHover()"
  (mouseleave)="onLeave()"
  (focusin)="onHover()"
  (focusout)="onLeave()"
>
  <!-- Close button -->
  <ion-icon name="close-outline" class="close-button" (click)="dismissNotification()"></ion-icon>
  
  <!-- Image content -->
  <img *ngIf="notification.imageUrl" class="notification-image" [src]="notification.imageUrl" alt="Notification image" />
  
  <!-- Main content -->
  <div class="content">
    <h4 *ngIf="notification.title" class="notification-title">{{notification.title}}</h4>
    
    <!-- Text body rendering -->
    <p *ngIf="isShortText(notification?.body)" class="notification-body short">{{renderBody(notification.body)}}</p>
    <div *ngIf="isLongText(notification?.body)" class="notification-body long">
      <pre>{{renderBody(notification.body)}}</pre>
    </div>
    
    <!-- Rich HTML content -->
    <div *ngIf="notification.richHtml" class="rich-content" [innerHTML]="sanitizeHtml(notification.richHtml)"></div>
    
    <!-- Media content -->
    <div *ngIf="notification.mediaUrl" class="media-content">
      <video *ngIf="isVideo(notification.mediaUrl)" 
             [src]="sanitizeMedia(notification.mediaUrl)" 
             controls 
             class="notification-video"
             preload="metadata">
        Your browser does not support the video tag.
      </video>
      <audio *ngIf="isAudio(notification.mediaUrl)" 
             [src]="sanitizeMedia(notification.mediaUrl)" 
             controls 
             class="notification-audio"
             preload="metadata">
        Your browser does not support the audio tag.
      </audio>
    </div>
  </div>
  
  <!-- Action buttons -->
  <div class="actions" *ngIf="notification.data?.actions?.length">
    <button *ngFor="let action of notification.data.actions" 
            (click)="onAction(action.deepLink)"
            class="action-button">
      {{action.label}}
    </button>
  </div>
  
  <!-- Progress bar -->
  <div 
    class="progress" 
    [ngClass]="{ 'reduced-motion': reducedMotion }"
    [style.animation-duration.ms]="!reducedMotion ? (notification.durationMs || notification.data?.durationMs || 7000) : null"
    [style.width]="reducedMotion ? '0%' : null"
  ></div>
</div>

