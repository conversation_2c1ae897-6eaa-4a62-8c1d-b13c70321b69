import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { LocationStrategy } from '@angular/common';

import { Router } from '@angular/router';
import { LoadingController, MenuController } from '@ionic/angular';
import {
  AbstractService,
  KeyCloakService,
  LogService,
  LssConfig,
  MemberProfile,
  MemberService,
} from 'lp-client-api';
import { App, URLOpenListenerEvent } from '@capacitor/app';
import { environment } from '../environments/environment';
import { Platform } from '@ionic/angular';
import { Preferences } from '@capacitor/preferences';
import { PushNotificationService } from './services/push-notification.service';
import { NotificationToastComponent } from './components/notification-toast/notification-toast.component';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit, On<PERSON><PERSON>roy {
  menuList = [
    { icon: 'home', text: 'Home', link: 'public/home' },
    { icon: 'images', text: 'Photo', link: 'pages/home' },
    { icon: 'person-circle-outline', text: 'Profile', link: 'pages/profile' },
    { icon: 'settings-outline', text: 'Settings', link: 'secure/notification-settings' },
  ];
  count?: any = 0;
  loading?: HTMLIonLoadingElement;
  slowLoad = false;
  environment = environment;
  profile?: MemberProfile;
  showTabs = false; // Can be toggled based on configuration

  _currentMenu = { icon: '', text: '', link: '' };

  constructor(
    private menu: MenuController,
    private kc: KeyCloakService,
    protected readonly router: Router,
    private memberService: MemberService,
    private logService: LogService,
    private platform: Platform,
    private loadingCtrl: LoadingController,
    public lssConfig: LssConfig,
    private LocationStrategy: LocationStrategy,
    private pushNotificationService: PushNotificationService
  ) {
    this.platform.ready().then(() => {
      console.log('🚀 Platform ready, checking platform:', {
        capacitor: this.platform.is('capacitor'),
        ios: this.platform.is('ios'),
        android: this.platform.is('android'),
        hybrid: this.platform.is('hybrid')
      });
      
      // Initialize push notifications on app start for native platforms
      if (this.platform.is('capacitor') || this.platform.is('ios') || this.platform.is('android')) {
        console.log('🔥 Initializing push notifications on app start');
        this.pushNotificationService.initialize();
      } else {
        console.log('⚠️ Not initializing push notifications - platform not detected as native');
      }
      
      if (this.platform.is('hybrid')) {
        App.addListener('appStateChange', ({ isActive }) => {
          console.log('App state changed. Is active?', isActive);
          if (isActive && this.kc.authSuccess) {
            if (this.kc.keycloak?.tokenParsed) {
              if (this.kc.keycloak.tokenParsed.exp) {
                if (
                  this.kc.keycloak.tokenParsed.exp <
                  new Date().getTime() / 1000
                ) {
                  this.kc.authed().then((authed: boolean) => {
                    if (!authed) {
                      console.log('Go to login screen');
                    }
                  });
                }
              }
            }
          }
        });
        App.addListener('appUrlOpen', (data) => {
          console.log('App opened with URL:', data);
        });
      }
    });
  }

  ngOnInit() {
    this.kc.authStatus.subscribe((data) => {
      console.log('this.kc.authStatus', data);
      if (data != null && data.eventName === 'init') {
        this.loadingCtrl
          .create({
            message: 'Loading Authentication',
            duration: 10000,
          })
          .then((data) => {
            console.log('Loading created');
            if (!this.slowLoad) {
              this.loading = data;
              this.loading.present();
            } else {
              this.slowLoad = false;
            }
          });
      } else {
        this.slowLoad = true;
        if (this.loading) {
          this.loading.dismiss();
        }
      }
      if (data != null) {
        if (data.eventName === 'login') {
          if (this.kc.keycloak) {
            Preferences.set({
              key: 'login',
              value: JSON.stringify({
                refreshToken: this.kc.keycloak.refreshToken,
                idToken: this.kc.keycloak.idToken,
                token: this.kc.keycloak.token,
              }),
            }).then(() => {
              console.log('Token Login');
            });
          }
          this.getNotificationCount();
          this.memberService
            .getProfile(this.kc.lpUniueReference)
            .subscribe((profile: MemberProfile) => {
              this.profile = profile;
              if (this.router.url === '/public/signup') {
                this.router.navigate(['/app/account']);
              }
            });
          
          // Initialize push notifications after login
          this.pushNotificationService.initializeAfterLogin();
        } else if (data.eventName === 'refresh') {
          if (this.kc.keycloak) {
            Preferences.set({
              key: 'login',
              value: JSON.stringify({
                refreshToken: this.kc.keycloak.refreshToken,
                idToken: this.kc.keycloak.idToken,
                token: this.kc.keycloak.token,
              }),
            }).then(() => {
              console.log('Token Refresh');
            });
          }
        } else if (data.eventName === 'expired') {
          //this.kc.keycloak?.updateToken(60000);
        } else if (data.eventName === 'logout') {
          Preferences.remove({ key: 'login' }).then(() => {
            console.log('Token Removed');
          });
          this.memberService.removeSession();
          this.router.navigate(['/']);
        }
      }
    });

    this.generateMenu();
    const htmlEl: any = document.querySelector('html');
    const theme = this.lssConfig.theme;
    htmlEl.style.setProperty('--ion-color-primary', theme.colours.primary);
    htmlEl.style.setProperty(
      '--ion-color-primary-contrast',
      theme.colours.primaryContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-primary-shade',
      theme.colours.primaryShade
    );
    htmlEl.style.setProperty(
      '--ion-color-primary-tint',
      theme.colours.primaryTint
    );

    htmlEl.style.setProperty('--ion-color-secondary', theme.colours.secondary);
    htmlEl.style.setProperty(
      '--ion-color-secondary-contrast',
      theme.colours.secondaryContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-secondary-shade',
      theme.colours.secondaryShade
    );
    htmlEl.style.setProperty(
      '--ion-color-secondary-tint',
      theme.colours.secondaryTint
    );

    htmlEl.style.setProperty('--ion-color-tertiary', theme.colours.tertiary);
    htmlEl.style.setProperty(
      '--ion-color-tertiary-contrast',
      theme.colours.tertiaryContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-tertiary-shade',
      theme.colours.tertiaryShade
    );
    htmlEl.style.setProperty(
      '--ion-color-tertiary-tint',
      theme.colours.tertiaryTint
    );

    htmlEl.style.setProperty('--ion-color-success', theme.colours.success);
    htmlEl.style.setProperty(
      '--ion-color-success-contrast',
      theme.colours.successContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-success-shade',
      theme.colours.successShade
    );
    htmlEl.style.setProperty(
      '--ion-color-success-tint',
      theme.colours.successTint
    );

    htmlEl.style.setProperty('--ion-color-warning', theme.colours.warning);
    htmlEl.style.setProperty(
      '--ion-color-warning-contrast',
      theme.colours.warningContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-warning-shade',
      theme.colours.warningShade
    );
    htmlEl.style.setProperty(
      '--ion-color-warning-tint',
      theme.colours.warningTint
    );

    htmlEl.style.setProperty('--ion-color-danger', theme.colours.danger);
    htmlEl.style.setProperty(
      '--ion-color-danger-contrast',
      theme.colours.dangerContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-danger-shade',
      theme.colours.dangerShade
    );
    htmlEl.style.setProperty(
      '--ion-color-danger-tint',
      theme.colours.dangerTint
    );

    htmlEl.style.setProperty('--ion-color-medium', theme.colours.medium);
    htmlEl.style.setProperty(
      '--ion-color-medium-contrast',
      theme.colours.mediumContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-medium-shade',
      theme.colours.mediumShade
    );
    htmlEl.style.setProperty(
      '--ion-color-medium-tint',
      theme.colours.mediumTint
    );

    htmlEl.style.setProperty('--ion-color-base', theme.colours.base);
    htmlEl.style.setProperty(
      '--ion-color-base-contrast',
      theme.colours.baseContrast
    );
    htmlEl.style.setProperty('--ion-color-base-shade', theme.colours.baseShade);
    htmlEl.style.setProperty('--ion-color-base-tint', theme.colours.baseTint);

    htmlEl.style.setProperty('--ion-color-light', theme.colours.light);
    htmlEl.style.setProperty(
      '--ion-color-light-contrast',
      theme.colours.lightContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-light-shade',
      theme.colours.lightShade
    );
    htmlEl.style.setProperty('--ion-color-light-tint', theme.colours.lightTint);
  }

  ngOnDestroy(): void {
    this.kc.authStatus.unsubscribe();
  }

  generateMenu(): void {
    // todo: generate this from settings
    if (this.lssConfig.navigation?.routes) {
      this.menuList = this.lssConfig.navigation.routes.filter(
        (rt: any) => rt.sidebar
      );
    }
    
    // Ensure Settings is always available
    const hasSettings = this.menuList.find(item => item.link === 'secure/notification-settings');
    if (!hasSettings) {
      this.menuList.push({ icon: 'settings-outline', text: 'Settings', link: 'secure/notification-settings' });
    }
  }

  openMenu() {
    this.menu.open('end');
  }

  get loggedin(): boolean | undefined {
    return this.kc.authSuccess;
  }

  get pageTitle(): string {
    let url = this.router.url;
    if (url === '/') {
      url = 'pages/home';
    } else {
      url = url.substring(1);
    }
    if (this._currentMenu == null || url !== this._currentMenu.link) {
      this._currentMenu = this.menuList.filter(
        (filter) => url === filter.link
      )[0];
    }
    return url; // todo: switch back to returning from menu page title
  }

  get pageText(): string {
    let url = this.router.url;
    let text: any = '';
    
    // Check specific routes
    if (url === '/public/storedetail') return 'Store Detail';
    if (url === '/secure/security') return 'Security';
    if (url === '/secure/profile') return 'Profile';
    if (url === '/secure/pools') return `Account ${this.lssConfig.terminology?.pool?.plural || 'Pools'}`;
    if (url === '/secure/points') return 'Points';
    if (url === '/secure/transactions') return 'Transactions';
    if (url === '/secure/virtualcard') return 'Virtual Card';
    if (url === '/secure/contactus') return 'Contact Us';
    if (url === '/app/stores') return 'Stores';
    if (url === '/public/stores') return 'Stores';
    if (url === '/public/notifications') return 'Notifications';

    text = this.menuList.filter((filter) => url === filter.link)[0];
    return text ? text.text : 'Home'; // todo: switch back to returning from menu page title
  }

  close(menuItem: any) {
    this.menu.close();
    this._currentMenu = menuItem;
  }
  back(): void {
    if (this.LocationStrategy.historyGo) this.LocationStrategy.historyGo(-1);
  }
  login() {
    if (!this.kc.authSuccess) {
      this.kc.keycloak?.login().then((data) => {
        console.log(data);
      });
    }
  }

  logout() {
    if (this.kc.authSuccess) {
      this.kc.keycloak?.logout();
    }
  }

  toggleMenu() {
    this.menu.toggle('main-menu2');
  }

  closeMenu() {
    this.menu.close('main-menu2');
  }

  getMenuIcon(menuText: string): string {
    const iconMap: { [key: string]: string } = {
      'Home': 'home-outline',
      'Profile': 'person-outline',
      'Transactions': 'receipt-outline',
      'Virtual Card': 'card-outline',
      'Card': 'card-outline',
      'Stores': 'location-outline',
      'Points': 'wallet-outline',
      'Points Transfer': 'swap-horizontal-outline',
      'Account Pooling': 'people-outline',
      'Communities': 'people-outline',
      'Security': 'shield-checkmark-outline',
      'Pools': 'people-outline',
      'Contact Us': 'chatbubbles-outline',
      'Settings': 'settings-outline'
    };
    return iconMap[menuText] || 'ellipse-outline';
  }

  getNotificationCount() {
    this.memberService
      .getNotificationsCount(this.kc.lpUniueReference)
      .subscribe({
        error: (error) => {
          console.log(error.message);
        },
        next: (body: any) => {
          console.log('body', body.count);
          if (body !== undefined) {
            this.count = body.count;
          }
        },
      });
  }

  get showMenuBack(): boolean | undefined {
    return this.router.url.includes('/app/home') ? false : true;
  }
}
