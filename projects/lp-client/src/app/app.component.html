<ion-app id="app" class="ion-page app-background bg-app">
  <!-- Desktop Sidebar Menu (Permanent Left) -->
  <div class="desktop-sidebar" *ngIf="loggedin">
    <div class="sidebar-header">
      <div class="sidebar-logo" *ngIf="lssConfig.navigation.sidebarIcon">
        <img [src]="lssConfig.navigation.sidebarIcon" alt="Logo" />
      </div>
      <div class="sidebar-profile" *ngIf="profile">
        <div class="profile-avatar">
          <ion-icon name="person-circle"></ion-icon>
        </div>
        <h3>{{ profile.givenNames }} {{ profile.surname }}</h3>
        <p>{{ profile.newMembershipNumber }}</p>
      </div>
    </div>
    
    <div class="sidebar-menu">
      <div class="menu-items">
        <a 
          *ngFor="let item of menuList" 
          class="sidebar-menu-item" 
          [class.active]="item.link == '/' + pageTitle"
          [routerLink]="[item.link]">
          <ion-icon [name]="getMenuIcon(item.text)"></ion-icon>
          <span>{{ item.text }}</span>
        </a>
        
        <div class="sidebar-divider"></div>
        
        <a class="sidebar-menu-item logout" (click)="logout()">
          <ion-icon name="log-out-outline"></ion-icon>
          <span>Sign Out</span>
        </a>
      </div>
    </div>
    
    <div class="sidebar-footer">
      <div class="app-version">
        <p>Version 1.0.0</p>
      </div>
    </div>
  </div>

  <!-- Main Layout Container -->
  <div class="main-layout" [class.with-sidebar]="loggedin" id="main-content">
    <!-- Modern Header -->
    <ion-header class="modern-header" *ngIf="loggedin">
      <ion-toolbar class="modern-toolbar">
        <ion-buttons slot="start">
          <ion-button class="back-button" (click)="back()" *ngIf="showMenuBack">
            <ion-icon name="chevron-back-outline"></ion-icon>
          </ion-button>
        </ion-buttons>
        
        <ion-title class="modern-title">{{ pageText }}</ion-title>
        
        <ion-buttons slot="end">
          <ion-button class="notification-button" [routerLink]="'/public/notifications'" *ngIf="loggedin">
            <div class="notification-wrapper">
              <ion-icon name="notifications-outline"></ion-icon>
              <span class="notification-badge" *ngIf="count > 0">{{ count > 99 ? '99+' : count }}</span>
            </div>
          </ion-button>
          <ion-button class="menu-toggle-button mobile-only" (click)="toggleMenu()">
            <ion-icon name="menu-outline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <!-- Main Content Area -->
    <ion-router-outlet class="app-background bg-app main-content-area"></ion-router-outlet>
  </div>

  <!-- Notification Toast Component -->
  <app-notification-toast></app-notification-toast>

  <!-- Modern Side Menu -->
  <ion-menu class="modern-menu" side="end" menuId="main-menu2" contentId="main-content">
    <ion-header class="menu-header">
      <ion-toolbar>
        <ion-title>Menu</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="closeMenu()" class="close-menu-button">
            <ion-icon name="close-outline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    
    <ion-content class="menu-content">
      <!-- Profile Section (when logged in) -->
      <div class="profile-section" *ngIf="loggedin && profile">
        <div class="profile-avatar">
          <ion-icon name="person-circle"></ion-icon>
        </div>
        <h3>{{ profile.givenNames }} {{ profile.surname }}</h3>
        <p>{{ profile.newMembershipNumber }}</p>
      </div>

      <!-- Logo Section -->
      <div class="logo-section" *ngIf="lssConfig.navigation.sidebarIcon && !loggedin">
        <img [src]="lssConfig.navigation.sidebarIcon" alt="Logo" />
      </div>

      <!-- Menu Items -->
      <ion-list class="menu-list">
        <!-- Logged In Menu -->
        <ng-container *ngIf="loggedin">
          <ion-menu-toggle *ngFor="let item of menuList" auto-hide="false">
            <ion-item 
              class="menu-item" 
              [class.active]="item.link == '/' + pageTitle"
              [routerLink]="[item.link]"
              button
              detail="false">
              <ion-icon [name]="getMenuIcon(item.text)" slot="start"></ion-icon>
              <ion-label>{{ item.text }}</ion-label>
            </ion-item>
          </ion-menu-toggle>
          
          <div class="menu-divider"></div>
          
          <ion-menu-toggle auto-hide="false">
            <ion-item class="menu-item logout" (click)="logout()" button detail="false">
              <ion-icon name="log-out-outline" slot="start"></ion-icon>
              <ion-label>Sign Out</ion-label>
            </ion-item>
          </ion-menu-toggle>
        </ng-container>

        <!-- Not Logged In Menu -->
        <ng-container *ngIf="!loggedin">
          <ion-menu-toggle auto-hide="false">
            <ion-item class="menu-item" (click)="login()" button detail="false">
              <ion-icon name="log-in-outline" slot="start"></ion-icon>
              <ion-label>Sign In</ion-label>
            </ion-item>
          </ion-menu-toggle>
          
          <ion-menu-toggle auto-hide="false">
            <ion-item class="menu-item" [routerLink]="['/public/validate']" button detail="false">
              <ion-icon name="person-add-outline" slot="start"></ion-icon>
              <ion-label>Sign Up</ion-label>
            </ion-item>
          </ion-menu-toggle>
          
          <ion-menu-toggle auto-hide="false">
            <ion-item class="menu-item" [routerLink]="['/public/password']" button detail="false">
              <ion-icon name="key-outline" slot="start"></ion-icon>
              <ion-label>Forgot Password</ion-label>
            </ion-item>
          </ion-menu-toggle>
        </ng-container>
      </ion-list>
    </ion-content>
    
    <!-- App Version -->
    <ion-footer class="menu-footer">
      <div class="app-version">
        <p>Version 1.0.0</p>
      </div>
    </ion-footer>
  </ion-menu>

</ion-app>