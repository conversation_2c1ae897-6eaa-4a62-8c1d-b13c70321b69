// Global Typography System
// Based on UX/UI Specification v1.0

// Font Stack
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;

// Type Scale
:root {
  // Font Family
  --ion-font-family: #{$font-family-base};
  
  // Headings
  --h1-font-size: 28px;
  --h1-line-height: 34px;
  --h1-font-weight: 600;
  
  --h2-font-size: 24px;
  --h2-line-height: 30px;
  --h2-font-weight: 600;
  
  --h3-font-size: 20px;
  --h3-line-height: 26px;
  --h3-font-weight: 500;
  
  --h4-font-size: 18px;
  --h4-line-height: 24px;
  --h4-font-weight: 500;
  
  // Body Text
  --body-large-font-size: 18px;
  --body-large-line-height: 24px;
  --body-large-font-weight: 400;
  
  --body-regular-font-size: 16px;
  --body-regular-line-height: 22px;
  --body-regular-font-weight: 400;
  
  --body-small-font-size: 14px;
  --body-small-line-height: 20px;
  --body-small-font-weight: 400;
  
  // Labels & Captions
  --label-font-size: 12px;
  --label-line-height: 16px;
  --label-font-weight: 500;
  --label-letter-spacing: 0.5px;
  --label-text-transform: uppercase;
  
  --caption-font-size: 11px;
  --caption-line-height: 14px;
  --caption-font-weight: 400;
  
  // Button Text
  --button-font-size: 16px;
  --button-font-weight: 500;
  --button-letter-spacing: 0.25px;
  
  // Responsive adjustments for small screens
  @media (max-width: 320px) {
    --h1-font-size: 24px;
    --h1-line-height: 30px;
    
    --h2-font-size: 20px;
    --h2-line-height: 26px;
    
    --h3-font-size: 18px;
    --h3-line-height: 24px;
    
    --body-large-font-size: 16px;
    --body-regular-font-size: 14px;
    --body-small-font-size: 12px;
  }
}

// Typography Classes
.h1 {
  font-size: var(--h1-font-size);
  line-height: var(--h1-line-height);
  font-weight: var(--h1-font-weight);
  margin: 0;
}

.h2 {
  font-size: var(--h2-font-size);
  line-height: var(--h2-line-height);
  font-weight: var(--h2-font-weight);
  margin: 0;
}

.h3 {
  font-size: var(--h3-font-size);
  line-height: var(--h3-line-height);
  font-weight: var(--h3-font-weight);
  margin: 0;
}

.h4 {
  font-size: var(--h4-font-size);
  line-height: var(--h4-line-height);
  font-weight: var(--h4-font-weight);
  margin: 0;
}

.body-large {
  font-size: var(--body-large-font-size);
  line-height: var(--body-large-line-height);
  font-weight: var(--body-large-font-weight);
}

.body-regular {
  font-size: var(--body-regular-font-size);
  line-height: var(--body-regular-line-height);
  font-weight: var(--body-regular-font-weight);
}

.body-small {
  font-size: var(--body-small-font-size);
  line-height: var(--body-small-line-height);
  font-weight: var(--body-small-font-weight);
}

.label {
  font-size: var(--label-font-size);
  line-height: var(--label-line-height);
  font-weight: var(--label-font-weight);
  letter-spacing: var(--label-letter-spacing);
  text-transform: var(--label-text-transform);
}

.caption {
  font-size: var(--caption-font-size);
  line-height: var(--caption-line-height);
  font-weight: var(--caption-font-weight);
}

// Font Weight Utilities
.font-light { font-weight: 300 !important; }
.font-regular { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }

// Text Transform Utilities
.text-uppercase { text-transform: uppercase !important; }
.text-lowercase { text-transform: lowercase !important; }
.text-capitalize { text-transform: capitalize !important; }
.text-normal { text-transform: none !important; }

// Text Alignment
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

// Letter Spacing
.letter-spacing-tight { letter-spacing: -0.25px !important; }
.letter-spacing-normal { letter-spacing: 0 !important; }
.letter-spacing-wide { letter-spacing: 0.25px !important; }
.letter-spacing-wider { letter-spacing: 0.5px !important; }
.letter-spacing-widest { letter-spacing: 1px !important; }

// Line Height
.leading-none { line-height: 1 !important; }
.leading-tight { line-height: 1.25 !important; }
.leading-normal { line-height: 1.5 !important; }
.leading-relaxed { line-height: 1.625 !important; }
.leading-loose { line-height: 2 !important; }

// Truncate Text
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}