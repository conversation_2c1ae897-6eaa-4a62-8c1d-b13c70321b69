// Global Spacing System
// Based on 4px grid for consistency

// Spacing Scale
:root {
  // Base unit: 4px
  --space-0: 0;
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-7: 28px;
  --space-8: 32px;
  --space-9: 36px;
  --space-10: 40px;
  --space-12: 48px;
  --space-14: 56px;
  --space-16: 64px;
  --space-20: 80px;
  --space-24: 96px;
  --space-32: 128px;
  
  // Named sizes
  --space-xs: var(--space-1);   // 4px
  --space-sm: var(--space-2);   // 8px
  --space-md: var(--space-4);   // 16px
  --space-lg: var(--space-6);   // 24px
  --space-xl: var(--space-8);   // 32px
  --space-2xl: var(--space-12); // 48px
  --space-3xl: var(--space-16); // 64px
  
  // Container padding
  --container-padding-mobile: var(--space-4);  // 16px
  --container-padding-tablet: var(--space-6);  // 24px
  --container-padding-desktop: var(--space-8); // 32px
  
  // Component spacing
  --card-padding: var(--space-4);              // 16px
  --card-margin: var(--space-2);               // 8px
  --button-padding-y: var(--space-3);          // 12px
  --button-padding-x: var(--space-6);          // 24px
  --input-padding-y: var(--space-3);           // 12px
  --input-padding-x: var(--space-4);           // 16px
  
  // Section spacing
  --section-padding-y: var(--space-8);         // 32px
  --section-margin-y: var(--space-12);         // 48px
}

// Margin utilities
@each $size, $value in (
  0: var(--space-0),
  1: var(--space-1),
  2: var(--space-2),
  3: var(--space-3),
  4: var(--space-4),
  5: var(--space-5),
  6: var(--space-6),
  8: var(--space-8),
  10: var(--space-10),
  12: var(--space-12),
  16: var(--space-16)
) {
  // All sides
  .m-#{$size} { margin: #{$value} !important; }
  
  // Individual sides
  .mt-#{$size} { margin-top: #{$value} !important; }
  .mr-#{$size} { margin-right: #{$value} !important; }
  .mb-#{$size} { margin-bottom: #{$value} !important; }
  .ml-#{$size} { margin-left: #{$value} !important; }
  
  // Axis
  .mx-#{$size} {
    margin-left: #{$value} !important;
    margin-right: #{$value} !important;
  }
  .my-#{$size} {
    margin-top: #{$value} !important;
    margin-bottom: #{$value} !important;
  }
}

// Padding utilities
@each $size, $value in (
  0: var(--space-0),
  1: var(--space-1),
  2: var(--space-2),
  3: var(--space-3),
  4: var(--space-4),
  5: var(--space-5),
  6: var(--space-6),
  8: var(--space-8),
  10: var(--space-10),
  12: var(--space-12),
  16: var(--space-16)
) {
  // All sides
  .p-#{$size} { padding: #{$value} !important; }
  
  // Individual sides
  .pt-#{$size} { padding-top: #{$value} !important; }
  .pr-#{$size} { padding-right: #{$value} !important; }
  .pb-#{$size} { padding-bottom: #{$value} !important; }
  .pl-#{$size} { padding-left: #{$value} !important; }
  
  // Axis
  .px-#{$size} {
    padding-left: #{$value} !important;
    padding-right: #{$value} !important;
  }
  .py-#{$size} {
    padding-top: #{$value} !important;
    padding-bottom: #{$value} !important;
  }
}

// Gap utilities for flexbox/grid
@each $size, $value in (
  0: var(--space-0),
  1: var(--space-1),
  2: var(--space-2),
  3: var(--space-3),
  4: var(--space-4),
  5: var(--space-5),
  6: var(--space-6),
  8: var(--space-8)
) {
  .gap-#{$size} { gap: #{$value} !important; }
  .gap-x-#{$size} { column-gap: #{$value} !important; }
  .gap-y-#{$size} { row-gap: #{$value} !important; }
}

// Negative margins
@each $size, $value in (
  1: var(--space-1),
  2: var(--space-2),
  3: var(--space-3),
  4: var(--space-4),
  5: var(--space-5),
  6: var(--space-6),
  8: var(--space-8)
) {
  .-mt-#{$size} { margin-top: calc(#{$value} * -1) !important; }
  .-mr-#{$size} { margin-right: calc(#{$value} * -1) !important; }
  .-mb-#{$size} { margin-bottom: calc(#{$value} * -1) !important; }
  .-ml-#{$size} { margin-left: calc(#{$value} * -1) !important; }
}

// Auto margins
.m-auto { margin: auto !important; }
.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}
.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}
.mt-auto { margin-top: auto !important; }
.mr-auto { margin-right: auto !important; }
.mb-auto { margin-bottom: auto !important; }
.ml-auto { margin-left: auto !important; }

// Width and Height utilities
.w-full { width: 100% !important; }
.w-screen { width: 100vw !important; }
.w-auto { width: auto !important; }

.h-full { height: 100% !important; }
.h-screen { height: 100vh !important; }
.h-auto { height: auto !important; }

// Max width utilities
.max-w-xs { max-width: 320px !important; }
.max-w-sm { max-width: 384px !important; }
.max-w-md { max-width: 448px !important; }
.max-w-lg { max-width: 512px !important; }
.max-w-xl { max-width: 576px !important; }
.max-w-full { max-width: 100% !important; }

// Container utilities
.container {
  width: 100%;
  padding-left: var(--container-padding-mobile);
  padding-right: var(--container-padding-mobile);
  margin-left: auto;
  margin-right: auto;
  
  @media (min-width: 768px) {
    padding-left: var(--container-padding-tablet);
    padding-right: var(--container-padding-tablet);
  }
  
  @media (min-width: 1024px) {
    padding-left: var(--container-padding-desktop);
    padding-right: var(--container-padding-desktop);
    max-width: 1024px;
  }
}

// Section spacing
.section {
  padding-top: var(--section-padding-y);
  padding-bottom: var(--section-padding-y);
  
  + .section {
    margin-top: var(--section-margin-y);
  }
}

// Card spacing
.card-spacing {
  padding: var(--card-padding);
  margin: var(--card-margin);
}

// Button spacing
.button-spacing {
  padding: var(--button-padding-y) var(--button-padding-x);
}

// Input spacing
.input-spacing {
  padding: var(--input-padding-y) var(--input-padding-x);
}