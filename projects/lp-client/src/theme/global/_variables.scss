.page-title {
    text-align: center;
    font-size: 22px;

    p {
        margin-top: 0;
        margin-block-start: 0;
    }    
}

.ios {
    --ion-safe-area-top: 44pt;

    ion-tab-bar {
        margin-bottom: 6pt;
    }
}

.md {
    --ion-safe-area-top: 0;
}

html { 
    //background: linear-gradient(rgba(255,255,255,0.65), rgba(255,255,255,0.65)), url("../../assets/images/background.jpg") center/cover no-repeat !important;
    --ion-background-color: var(--ion-color-base, #0072bc);
}

ion-popover {
    --background: #fff;
    --ion-background-color: #fff;

    & ion-item {
        --background-focused: rgba(0, 0, 0, 0.05);
    }
}

ion-item{
    div.validator-error{
        background-color: var(--ion-color-danger-tint);
        color: var(--ion-color-danger-contrast);
        width: 100%;
    }
}