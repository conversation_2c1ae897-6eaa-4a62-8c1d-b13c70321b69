.statement-list {
  .statement-item {
    --inner-padding-end: 0;
  
    ion-card {
        width: 100%;
        
        ion-card-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
  
            h2 {
                font-size: 19px;
            }
        }
    }
  
    ion-thumbnail {
        width: 60px;
        height: 60px;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--ion-color-primary);
  
        ion-icon {
            color: white;
            font-size: 1.5em;
        }
    }
  }
  
  
  
  .itemdsf {
    ion-card {
        width: 100%;
        
  
        ion-card-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
  
            h2 {
                font-size: 18px;
            }
        }
    }
  
    .sc-ion-label-md-h {
        --color: initial;
        display: block;
        color: var(--color);
        font-family: var(--ion-font-family, inherit);
        font-size: inherit;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
    }
  
    .sc-ion-label-md-s {
        h2 {
            margin-left: 0;
            margin-right: 0;
            margin-top: 2px;
            margin-bottom: 2px;
            font-size: 16px;
            font-weight: normal;
        }
  
        p {
            margin-left: 0;
            margin-right: 0;
            margin-top: 0;
            margin-bottom: 2px;
            font-size: 14px;
            line-height: 20px;
            text-overflow: inherit;
            overflow: inherit;
        }
    }
  }
}