/* Modern Tab Bar Styles */

ion-tabs {
  --background: none;
  background: none;
}

ion-tab-bar {
  --background: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  bottom: 0;
  position: relative;
  border-radius: 0;
  width: 100%;
  border-top: none;
  margin: 0;
  height: 65px;
  padding: 0 10px;
}
  
ion-tab-button {
  --color: #999;
  --color-selected: var(--ion-color-primary, #FF6B35);
  --padding-bottom: 8px;
  --padding-top: 8px;
  border-bottom: none;
  position: relative;

  ion-icon {
    font-size: 24px;
    transition: all 0.3s ease;
  }

  ion-label {
    font-size: 11px;
    font-weight: 500;
    text-transform: none;
    margin-top: 2px;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%) scaleX(0);
    width: 30px;
    height: 3px;
    background: var(--ion-color-primary, #FF6B35);
    border-radius: 0 0 3px 3px;
    transition: transform 0.3s ease;
  }

  &.tab-selected {
    ion-label {
      color: var(--ion-color-primary, #FF6B35);
      font-weight: 600;
    }
    
    &::before {
      transform: translateX(-50%) scaleX(1);
    }
  }
}