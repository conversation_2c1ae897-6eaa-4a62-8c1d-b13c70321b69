/* Generic card style */
.card2 {
    border-radius: 20px;
    margin: 10px 4px;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;

    & .blur {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        background-color: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(8px);
        z-index: -1;
    }

    & .header-image {
        height: 85px;
    }

    ion-card-header {
        background: rgba(255, 255, 255, .4);

        & ion-card-title {
            color: var(--ion-color-primary);
            //text-align: center;
        }

        & .subtitle-button {
            display: flex;
            justify-content: flex-end;
            margin-top: -32px;
        }
    }    

    ion-card-content {
        padding: 0;
    }    
}

.card {
    margin: 10px 4px;
    --clay-background: #fff;
    --clay-shadow-inset-primary: 8px 8px 8px 0 hsla(0,0%,100%,.9);
    --clay-shadow-inset-secondary: 8px 8px 8px 0 hsla(0,0%,100%,.9);

    .card-header {
        text-align: left;
        font-weight: 700;
        font-size: 19px;
        margin: 1rem;
        display: flex;
        align-items: center;

        ion-icon {
            font-size: 22px;
            padding-right: 10px;
        }
    }

    .card-sub-header {
        text-align: left;
        font-weight: 700;
        font-size: 15px;
        margin: 1rem;
        display: flex;
        align-items: center;

        ion-icon {
            font-size: 18px;
            padding-right: 10px;
        }
    }

    .card-text {
        margin: 0 0.7rem;
        margin-block-end: 1rem;
        text-align: left;
    }

    /* Style for overall user account card */
    .account {
        .avatar {
            border-radius: 10px;
        }

        .account-summary-info {
            text-align: left;
            margin-top: -0.2rem;
        }

        .account-name {
            font-weight: 500 !important;
            
            p {
                font-size: 1.2rem;
            }
        }

        .account-level {
            background-color: var(--account-level-color);
            border-radius: 10px !important;
            padding: 0.2rem;
        }

        .account-level-item {
            text-align: center;
            margin: 0 auto;
        
            ion-card-title {
                font-size: 0.8rem;
                color: var(--account-level-text-color);
            }
        
            ion-card-subtitle {
                font-size: 0.6rem;
            }
        }
    }

    /* Style for account item cards, e.g. Tier info */
    .account-item {
        padding-bottom: 0;

        ion-icon {
            font-size: 1.75rem;
            color: var(--ion-color-primary);
            margin-bottom: 1rem;
        }

        ion-card-subtitle {
            font-size: 1.2rem;
            margin-left: 1rem;
        }
    }

    /* Style for quick action cards */
    .quick-action {
        ion-icon {
            font-size: 1.75rem;
            color: var(--ion-color-primary);
            margin-top: -0.3rem;
        }
    }
}

.state-empty {
    width: 100%;
    text-align: center;
    padding-left: 32px;
    padding-right: 32px;
    padding-top: 12vh;
    height: 100%;

    h3 {
        margin-bottom: 30px;
        font-size: 22px;
        font-weight: 300;
        line-height: 1.2;
    }

    img {
        width: 55%;
        max-width: 300px;
        margin-bottom: 30px;
    }
}