
.ios{
    &.ion-selectable-item{
        .div-ion-item {
            min-height: 56px;
            width: 100%;
            --inner-border-width: 0 0 1px 0;
        }
    }
    &.ion-selectable-item.item-has-value{
        ion-label{
            margin-top: 8px;
            transform: scale(0.75);
        }
        .ionic-selectable-icon{
            margin-top: -8px;

        }
        ionic-selectable{
            padding-top: 0px;

        }
    }
    &.ion-selectable-item:not(.item-has-value){
        ion-label {
            margin-top: -12px;
        }
        ion-icon {
            margin-top: 10px;
        }
    }
}

.md{
    &.ion-selectable-item{
        .div-ion-item {
            width: 100%;
            --inner-border-width: 0 0 1px 0;
        }
    }
    &.ion-selectable-item{
        .ionic-selectable-icon{
            margin-top: -8px;
            margin-right: -4px;
        }
    }
}