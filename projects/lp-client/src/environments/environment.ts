// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  env: 'DEV',
  client: 'rmic', // Add your desired client here (rmic, ffz1, etc.)
  firebase: {
    apiKey: "AIzaSyAseqL6KTURHxDYvK7Wygl0ViRIDS1OM3c",
    authDomain: "loyalty-test-project-58bcb.firebaseapp.com",
    projectId: "loyalty-test-project-58bcb",
    storageBucket: "loyalty-test-project-58bcb.firebasestorage.app",
    messagingSenderId: "798238467180",
    appId: "1:798238467180:web:ddab11e43f82af1de86449",
    measurementId: "G-8CCCGX5VTK",
    vapidKey: "BE9BCB1mD6jOEL5wGM46BK4eVsVOTtuq4YmKEACpDqXJymeXQG7O2d8QBpN0NE9FBzsrZ3FnySi7unGKbqdQ1yQ"
  },
  lssConfig: {
    googleApiKey: 'AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0',
    apiId: '812275411',
    appCode: 'start',
    appName: 'Loyalty Client Mobile App',
    appVersion: '0.0.1',
    useAuth: true,
    useION: true,
    useISO: false,
    defaultNotAuthURL: '',
    autoLogout: true,
    autoLogoutTimeout: 180,
    autoLogoutWarning: 120,
    defaultLat: -28.83688693522886,
    defaultLng: 25.**************,
    loadIdentity: false,
    identityBaseUrl: 'http://payroll.dv.lss.si/servlet/systemImage',
    appBaseUrl: 'http://alpine',
    apiBaseUrl: 'https://ffbpqa.loyaltyplus.aero/',
    configAPIUrl: 'http://{hostname}/config/api/v1/',
    memberPhone: {
      dialCode: '+27',
      nationalNumber: '',
    },
    memberCard: '',
    telephone: {
      selectFirstCountry: true,
      preferredCountries: ['za'],
      onlyCountries: ['za', 'ls', 'bw', 'na', 'sz', 'mz'],
    },

    authConfig: {
      // Url of the Identity Provider
      issuer: 'https://authqa.loyaltyplus.aero/auth/realms/AgriBonus',
      redirectUri: location.origin + '/lp-pos', //window.location.href.substring(0, window.location.href.lastIndexOf('/')),
      responseType: 'code',
      clientId: 'mobile-app',
      scope: 'openid profile email offline_access',
      requireHttps: false,
      logoutUrl: '/',
      url: 'https://authqa.loyaltyplus.aero/auth',
      realm: 'ZIADA',
      initOptions: {
        adapter: 'capacitor-native',
        responseMode: 'query',
        redirectUri: 'http://localhost:8100/pages/home',
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri:
          window.location.origin + '/assets/silent-check-sso.html',
      },
    },
  },
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
