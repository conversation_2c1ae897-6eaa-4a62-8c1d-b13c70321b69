#!/bin/bash

# Path to your project.pbxproj file
PROJECT_FILE="App/App.xcodeproj/project.pbxproj"

# Check if the file exists
if [ ! -f "$PROJECT_FILE" ]; then
    echo "Project file not found: $PROJECT_FILE"
    exit 1
fi

# Create backup
cp "$PROJECT_FILE" "${PROJECT_FILE}.bak"

# Add the SandboxConfig.xcconfig reference to the project
if ! grep -q "SandboxConfig.xcconfig" "$PROJECT_FILE"; then
    # Find the last file reference section
    LAST_REF=$(grep -n "fileRef =" "$PROJECT_FILE" | tail -1 | cut -d':' -f1)
    
    # Generate a unique ID for the new file
    NEW_ID="SANDBX$(date +%s | md5sum | head -c 8)"
    
    # Insert the new file reference
    sed -i.tmp "${LAST_REF}a\\
\\t\\t$NEW_ID /* SandboxConfig.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = SandboxConfig.xcconfig; sourceTree = \"<group>\"; };" "$PROJECT_FILE"
    
    # Add it to the main group as well
    MAIN_GROUP=$(grep -n "children = (" "$PROJECT_FILE" | head -1 | cut -d':' -f1)
    sed -i.tmp "${MAIN_GROUP}a\\
\\t\\t\\t$NEW_ID /* SandboxConfig.xcconfig */," "$PROJECT_FILE"
fi

# Modify the build settings to disable sandboxing
sed -i.tmp 's/ENABLE_USER_SCRIPT_SANDBOXING = YES;/ENABLE_USER_SCRIPT_SANDBOXING = NO;/g' "$PROJECT_FILE"

# Clean up temporary files
rm -f "${PROJECT_FILE}.tmp"

echo "Project configuration updated successfully!"
