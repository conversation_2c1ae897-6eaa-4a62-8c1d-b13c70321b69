#!/bin/bash

# Create copies of existing files for any missing icon files
echo "Generating missing icon files..."

# Create 180.png from 258.png if it doesn't exist
if [ ! -f "App/App/Assets.xcassets/AppIcon.appiconset/180.png" ]; then
  echo "Creating 180.png from 258.png"
  cp "App/App/Assets.xcassets/AppIcon.appiconset/258.png" "App/App/Assets.xcassets/AppIcon.appiconset/180.png"
fi

# Create 80.png from 76.png if it doesn't exist
if [ ! -f "App/App/Assets.xcassets/AppIcon.appiconset/80.png" ]; then
  echo "Creating 80.png from 76.png"
  cp "App/App/Assets.xcassets/AppIcon.appiconset/76.png" "App/App/Assets.xcassets/AppIcon.appiconset/80.png"
fi

# Create 120.png from 128.png if it doesn't exist
if [ ! -f "App/App/Assets.xcassets/AppIcon.appiconset/120.png" ]; then
  echo "Creating 120.png from 128.png"
  cp "App/App/Assets.xcassets/AppIcon.appiconset/128.png" "App/App/Assets.xcassets/AppIcon.appiconset/120.png"
fi

# Create 234.png from 216.png if it doesn't exist
if [ ! -f "App/App/Assets.xcassets/AppIcon.appiconset/234.png" ]; then
  echo "Creating 234.png from 216.png"
  cp "App/App/Assets.xcassets/AppIcon.appiconset/216.png" "App/App/Assets.xcassets/AppIcon.appiconset/234.png"
fi

echo "Icons generated successfully!"
