#!/bin/bash

# Create the destination directory if it doesn't exist
mkdir -p "App/App/AppIcon60x60.imageset"
mkdir -p "App/App/AppIcon76x76.imageset"
mkdir -p "App/App/Assets.xcassets/AppIcon.appiconset"

# Create all the necessary icon files if they don't exist
echo "Creating missing icon files..."

# List of required icon files
REQUIRED_ICONS=("120.png" "180.png" "80.png" "76.png" "152.png" "40.png" "58.png" "87.png" "29.png" "60.png" "20.png")
SOURCE_ICON="App/App/Assets.xcassets/AppIcon.appiconset/128.png"

# Check if source icon exists, if not use another one
if [ ! -f "$SOURCE_ICON" ]; then
  SOURCE_ICON="App/App/Assets.xcassets/AppIcon.appiconset/258.png"
  if [ ! -f "$SOURCE_ICON" ]; then
    # Try to find any png file in the directory
    SOURCE_ICON=$(find "App/App/Assets.xcassets/AppIcon.appiconset" -name "*.png" | head -n 1)
    if [ -z "$SOURCE_ICON" ]; then
      echo "Error: No source icon found!"
      exit 1
    fi
  fi
fi

# Create each required icon file if it doesn't exist
for ICON in "${REQUIRED_ICONS[@]}"; do
  if [ ! -f "App/App/Assets.xcassets/AppIcon.appiconset/$ICON" ]; then
    echo "Creating $ICON from source icon"
    cp "$SOURCE_ICON" "App/App/Assets.xcassets/AppIcon.appiconset/$ICON"
  fi
done

# Copy the icons to the required locations for iOS
# Copy the 120x120 icon (60x60@2x) for iPhone
cp "App/App/Assets.xcassets/AppIcon.appiconset/120.png" "App/App/AppIcon60x60.imageset/<EMAIL>" || echo "Warning: Failed to copy 120.png"

# Copy the 180x180 icon (60x60@3x) for iPhone
cp "App/App/Assets.xcassets/AppIcon.appiconset/180.png" "App/App/AppIcon60x60.imageset/<EMAIL>" || echo "Warning: Failed to copy 180.png"

# Copy the 76x76 icon for iPad
cp "App/App/Assets.xcassets/AppIcon.appiconset/76.png" "App/App/AppIcon76x76.imageset/AppIcon76x76.png" || echo "Warning: Failed to copy 76.png"

# Copy the 152x152 icon (76x76@2x) for iPad
cp "App/App/Assets.xcassets/AppIcon.appiconset/152.png" "App/App/AppIcon76x76.imageset/<EMAIL>" || echo "Warning: Failed to copy 152.png"

echo "Icons copied successfully!"
