{"compilerOptions": {"allowUnreachableCode": false, "declaration": true, "esModuleInterop": true, "inlineSources": true, "lib": ["dom", "es2017"], "module": "esnext", "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "dist/esm", "pretty": true, "sourceMap": true, "strict": true, "target": "es2017", "types": []}, "files": ["src/index.ts"]}