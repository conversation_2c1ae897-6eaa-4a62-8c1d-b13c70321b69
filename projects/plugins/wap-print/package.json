{"name": "wap-print", "version": "0.0.1", "description": "Integration to the WAP device printing service", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "files": ["android/src/main/", "android/build.gradle", "dist/", "ios/Plugin/", "WapPrint.podspec"], "author": "LoyaltyPlus", "license": "MIT", "repository": {"type": "git", "url": "git+http://gitlab.loyaltyplus.aero/loyalty/lp-angular.git"}, "bugs": {"url": "http://gitlab.loyaltyplus.aero/loyalty/lp-angular/issues"}, "keywords": ["capacitor", "plugin", "native"], "scripts": {"verify": "npm run verify:ios && npm run verify:android && npm run verify:web", "verify:ios": "cd ios && pod install && xcodebuild -workspace Plugin.xcworkspace -scheme Plugin -destination generic/platform=iOS && cd ..", "verify:android": "cd android && ./gradlew clean build test && cd ..", "verify:web": "npm run build", "lint": "npm run eslint && npm run prettier -- --check && npm run swiftlint -- lint", "fmt": "npm run eslint -- --fix && npm run prettier -- --write && npm run swiftlint -- --fix --format", "eslint": "eslint . --ext ts", "prettier": "prettier \"**/*.{css,html,ts,js,java}\"", "swiftlint": "node-swiftlint", "docgen": "docgen --api WapPrintPluginPlugin --output-readme README.md --output-json dist/docs.json", "build": "npm run clean && tsc && rollup -c rollup.config.mjs", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "npm run build"}, "devDependencies": {"@capacitor/android": "5.0.4", "@capacitor/core": "^5.1.0", "@capacitor/docgen": "^0.0.18", "@capacitor/ios": "5.0.4", "@ionic/eslint-config": "^0.3.0", "@ionic/prettier-config": "^1.0.1", "@ionic/swiftlint-config": "^1.1.2", "eslint": "^7.11.0", "prettier": "~2.3.0", "prettier-plugin-java": "~1.0.2", "rimraf": "^3.0.2", "rollup": "^2.32.0", "swiftlint": "^1.0.1", "typescript": "~4.9.5"}, "peerDependencies": {"@capacitor/core": "5.0.4"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "eslintConfig": {"extends": "@ionic/eslint-config/recommended"}, "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}}