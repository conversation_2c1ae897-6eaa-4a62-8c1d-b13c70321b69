package si.lss.wap.sdk;

import android.content.Context;
import android.util.Log;
import android.os.Bundle;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import com.getcapacitor.JSArray;
import com.getcapacitor.JSObject;

import si.lss.wap.print.WapPrintPlugin;

import com.wisedevice.sdk.IInitDeviceSdkListener;
import com.wisedevice.sdk.WiseDeviceSdk;
import com.wisepos.smartpos.InitPosSdkListener;
import com.wisepos.smartpos.WisePosSdk;
import com.wisepos.smartpos.device.Device;
import com.wisepos.smartpos.WisePosException;
import com.wisepos.smartpos.cardreader.CardReader;
import com.wisepos.smartpos.cardreader.CardListener;
import com.wisepos.smartpos.cardreader.MagCardResponse;
import com.wisepos.smartpos.cardreader.RfCardResponse;
import com.wisepos.smartpos.printer.Printer;
import com.wisepos.smartpos.printer.PrinterListener;
import com.wisepos.smartpos.printer.TextInfo;

import static com.wisepos.smartpos.cardreader.CardSlot.CARD_SLOT_MAG;
import static com.wisepos.smartpos.errorcode.WisePosErrorCode.ERR_SUCCESS;

import java.io.ByteArrayInputStream;
import java.util.Base64;
import java.util.Iterator;
import java.util.HashMap;
import java.util.Map;

public class sdk6 extends WapSdk {
    private WapPrintPlugin wapPlugin;
    private WisePosSdk wisePosSdk = null;
    private WiseDeviceSdk wiseDeviceSdk = null;
    private CardReader cardReader;
    private Printer printer;
    private JSObject printData;

    public static final int PRINT_STYLE_LEFT = 0x01;    //Print style to the left
    public static final int PRINT_STYLE_CENTER = 0x02;  //print style to the center
    public static final int PRINT_STYLE_RIGHT = 0x04;   //print style to the right

    public sdk6(WapPrintPlugin wapPlugin) {
        this.wapPlugin = wapPlugin;
    }

    @Override
    public String version() {
      return "WapSdk_6";
    }

    @Override
    public void initSdk(Context context) {
        Log.d(version(),"initSdk called..");

        this.wisePosSdk = WisePosSdk.getInstance();
        this.wisePosSdk.initPosSdk(context, new InitPosSdkListener() {  //Initialize the SDK and bind the service
            @Override
            public void onInitPosSuccess() {
                Log.d(version(), "initPosSdk: success!");
            }

            @Override
            public void onInitPosFail(int i) {
                Log.d(version(), "initPosSdk: fail!");
            }
        });

        this.wiseDeviceSdk = WiseDeviceSdk.getInstance();
        wiseDeviceSdk.initDeviceSdk(context, new IInitDeviceSdkListener() {
            @Override
            public void onInitPosSuccess() {
                Log.d(version(), "initDeviceSdk: success!");
            }

            @Override
            public void onInitPosFail(int i) {
                Log.d(version(), "initDeviceSdk: fail!");
            }
        });

        this.cardReader = WisePosSdk.getInstance().getCardReader();
        this.printer = WisePosSdk.getInstance().getPrinter();
    }

    @Override
    public boolean print(Context context, Map<String, String> data) throws Exception {
        Log.d(version(), "print called..");

        JSArray printConfig = new JSArray(data.get("printConfig"));
        this.printData = new JSObject(data.get("printData"));
        int status = this.printer.initPrinter();
        Map<String, Object> map = printer.getPrinterStatus();
        
        if (status != ERR_SUCCESS) {
            Log.d(version(), "printer startCaching failed" + String.format(" errCode = 0x%x\n", status));
            return false;
        }

        if (map == null) {
            Log.d(version(), "printer getStatus failed" + String.format(" errCode = 0x%x\n", status));
            return false;
        }

        for (int i = 0; i < printConfig.length(); i++) {
            printLine(new JSObject(printConfig.getString(i)));
        }

        Bundle printerOption = new Bundle();
        //Start printing
        printer.startPrinting(printerOption, new PrinterListener() {
            @Override
            public void onError(int i) {
                Log.d(version(), "startPrinting failed errCode = " + i);
            }

            @Override
            public void onFinish() {
                Log.d(version(), "print success\n");
                try {
                    //After printing, Feed the paper.
                    printer.feedPaper(60);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onReport(int i) {
                //The callback method is reserved and does not need to be implemented
            }
        });

        return false;
    }

    private void printLine(JSObject printItem) throws WisePosException {
        String type = printItem.getString("type");
        TextInfo textInfo = new TextInfo();

        if (type.equalsIgnoreCase("text")) {
            textInfo.setText(setPlaceholders(printItem.getString("value")));
            textInfo.setFontSize(printItem.getInteger("fontSize", 25));
            textInfo.setAlign(printItem.getString("align").equals("CENTER") ? PRINT_STYLE_CENTER : printItem.getString("align").equals("RIGHT") ? PRINT_STYLE_RIGHT : PRINT_STYLE_LEFT );
            textInfo.setBold(printItem.getBoolean("isBold", false));
            textInfo.setItalic(printItem.getBoolean("isItalic", false));

            printer.addSingleText(textInfo);
        } else if (type.equalsIgnoreCase("image")) {
            String imageBase64 = printItem.getString("value");
            imageBase64 = imageBase64.replace("[logo]", printData.getString("logo"));
            imageBase64 = imageBase64.replace("data:image/png;base64,", "");

            byte[] image = Base64.getDecoder().decode(imageBase64);
            Bitmap bitmap = BitmapFactory.decodeStream(new ByteArrayInputStream((image)));

            printer.addPicture(PRINT_STYLE_CENTER, bitmap);
        } else if (type.equalsIgnoreCase("space")) {
            textInfo.setText("");
            textInfo.setFontSize(1);

            printer.addSingleText(textInfo);
        }
    }

    private String setPlaceholders(String text) {
      Iterator<String> keys = printData.keys();
      while(keys.hasNext()) {
        String key = keys.next();
        String placeholder = '[' + key + ']';
        text = text.replace(placeholder, printData.getString(key));
      }

      return text;
    }

    @Override
    public String getDeviceSerial() {
        Log.d(version(), "getDeviceSerial called..");
        String serial = android.os.Build.SERIAL;
        if (serial == null) {
            serial = android.os.Build.getSerial();
        }

        return serial;
    }

    @Override
    public void initCardReader() {
        Log.d(version(), "initCardReader called..");

        if (this.cardReader == null) {
            this.cardReader = WisePosSdk.getInstance().getCardReader();
        }
    }

    @Override
    public void closeCardReader() throws Exception {
        Log.d(version(), "closeCardReader called..");
        cardReader.cancelOperation();
    }

    @Override
    public void readMagStripe(int timeoutLimit) {
        Log.d(version(), "readMagStripe called..");
        JSObject magResult = new JSObject();

        CardListener cardListener = new CardListener() {
            @Override
            public void onError(int err) {
            }

            @Override
            public void onMagCardDetected(MagCardResponse magCardResponse) {
                if(magCardResponse.track1Status == 0) {
                    magResult.put("track1", new String(magCardResponse.track1Data));
                }

                if(magCardResponse.track2Status == 0) {
                    magResult.put("track2", new String(magCardResponse.track2Data));
                }

                if(magCardResponse.track3Status == 0) {
                    magResult.put("track3", new String(magCardResponse.track3Data));
                }
                
                Log.d(version(), "onMagCardDetected: " + magResult.toString());
                wapPlugin.notifyListeners("readMagStripe", magResult);
            }

            @Override
            public void onIcDetected(String atr) {
            }

            @Override
            public void onRfCardDetected(RfCardResponse rfCardResponse) {
            }
        };
        
        try {
            this.cardReader.openCard(CARD_SLOT_MAG, timeoutLimit * 1000, cardListener);
            Log.d(version(), "cardReader opened");
        } catch (WisePosException e) {
            e.printStackTrace();
        }
    }
}
