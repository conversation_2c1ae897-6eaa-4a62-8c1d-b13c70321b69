package si.lss.wap.sdk;

import android.util.Log;

import si.lss.wap.print.WapPrintPlugin;

public class WapSdkFactory {

    public static WapSdk getSdk(WapPrintPlugin wapPlugin) {
        String model = android.os.Build.MODEL;
        Log.d("WapSdkFactory","Configuring SDK for model: " + model);

        if (model.equals("P5SE") || model.equals("P5MAX") || model.equals("P052")) {
            Log.d("WapSdkFactory","== SDK6 selected ==");
            return new sdk6(wapPlugin);
        } else if (model.equals("WPOS-3") || model.equals("WPOS-QT")) {
            Log.d("WapSdkFactory","== SDK4 selected ==");
            return new sdk4(wapPlugin);
        } else {
            return null;
        }
    }

}