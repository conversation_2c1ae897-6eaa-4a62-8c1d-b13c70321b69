package si.lss.wap.sdk;

import android.content.Context;
import android.util.Log;
import android.os.RemoteException;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import com.getcapacitor.JSArray;
import com.getcapacitor.JSObject;

import java.io.ByteArrayInputStream;
import java.util.Base64;
import java.util.Iterator;
import java.util.Map;

import si.lss.wap.print.WapPrintPlugin;

import wangpos.sdk4.libbasebinder.BankCard;
import wangpos.sdk4.libbasebinder.Printer;
import wangpos.sdk4.libbasebinder.Printer.Align;

public class sdk4 extends WapSdk  {
    private WapPrintPlugin wapPlugin;
    private BankCard mBankCard;
    private Printer mPrinter;
    private JSObject printData;
    private Thread worker;
    private static boolean stopThread;

    public sdk4(WapPrintPlugin wapPlugin) {
        this.wapPlugin = wapPlugin;
    }

    @Override
    public String version() {
      return "WapSdk_4";
    }

    @Override
    public void initSdk(Context context) {
        Log.d(version(),"initSdk called..");
        Log.d(version(),"This SDK version does not have any specific initialization!");
    }

    @Override
    public boolean print(Context context, Map<String, String> data) throws Exception {
        Log.d(version(), "print...");

        JSArray printConfig = new JSArray(data.get("printConfig"));
        this.printData = new JSObject(data.get("printData"));
        int[] status = new int[1];
        int result = 0;

        this.mPrinter = new Printer(context);
        result = mPrinter.getPrinterStatus(status);
        Log.d("WapPrint",mPrinter.toString());

        if (result == 0) {
            mPrinter.printInit();
            mPrinter.clearPrintDataCache();

            for (int i = 0; i < printConfig.length(); i++) {
                printLine(new JSObject(printConfig.getString(i)));
            }

            mPrinter.printPaper(100);
            mPrinter.printFinish();
        }

        return true;
    }

    private void printLine(JSObject printItem) throws RemoteException {
      String type = printItem.getString("type");

      if (type.equalsIgnoreCase("text")) {
        String text = printItem.getString("value");
        Integer fontSize = printItem.getInteger("fontSize", 25);
        String align = printItem.getString("align", "LEFT");
        Boolean isBold = printItem.getBoolean("isBold", false);
        Boolean isItalic = printItem.getBoolean("isItalic", false);

        mPrinter.printString(setPlaceholders(text), fontSize, Align.valueOf(align), isBold, isItalic);
      } else if (type.equalsIgnoreCase("image")) {
        Integer width = printItem.getInteger("width", 100);
        Integer height = printItem.getInteger("height", 100);
        String align = printItem.getString("align", "LEFT");
        String imageBase64 = printItem.getString("value");
        imageBase64 = imageBase64.replace("[logo]", printData.getString("logo"));
        imageBase64 = imageBase64.replace("data:image/png;base64,", "");

        byte[] image = Base64.getDecoder().decode(imageBase64);

        Bitmap bitmap = BitmapFactory.decodeStream(new ByteArrayInputStream((image)));
        mPrinter.printImageBase(bitmap, width, height, Align.valueOf(align), 0);
        bitmap.recycle();
      } else if (type.equalsIgnoreCase("space")) {
        Integer marginSize = printItem.getInteger("marginSize", 100);
        mPrinter.printPaper(marginSize);
      }
    }

    private String setPlaceholders(String text) {
      Iterator<String> keys = printData.keys();
      while(keys.hasNext()) {
        String key = keys.next();
        String placeholder = '[' + key + ']';
        text = text.replace(placeholder, printData.getString(key));
      }

      return text;
    }

    @Override
    public String getDeviceSerial() {
        Log.d(version(), "getDeviceSerial called..");
        String serial = android.os.Build.SERIAL;
        if (serial == null) {
            serial = android.os.Build.getSerial();
        }

        return serial;
    }

    @Override
    public void initCardReader() {
        mBankCard = new BankCard(wapPlugin.getActivity().getApplicationContext());
    }

    @Override
    public void closeCardReader() throws Exception {
        stopThread = true;
        worker.interrupt();
        int result = mBankCard.openCloseCardReader(0x04, 0x02);
        mBankCard.breakOffCommand();
    }

    @Override
    public void readMagStripe(int timeoutLimit) {
        try {
            mBankCard.breakOffCommand();
            mBankCard.openCloseCardReader(0x04, 0x01);

            stopThread = false;

            worker = new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        byte[] cardData = new byte[256];
                        int[] length = new int[1];

                        int result = -1;
                        int timeout = 0;
                        while (result != 0 && ++timeout < timeoutLimit && !stopThread) {
                            result = mBankCard.cardReaderDetact(0x00, 0x04, 0x04, cardData, length, null);
                            Thread.sleep(1000);
                        }

                        stopThread = true;

                        byte[] mag1 = new byte[256];
                        byte[] mag2 = new byte[256];
                        byte[] mag3 = new byte[256];
                        int[] magLen1 = new int[1];
                        int[] magLen2 = new int[1];
                        int[] magLen3 = new int[1];

                        parseMagneticOverride(cardData, length[0], mag1, magLen1, mag2, magLen2, mag3, magLen3);

                        JSObject magResult = new JSObject();
                        magResult.put("track1", new String(mag1).substring(0,magLen1[0]));
                        magResult.put("track2", new String(mag2).substring(0,magLen2[0]));
                        magResult.put("track3", new String(mag3).substring(0,magLen3[0]));
                        Log.d(version(), "cardReaderDetact: " + magResult.toString());

                        mBankCard.openCloseCardReader(0x04, 0x02);

                        wapPlugin.notifyListeners("readMagStripe", magResult);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            });
            worker.start();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public int parseMagneticOverride(byte[] data, int length, byte[] dataOutStr1, int[] lenStr1, byte[] dataOutStr2, int[] lenStr2, byte[] dataOutStr3, int[] lenStr3) {
        if (length <= 2) {
            return -1;
        } else {
            int pos = 0;
            lenStr1[0] = data[pos];
            ++pos;
            lenStr2[0] = data[pos];
            ++pos;
            lenStr3[0] = data[pos];
            ++pos;
            int i;
            if (lenStr1[0] > 0) {
                for(i = 0; i < lenStr1[0]; ++i) {
                dataOutStr1[i] = data[pos + i];
                }
            }

            pos += lenStr1[0];
            if (lenStr2[0] > 0) {
                for(i = 0; i < lenStr2[0]; ++i) {
                dataOutStr2[i] = data[pos + i];
                }
            }

            pos += lenStr2[0];
            if (lenStr3[0] > 0) {
                for(i = 0; i < lenStr3[0]; ++i) {
                dataOutStr3[i] = data[pos + i];
                }
            }

            int var10000 = pos + lenStr3[0];
            return 0;
        }
    }
}
