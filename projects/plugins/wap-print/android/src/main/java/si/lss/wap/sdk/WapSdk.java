package si.lss.wap.sdk;

import android.content.Context;

import com.getcapacitor.JSObject;

import java.util.Map;

public abstract class WapSdk {
    public abstract String version();
    public abstract void initSdk(Context context);
    public abstract boolean print(Context context, Map<String, String> data) throws Exception;
    public abstract String getDeviceSerial();
    public abstract void initCardReader();
    public abstract void closeCardReader() throws Exception;
    public abstract void readMagStripe(int timeoutLimit);
}
