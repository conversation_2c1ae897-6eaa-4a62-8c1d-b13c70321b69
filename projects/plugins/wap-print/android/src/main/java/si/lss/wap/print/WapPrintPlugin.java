package si.lss.wap.print;

import android.graphics.Paint;
import android.os.RemoteException;
import android.content.Context;

import com.getcapacitor.JSArray;
import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import si.lss.wap.sdk.WapSdk;
import si.lss.wap.sdk.WapSdkFactory;

import java.io.ByteArrayInputStream;
import java.util.Base64;
import java.util.Iterator;
import java.util.HashMap;
import java.util.Map;

import android.provider.Settings;
import android.provider.Settings.System;
import android.util.Log;


@CapacitorPlugin(name = "WapPrintPlugin")
public class WapPrintPlugin extends Plugin {
    private WapSdk sdk;
    private Thread worker;
    private static boolean stopThread;

    /**
     * Initializes the device SDK - this must always be called on app launch
     */
    @PluginMethod
    public void initSDK(PluginCall call) {
      this.sdk = WapSdkFactory.getSdk(this);
      sdk.initSdk(this.getContext());
      call.resolve(null);
    }

    /**
     * Activates the device printer for the given printing configuration
     */
    @PluginMethod
    public void print(PluginCall call) {
      Map<String, String> printData = new HashMap<>();
      printData.put("printConfig", call.getString("printConfig"));
      printData.put("printData", call.getString("printData"));

      JSObject returnData = new JSObject();
      try {
          this.sdk.print(this.getContext(), printData);
          returnData.put("result", true);
      } catch (Exception ex) {
          ex.printStackTrace();
          returnData.put("result", false);
          returnData.put("error", ex.getMessage());
      }

      call.resolve(returnData);
    }

    /**
   * Returns the unique serial number for the physical device
   */
  @PluginMethod
  public void getDeviceSerial(PluginCall call) {
    try {
      JSObject returnData = new JSObject();
      returnData.put("serial", this.sdk.getDeviceSerial());
      call.resolve(returnData);
    } catch (Exception ex) {
      ex.printStackTrace();
    }
  }

  @PluginMethod
  public void initCardReader(PluginCall call) {
    try {
      this.sdk.initCardReader();
    } catch (Exception ex) {
      ex.printStackTrace();
    }
    call.resolve(null);
  }

  @PluginMethod
  public void closeCardReader(PluginCall call) {
    try {
      this.sdk.closeCardReader();
    } catch (Exception ex) {
      ex.printStackTrace();
    }
    call.resolve(null);
  }

  @PluginMethod
  public void readMagStripe(PluginCall call) {
    int timeoutLimit = call.getInt("timeout");
    this.sdk.readMagStripe(timeoutLimit);
    call.resolve(null);
  }

}
