import { WebPlugin } from '@capacitor/core';

import type { WapPrintPlugin } from './definitions';

export class WapPrintPluginWeb extends WebPlugin implements WapPrintPlugin {
  async initSDK(): Promise<void> {
    return;
  }

  async print(options: {
    printConfig: string;
    printData: string;
    result: boolean;
  }): Promise<{ result: boolean }> {
    console.log('PRINT: ', options);
    console.log('Printing not allowed on WEB');
    return options;
  }

  async getDeviceSerial(options: {
    serial: string;
  }): Promise<{ serial: string }> {
    return options;
  }

  async initCardReader(): Promise<void> {
    return;
  }

  async closeCardReader(): Promise<void> {
    return;
  }

  async readMagStripe(options: { timeout?: number }): Promise<void> {
    if (options) {
      return;
    }
    return;
  }
}
