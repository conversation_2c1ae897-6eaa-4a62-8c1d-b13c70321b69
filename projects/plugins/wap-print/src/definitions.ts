import type { PluginListenerHandle } from '@capacitor/core';

export interface WapPrintPlugin {
  initSDK(): Promise<void>;
  print(options: {
    printConfig: string;
    printData: string;
    result: boolean;
  }): Promise<{ result: boolean }>;
  getDeviceSerial(options: { serial: string }): Promise<{ serial: string }>;
  getDeviceSerial(options: { serial: string }): Promise<{ serial: string }>;
  initCardReader(): Promise<void>;
  closeCardReader(): Promise<void>;
  readMagStripe(options: { timeout?: number }): Promise<void>;
  addListener(
    eventName: string,
    listenerFunc: (result: any) => void,
  ): Promise<PluginListenerHandle> & PluginListenerHandle;
  removeAllListeners(): Promise<void>;
}
