:host {
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: -4px;

    .ion-intl-tel-input-code{
        position: relative;
        /*min-height: calc(var(--min-height) - 8px);*/
    }

    .ion-intl-tel-input-number {
        flex: 1;
    }

    .ionic-selectable {
        &-label-default,
        &-label-fixed {
            max-width: 100%;
        }

        /* Keep an eye out for depreciated ::ng-deep replacement*/
        &:not(&-label-stacked):not(&-label-floating) {
            ::ng-deep {
                .ionic-selectable-inner {
                    .ionic-selectable-value {
                        padding-top: 10px;
                        padding-bottom: 10px;
                    }
                }

                .ionic-selectable-icon {
                    &-inner {
                        top: 17px;
                    }
                }
            }

        }
    }

    .fi {
        margin-right: 5px;
    }
}
