import { Component, Input, On<PERSON><PERSON>roy, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'lib-error-boundary',
  standalone: true,
  imports: [CommonModule],
  template: `
    <ng-container *ngIf="!hasError; else errorTemplate">
      <ng-content></ng-content>
    </ng-container>
    <ng-template #errorTemplate>
      <div class="error-boundary p-4 border border-red-300 bg-red-50 rounded-md">
        <div class="font-semibold text-red-700 mb-2">Component Error</div>
        <div class="text-sm text-red-600">
          {{ errorMessage || 'An error occurred while rendering this component.' }}
        </div>
        <button 
          *ngIf="canRetry"
          (click)="reset()" 
          class="mt-3 px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700"
        >
          Try Again
        </button>
      </div>
    </ng-template>
  `,
  styles: []
})
export class ErrorBoundaryComponent implements OnDestroy {
  @Input() componentName = 'Component';
  @Input() canRetry = true;
  
  hasError = false;
  errorMessage = '';
  
  private destroy$ = new Subject<void>();
  
  constructor(private errorHandler: ErrorHandler) {}
  
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  
  reset(): void {
    this.hasError = false;
    this.errorMessage = '';
  }
  
  handleError(error: Error): void {
    this.hasError = true;
    this.errorMessage = error.message;
    this.errorHandler.handleError(error);
  }
}
