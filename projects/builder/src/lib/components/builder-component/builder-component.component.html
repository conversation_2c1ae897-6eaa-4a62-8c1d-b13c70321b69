<div *ngIf="component"
     [ngClass]="getComponentClasses()"
     cdkDrag
     [cdkDragDisabled]="isPreview"
     (cdkDragStarted)="onDragStarted($event)"
     (cdkDragEnded)="onDragEnded($event)"
     (click)="onSelect($event)">

  <!-- Selection overlay - only shown when component is selected and not in preview -->
  <div class="builder-component__selection-overlay" *ngIf="isSelected && !isPreview">
    <!-- Primary selection indicator with component type label -->
    <div class="builder-component__type-label" *ngIf="isPrimarySelected">
      <span>{{component.type}}</span>
    </div>
    
    <!-- Quick action buttons -->
    <div class="builder-component__actions">
      <button class="builder-component__action-btn" 
              title="Delete component"
              (click)="onDelete($event)">
        <i class="icon icon-trash"></i>
      </button>
      <button class="builder-component__action-btn" 
              title="Duplicate component"
              (click)="onDuplicate($event)">
        <i class="icon icon-copy"></i>
      </button>
      <button class="builder-component__action-btn" 
              title="Move Up"
              (click)="moveUp($event)">
        <i class="icon icon-arrow-up"></i>
      </button>
      <button class="builder-component__action-btn" 
              title="Move Down"
              (click)="moveDown($event)">
        <i class="icon icon-arrow-down"></i>
      </button>
    </div>
  </div>

  <!-- Resize handles - only shown for primary selected resizable components -->
  <ng-container *ngIf="showResizeHandles()">
    <div class="builder-component__resize-handle builder-component__resize-handle--n"></div>
    <div class="builder-component__resize-handle builder-component__resize-handle--ne"></div>
    <div class="builder-component__resize-handle builder-component__resize-handle--e"></div>
    <div class="builder-component__resize-handle builder-component__resize-handle--se"></div>
    <div class="builder-component__resize-handle builder-component__resize-handle--s"></div>
    <div class="builder-component__resize-handle builder-component__resize-handle--sw"></div>
    <div class="builder-component__resize-handle builder-component__resize-handle--w"></div>
    <div class="builder-component__resize-handle builder-component__resize-handle--nw"></div>
  </ng-container>

  <!-- Drag handle -->
  <div *ngIf="!isPreview" 
       class="builder-component__handle" 
       cdkDragHandle>
    <i class="icon icon-drag"></i>
  </div>

  <!-- Drop indicators -->
  <div class="drop-indicator drop-indicator--top"
       [class.drop-indicator--visible]="dropPosition === 'top'"></div>

  <div class="drop-indicator drop-indicator--bottom"
       [class.drop-indicator--visible]="dropPosition === 'bottom'"></div>
  
  <!-- Component content -->
  <div class="builder-component__content"
       [attr.data-component-id]="component.id"
       [attr.data-component-type]="component.type"
       cdkDropList
       [cdkDropListDisabled]="isPreview || !canAcceptDrop()"
       (cdkDropListDropped)="onDrop($event)"
       (cdkDropListEntered)="onDragEnter($event)"
       (cdkDropListExited)="onDragExit($event)">

    <!-- Component content rendering logic -->
    <ng-container *ngComponentOutlet="getDynamicComponent(); injector: getInjector()"></ng-container>

    <!-- Placeholder for empty containers -->
    <div *ngIf="canAcceptDrop() && (!component.children || component.children.length === 0)"
         class="builder-component__placeholder"
         [class.builder-component__placeholder--active]="dropPosition === 'inside'">
      <span>Drop components here</span>
    </div>

    <!-- Child components -->
    <ng-container *ngIf="component.children && component.children.length > 0">
      <lib-builder-component *ngFor="let child of component.children"
                           [component]="child"
                           [isPreview]="isPreview"
                           [parentId]="component.id"
                           (componentUpdate)="componentUpdate.emit()">
      </lib-builder-component>
    </ng-container>
  </div>
</div>
