import { 
  Component, Input, ViewChild, ElementRef, OnInit, OnDestroy, 
  ChangeDetectionStrategy, ChangeDetectorRef, AfterViewInit,
  Renderer2, ViewContainerRef, ComponentRef, OnChanges, SimpleChanges,
  Injector, EventEmitter, Output
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { 
  CdkDrag, CdkDragStart, CdkDragMove, CdkDragEnd, 
  CdkDropList, CdkDragDrop, DragDropModule, CdkDragHandle,
  CdkDragEnter
} from '@angular/cdk/drag-drop';
import { take, takeUntil, filter, tap } from 'rxjs/operators';
import { Subscription, Subject, firstValueFrom, combineLatest, async, from } from 'rxjs';

import { BuilderComponent } from '../../interfaces/component.interface';
import { SelectionService } from '../../services/selection.service';
import { ComponentRegistryService, COMPONENT_DATA, IS_PREVIEW_MODE } from '../../services/component-registry.service';
import { ConfigService } from '../../services/config.service';
import { DragFeedbackService } from '../../services/drag-feedback.service';
import { DropListService } from '../../services/drop-list.service';
import { PlatformService } from '../../services/platform.service';
import { at, has, method } from 'lodash';

@Component({
  selector: 'lib-builder-component',
  standalone: true,
  imports: [CommonModule, DragDropModule],
  templateUrl: './builder-component.component.html',
  styleUrls: ['./builder-component.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BuilderComponentComponent implements OnInit, OnChanges, AfterViewInit, OnDestroy {
  @Input() component!: BuilderComponent;
  @Input() parentId: string | null = null;
  @Input() isPreview: boolean = false;
  @Input() isSelected: boolean = false;
  @Input() isPrimarySelected: boolean = false;
  @Input() level: number = 0;
  @Input() index: number = 0;
  
  @ViewChild('componentElement') componentElementRef!: ElementRef;
  @ViewChild('childrenContainer', { read: ElementRef }) childrenContainerRef!: ElementRef;
  @ViewChild('dropList') dropListRef!: CdkDropList;
  @ViewChild('dragRef') dragRef!: CdkDrag;
  @ViewChild('previewTemplate', { read: ElementRef }) previewTemplateRef!: ElementRef;
  
  @Output() componentUpdate = new EventEmitter<void>();
  
  // Dynamic component reference
  componentInstance: any;
  componentRef: ComponentRef<any> | null = null;
  
  // Drag and drop
  isFocused: boolean = false;
  dragStarted: boolean = false;
  dragPosition = { x: 0, y: 0 };
  dropListId: string = '';
  connectedLists: string[] = [];
  childrenIds: string[] = [];
  
  // Component registry data
  componentData: any = null;
  private dragSubscription = new Subscription();
  private destroy$ = new Subject<void>();
  
  // UI State
  dropPosition: 'top' | 'bottom' | 'inside' | null = null;
  
  constructor(
    private selectionService: SelectionService,
    private configService: ConfigService,
    private componentRegistry: ComponentRegistryService,
    private viewContainerRef: ViewContainerRef,
    private renderer: Renderer2,
    private dragFeedbackService: DragFeedbackService,
    private dropListService: DropListService,
    private platformService: PlatformService,
    private cd: ChangeDetectorRef
  ) {}
  
  ngOnInit(): void {
    // Setup dropdown list ID and register it with service
    this.dropListId = `component-${this.component.id}`;
    
    // Register as container if has children
    if (this.canAcceptChildren()) {
      this.dropListService.registerContainer(this.dropListId);
    }
    
    // Subscribe to selection changes
    this.selectionService.selectedComponents$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        const isSelected = this.selectionService.isSelected(this.component.id);
        const isPrimarySelected = this.selectionService.isPrimarySelected(this.component.id);
        
        if (this.isSelected !== isSelected || this.isPrimarySelected !== isPrimarySelected) {
          this.isSelected = isSelected;
          this.isPrimarySelected = isPrimarySelected;
          this.cd.markForCheck();
        }
      });
      
    // Setup connected lists
    this.dropListService.containerIds$
      .pipe(takeUntil(this.destroy$))
      .subscribe(ids => {
        if (this.canAcceptChildren()) {
          this.connectedLists = ids.filter(id => id !== this.dropListId);
          this.cd.markForCheck();
        }
      });
  }
  
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['component'] && !changes['component'].firstChange) {
      this.updateComponentData();
      
      // Update child IDs when component children change
      if (this.component.children) {
        this.childrenIds = this.component.children.map(child => child.id);
      } else {
        this.childrenIds = [];
      }
      
      this.cd.markForCheck();
    }
  }
  
  ngAfterViewInit(): void {
    // Register the drop list after view init
    if (this.canAcceptChildren() && this.dropListRef) {
      this.dropListService.registerContainer(this.dropListId);
    }
    
    // Load dynamic component if needed
    if (this.component && !this.isPreview) {
      this.loadDynamicComponent();
    }
    
    // Track child component IDs
    if (this.component.children) {
      this.childrenIds = this.component.children.map(child => child.id);
    }
    
    // Setup custom drag preview
    if (this.dragRef && this.previewTemplateRef && !this.isPreview) {
      this.setupDragPreview();
    }

    // Add platform-specific setup
    this.setupPlatformSpecificBehavior();
  }
  
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.dragSubscription.unsubscribe();
    
    // Unregister from drop list service
    if (this.canAcceptChildren()) {
      this.dropListService.unregisterContainer(this.dropListId);
    }
    
    // Clean up component reference
    if (this.componentRef) {
      this.componentRef.destroy();
    }
  }
  
  onDragStarted(event: CdkDragStart): void {
    if (this.isPreview) return;
    
    this.dragStarted = true;
    
    // Ensure the component is selected when dragging starts
    if (!this.isSelected) {
      this.selectComponent(null);
    }
    
    // Enable drag feedback
    this.dragFeedbackService.startDrag({
      id: this.component.id,
      componentName: this.component.name || this.component.type,
      type: this.component.type,
      preview: this.previewTemplateRef?.nativeElement.innerHTML || ''
    });
  }ally to reduce performance impact
  > 50) {
  onDragMoved(event: CdkDragMove): void {   this.dragFeedbackService.updatePosition({
    if (this.isPreview) return;      x: event.pointerPosition.x,
    
    // Update drag feedback position
    this.dragFeedbackService.updatePosition(event.pointerPosition);  this._lastDragUpdate = Date.now();
  }
  
  onDragEnded(event: CdkDragEnd): void {
    if (this.isPreview) return;DragEnd): void {
    
    this.dragStarted = false;
    this.dragPosition = { x: 0, y: 0 }; this.dragStarted = false;
      this.dragPosition = { x: 0, y: 0 };
    // End drag feedback
    this.dragFeedbackService.endDrag();
    this.cd.markForCheck();this.dragFeedbackService.endDrag();
  }();
  
  onDrop(event: CdkDragDrop<BuilderComponent[]>): void {
    if (this.isPreview) return;uilderComponent[]>): void {
    if (this.isPreview) return;
    // Handle drop event
    const droppedItem = event.item.data;
    
    if (!droppedItem) return;
    
    // Add component as child
    if (this.canAcceptChildren()) {
      const children = [...(this.component.children || [])];
      nst children = [...(this.component.children || [])];
      // If dropped from another container
      if (event.previousContainer !== event.container) {
        // Create a new component when dragged from palette
        const isPaletteItem = event.previousContainer.id === 'componentPalette'; palette
        PaletteItem = event.previousContainer.id === 'componentPalette';
        if (isPaletteItem) {
          // The component will already have a generated ID from the palette
          children.splice(event.currentIndex, 0, droppedItem);erated ID from the palette
          droppedItem.parentId = this.component.id;children.splice(event.currentIndex, 0, droppedItem);
        } else {ent.id;
          // Move from another container
          const sourceId = event.previousContainer.id;// Move from another container
          const sourceIndex = event.previousIndex;eviousContainer.id;
          
          // Clone the component being moved
          const movedComponent = { ...droppedItem, parentId: this.component.id };ing moved
          this.component.id };
          // Insert at new position 
          children.splice(event.currentIndex, 0, movedComponent);nsert at new position
          movedComponent);
          // Remove from old parent
          this.configService.removeComponent(droppedItem.id);
        }tem.id);
      } else { }
        // Just reorder within the same container} else {
        const item = children[event.previousIndex];tainer
        children.splice(event.previousIndex, 1);ent.previousIndex];
        children.splice(event.currentIndex, 0, item);ent.previousIndex, 1);
      }.splice(event.currentIndex, 0, item);
      
      // Update component with new children
      const updatedComponent = {
        ...this.component, const updatedComponent = {
        children    ...this.component,
      };
         };
      this.configService.updateComponent(updatedComponent);    
    } this.configService.updateComponent(updatedComponent);
    
    this.cd.markForCheck();
  }
  
  /**
   * Check if this component can accept child components
   */component can accept child components
  canAcceptChildren(): boolean {
    // Container types that can have children(): boolean {
    const containerTypes = [types that can have children
      'base-container', nst containerTypes = [
      'base-card',   'base-container', 
      'base-grid',
      'base-stack',   'base-grid',
      'base-form'    'base-stack',
    ]; 'base-form'
    
    return containerTypes.includes(this.component.type);
  }component.type);
  
  /**
   * Load dynamic component based on component type
   */
  private loadDynamicComponent(): void {/
    if (!this.component?.type) return;): void {
    if (!this.component?.type) return;
    // Get component class from registry
    const componentType = this.componentRegistry.getComponentByType(this.component.type);
    Registry.getComponentByType(this.component.type);
    if (!componentType) return;
    
    try {
      // Create injector with component data
      const injector = Injector.create({
        providers: [st injector = Injector.create({
          { provide: COMPONENT_DATA, useValue: this.component },  providers: [
          { provide: IS_PREVIEW_MODE, useValue: this.isPreview }PONENT_DATA, useValue: this.component },
        ],, useValue: this.isPreview }
        parent: this.viewContainerRef.injector  ],
      });ContainerRef.injector
      
      // Clear container
      this.viewContainerRef.clear();Clear container
      this.viewContainerRef.clear();
      // Create component
      this.componentRef = this.viewContainerRef.createComponent(componentType, {
        injectorthis.componentRef = this.viewContainerRef.createComponent(componentType, {
      });
      
      this.componentInstance = this.componentRef.instance;
      this.componentData = this.component;this.componentInstance = this.componentRef.instance;
      ata = this.component;
      // Detect changes
      this.componentRef.changeDetectorRef.detectChanges(); // Detect changes
      this.cd.markForCheck();   this.componentRef.changeDetectorRef.detectChanges();
          this.cd.markForCheck();
    } catch (error) { 
      console.error(`Failed to load component type: ${this.component.type}`, error);
    } console.error(`Failed to load component type: ${this.component.type}`, error);
  }
  
  /**
   * Update component data when changed
   */
  private updateComponentData(): void {
    if (this.componentRef) {a(): void {
      const instance = this.componentRef.instance;
      st instance = this.componentRef.instance;
      if (instance && this.component) {
        // Update component data if instance has data property
        if (instance.data) {
          instance.data = { ...this.component };
        }s.component };
        
        // Update instance properties from component
        Object.entries(this.component).forEach(([key, value]) => {// Update instance properties from component
          if (instance.hasOwnProperty(key)) {) => {
            instance[key] = value;   if (instance.hasOwnProperty(key)) {
          }       instance[key] = value;
        });       }
              });
        this.componentRef.changeDetectorRef.detectChanges();   
      }geDetectorRef.detectChanges();
    } }
  }
  
  /**
   * Setup custom drag preview
   */ Setup custom drag preview
  private setupDragPreview(): void {
    // We need to import DragRef and access the CdkDrag's drag property
    // to set a custom drag preview// We need to import DragRef and access the CdkDrag's drag property
    // Since setDragPreview is not available on dragRef directly, we need to modify our approach
     our approach
    // Get the drag ref's underlying drag object
    const dragRefObject = this.dragRef['_dragRef'];lying drag object
    
    // Check if the underlying drag object exists and has the setDragPreview method
    if (dragRefObject && typeof dragRefObject.withPreviewTemplate === 'function') { underlying drag object exists and has the setDragPreview method
      // Create a proper DragPreviewTemplatedragRefObject && typeof dragRefObject.withPreviewTemplate === 'function') {
      const previewTemplate = {
        template: this.previewTemplateRef.nativeElement, const previewTemplate = {
        viewContainer: this.viewContainerRef,     template: this.previewTemplateRef.nativeElement,
        context: {}      viewContainer: this.viewContainerRef,
      };   context: {}
      dragRefObject.withPreviewTemplate(previewTemplate);
    } dragRefObject.withPreviewTemplate(previewTemplate);
  }
  
  /**
   * Select this component
   */
  selectComponent(event: MouseEvent | null): void {
    if (event) {ctComponent(event: MouseEvent | null): void {
      event.stopPropagation();
      stopPropagation();
      // Check if shift key was pressed for multi-select
      const isShiftKey = event.shiftKey; // Check if shift key was pressed for multi-select
         const isShiftKey = event.shiftKey;
      this.selectionService.toggleSelection(this.component.id, isShiftKey);    
    } else { this.selectionService.toggleSelection(this.component.id, isShiftKey);
      this.selectionService.toggleSelection(this.component.id, true);
    } this.selectionService.toggleSelection(this.component.id, true);
  }
  
  /**
   * Delete this component
   */ Delete this component
  deleteComponent(event: MouseEvent): void {
    if (event) {eleteComponent(event: MouseEvent): void {
      event.stopPropagation();  if (event) {
    } event.stopPropagation();
    
    this.configService.removeComponent(this.component.id);
  }mponent.id);
  
  /**
   * Get drag feedback state
   */
  async getDragFeedbackState(): Promise<any> {
    try {nc getDragFeedbackState(): Promise<any> {
      return await firstValueFrom(this.dragFeedbackService.dragState$); try {
    } catch (error) {      return await firstValueFrom(this.dragFeedbackService.dragState$);
      console.error('Error getting drag feedback state:', error);
      return null;tate:', error);
    } null;
  }

  // Add this property to track last update time
  private _lastDragUpdate = 0;

  getComponentClasses(): Record<string, boolean> {
    return {
      'builder-component': true,
      'is-selected': this.this.isSelected,
      'is-primary-selected': this.this.isPrimarySelected,
      'is-preview': this.this.isPreview,
      'can-accept-children': this.this.canAcceptChildren()
    };
  }

  onDragEnded(event: any): void {

  }

  onSelect(event: any): void {

  }

  onDelete(event: any): void {

  }

  onDuplicate(event: any): void {

  }

  moveUp(event: any): void {

  }

  moveDown(event: any): void {

  }

  showResizeHandles(): boolean {
    return false;
  }

  canAcceptDrop(): boolean {
    return false;
  }

  onDrop(event: any): void {

  }

  onDragEnter(event: any): void {

  }

  onDragExit(event: any): void {

  }

  getDynamicComponent(): any {
    return null;
  }

  getInjector(): Injector {
    return Injector.create({
      providers: []
    });
  }
}
