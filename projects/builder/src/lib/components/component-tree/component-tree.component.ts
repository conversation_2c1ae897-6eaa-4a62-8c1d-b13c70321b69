import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy, ChangeDetectorRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { BuilderComponent } from '../../interfaces/component.interface';
import { ConfigService } from '../../services/config.service';
import { SelectionService } from '../../services/selection.service';
import { SubscriptionManager } from '../../utils/subscription-manager';
import { Subject } from 'rxjs';
import { ScrollingModule, CdkVirtualScrollViewport } from '@angular/cdk/scrolling';

// Interface for node toggle event
interface NodeToggleEvent {
  node: BuilderComponent;
  expanded: boolean;
}

// Interface for node move event
interface NodeMoveEvent {
  node: BuilderComponent;
  direction: 'up' | 'down';
  parentNode: BuilderComponent | null;
  index: number;
}

interface FlatTreeNode {
  id: string;
  name: string;
  type: string;
  level: number;
  isExpanded: boolean;
  isVisible: boolean;
  hasChildren: boolean;
  component: BuilderComponent;
}

@Component({
  selector: 'lib-component-tree',
  standalone: true,
  imports: [
    CommonModule, 
    FormsModule, 
    ScrollingModule
  ],
  template: `
    <div class="component-tree">
      <div class="component-tree__header">Component Tree</div>
      <div class="component-tree__search">
        <input 
          type="text" 
          placeholder="Search components..." 
          class="component-tree__search-input"
          (input)="filterComponents($event)"
        />
      </div>
      <cdk-virtual-scroll-viewport 
        class="component-tree__list" 
        [itemSize]="32"
        [minBufferPx]="200"
        [maxBufferPx]="400">
        <div 
          *cdkVirtualFor="let node of flattenedNodes" 
          class="component-tree__item"
          [class.component-tree__item--selected]="node.id === selectedNodeId"
          [class.component-tree__item--hidden]="!node.isVisible"
          [style.padding-left.px]="node.level * 20"
          (click)="selectNode(node)">
          <span 
            class="component-tree__toggle"
            *ngIf="node.hasChildren"
            (click)="toggleNode($event, node)">
            {{ node.isExpanded ? '▼' : '►' }}
          </span>
          <span class="component-tree__type-icon" [attr.data-type]="node.type"></span>
          <span class="component-tree__name">{{ node.name || node.type }}</span>
        </div>
      </cdk-virtual-scroll-viewport>
    </div>
  `,
  styleUrls: ['./component-tree.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ComponentTreeComponent extends SubscriptionManager implements OnInit, OnDestroy {
  rootComponents: BuilderComponent[] = [];
  filteredComponents: BuilderComponent[] = [];
  expandedNodes = new Set<string>();
  searchTerm: string = '';
  isLoading: boolean = true;
  
  private searchSubject = new Subject<string>();
  
  @ViewChild(CdkVirtualScrollViewport) viewport!: CdkVirtualScrollViewport;
  
  flattenedNodes: FlatTreeNode[] = [];
  selectedNodeId: string | null = null;
  
  private filterText = '';
  
  constructor(
    private configService: ConfigService,
    private selectionService: SelectionService,
    private cd: ChangeDetectorRef
  ) {
    super();
  }
  
  ngOnInit(): void {
    // Subscribe to component changes
    this.configService.rootComponents$
      .pipe(takeUntil(this.destroy$))
      .subscribe((components: BuilderComponent[]) => {
        this.rootComponents = components || [];
        this.applyFilters();
        this.isLoading = false;
        this.cd.markForCheck();
      });
      
    // Initialize search debounce
    this.searchSubject
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe(term => {
        this.searchTerm = term;
        this.applyFilters();
        this.cd.markForCheck();
      });

    this.configService.selectedPage$
      .pipe(takeUntil(this.destroy$))
      .subscribe(page => {
        if (page?.components) {
          this.rootComponents = page.components;
          this.flattenTree();
        } else {
          this.rootComponents = [];
          this.flattenedNodes = [];
        }
      });
      
    // Fix: Subscribe to primarySelectedId$ observable instead of using non-existent method
    this.selectionService.primarySelectedId$
      .pipe(takeUntil(this.destroy$))
      .subscribe(primaryId => {
        if (primaryId && primaryId !== this.selectedNodeId) {
          this.selectedNodeId = primaryId;
          this.scrollToSelectedNode();
        }
      });
  }
  
  override ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  
  onSearch(term: string): void {
    this.searchSubject.next(term);
  }
  
  clearSearch(): void {
    this.searchTerm = '';
    this.applyFilters();
  }
  
  // Updated method signature to match the emitted event type
  onNodeSelected(node: BuilderComponent): void {
    // Selection is handled by the tree-node component through the SelectionService
    console.log('Node selected:', node.id);
  }
  
  // Updated method signature to match the emitted event type
  onNodeToggled(data: NodeToggleEvent): void {
    if (data.expanded) {
      this.expandedNodes.add(data.node.id);
    } else {
      this.expandedNodes.delete(data.node.id);
    }
  }
  
  // Updated method signature to match the emitted event type
  onNodeDeleted(node: BuilderComponent): void {
    this.configService.deleteComponent(node.id);
  }
  
  // Updated method signature to match the emitted event type
  onNodeDuplicated(node: BuilderComponent): void {
    // Clone the component
    const clonedNode = this.cloneComponent(node);
    clonedNode.id = `${node.type}-${Date.now()}`;
    clonedNode.name = `${node.name || node.type} (Copy)`;
    
    // Add the cloned component to the same parent
    if (node.parentId) {
      this.configService.addChildComponent(node.parentId, clonedNode);
    } else {
      this.configService.addRootComponent(clonedNode);
    }
  }
  
  // Updated method signature to match the emitted event type
  onNodeMoved(data: NodeMoveEvent): void {
    const { node, direction, parentNode, index } = data;
    const targetIndex = direction === 'up' ? index - 1 : index + 1;
    
    // Implement move using available ConfigService methods
    this.configService.reorderComponent(node.id, parentNode?.id || null, targetIndex);
  }
  
  // Helper method to deep clone a component
  private cloneComponent(component: BuilderComponent): BuilderComponent {
    const cloned = { ...component };
    
    // Clone children if they exist
    if (component.children && component.children.length) {
      cloned.children = component.children.map(child => this.cloneComponent(child));
    }
    
    return cloned;
  }
  
  private applyFilters(): void {
    if (!this.searchTerm) {
      this.filteredComponents = [...this.rootComponents];
      return;
    }
    
    const searchTermLower = this.searchTerm.toLowerCase();
    
    // Helper function to recursively search through component tree
    const matchesSearch = (component: BuilderComponent): boolean => {
      // Match by ID, name, or type
      if (component.id?.toLowerCase().includes(searchTermLower) ||
          component.name?.toLowerCase().includes(searchTermLower) ||
          component.type?.toLowerCase().includes(searchTermLower)) {
        return true;
      }
      
      // Search in children
      if (component.children && component.children.length) {
        return component.children.some(child => matchesSearch(child));
      }
      
      return false;
    };
    
    // Create a deep copy of components that match search term
    const filterComponents = (components: BuilderComponent[]): BuilderComponent[] => {
      return components.filter(component => {
        const matches = matchesSearch(component);
        
        if (matches && component.children?.length) {
          // Make sure we expand nodes that contain matching children
          this.expandedNodes.add(component.id);
          
          // Filter children recursively
          const filteredChildren = filterComponents(component.children);
          
          // Create a new component with filtered children
          return {
            ...component,
            children: filteredChildren
          };
        }
        
        return matches;
      });
    };
    
    this.filteredComponents = filterComponents(this.rootComponents);
  }

  selectNode(node: FlatTreeNode): void {
    this.selectionService.selectComponent(node.id);
  }
  
  toggleNode(event: MouseEvent, node: FlatTreeNode): void {
    event.stopPropagation();
    node.isExpanded = !node.isExpanded;
    this.flattenTree();
  }
  
  filterComponents(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.filterText = target.value.toLowerCase();
    this.flattenTree();
  }
  
  private flattenTree(): void {
    this.flattenedNodes = [];
    this.flattenNodes(this.rootComponents, 0);
    
    if (this.filterText) {
      // Mark nodes that match the filter as visible
      const visibleIds = new Set<string>();
      
      // First pass: find matching nodes
      this.flattenedNodes.forEach(node => {
        if (node.name.toLowerCase().includes(this.filterText) || 
            node.type.toLowerCase().includes(this.filterText)) {
          visibleIds.add(node.id);
          
          // Also add all parent nodes to keep tree structure
          let parent = this.findParentNode(node.id);
          while (parent) {
            visibleIds.add(parent.id);
            parent.isExpanded = true;
            parent = this.findParentNode(parent.id);
          }
        }
      });
      
      // Update visibility based on filter
      this.flattenedNodes.forEach(node => {
        node.isVisible = visibleIds.has(node.id);
      });
    } else {
      // No filter, all nodes are visible based on expansion state
      this.updateVisibility();
    }
  }
  
  private flattenNodes(components: BuilderComponent[], level: number): void {
    if (!Array.isArray(components)) return;
    
    components.forEach(component => {
      const node: FlatTreeNode = {
        id: component.id,
        name: component.name || component.type,
        type: component.type,
        level: level,
        isExpanded: true, // Default expanded
        isVisible: true,
        hasChildren: Array.isArray(component.children) && component.children.length > 0,
        component: component
      };
      
      this.flattenedNodes.push(node);
      
      if (node.hasChildren && node.isExpanded) {
        this.flattenNodes(component.children!, level + 1);
      }
    });
  }
  
  private updateVisibility(): void {
    // Reset all to visible
    this.flattenedNodes.forEach(node => node.isVisible = true);
    
    // Hide children of collapsed nodes
    this.flattenedNodes.forEach(node => {
      if (!node.isExpanded && node.hasChildren) {
        this.hideChildrenOf(node.id);
      }
    });
  }
  
  private hideChildrenOf(nodeId: string): void {
    const nodeIndex = this.flattenedNodes.findIndex(n => n.id === nodeId);
    if (nodeIndex === -1) return;
    
    const nodeLevel = this.flattenedNodes[nodeIndex].level;
    
    // Hide all nodes with greater level until we hit another node at same or lower level
    for (let i = nodeIndex + 1; i < this.flattenedNodes.length; i++) {
      if (this.flattenedNodes[i].level > nodeLevel) {
        this.flattenedNodes[i].isVisible = false;
      } else {
        break; // Exit when we've reached a sibling or uncle node
      }
    }
  }
  
  private findParentNode(childId: string): FlatTreeNode | null {
    for (const node of this.flattenedNodes) {
      if (node.hasChildren && node.component.children?.some(child => child.id === childId)) {
        return node;
      }
    }
    return null;
  }
  
  private scrollToSelectedNode(): void {
    setTimeout(() => {
      if (!this.viewport || !this.selectedNodeId) return;
      
      const index = this.flattenedNodes.findIndex(node => 
        node.id === this.selectedNodeId && node.isVisible
      );
      
      if (index !== -1) {
        this.viewport.scrollToIndex(index);
      }
    });
  }
}
