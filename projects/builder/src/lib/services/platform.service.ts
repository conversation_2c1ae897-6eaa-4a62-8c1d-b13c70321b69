import { Injectable } from '@angular/core';
import { Capacitor } from '@capacitor/core';
import { BehaviorSubject, Observable } from 'rxjs';

export type Platform = 'web' | 'ios' | 'android';

@Injectable({
  providedIn: 'root'
})
export class PlatformService {
  private readonly platformSubject = new BehaviorSubject<Platform>(this.detectPlatform());
  
  /**
   * Observable of the current platform
   */
  readonly platform$: Observable<Platform> = this.platformSubject.asObservable();
  
  /**
   * Returns true if running on a native mobile platform
   */
  isNativePlatform(): boolean {
    return Capacitor.isNativePlatform();
  }
  
  /**
   * Returns the current platform
   */
  getCurrentPlatform(): Platform {
    return this.detectPlatform();
  }
  
  /**
   * Detects the current platform
   */
  private detectPlatform(): Platform {
    if (!Capacitor.isNativePlatform()) {
      return 'web';
    }
    
    const platform = Capacitor.getPlatform();
    return platform as Platform;
  }
  
  /**
   * Runs platform-specific code
   */
  runForPlatform<T>(options: {
    ios?: () => T,
    android?: () => T,
    web?: () => T,
    fallback?: () => T
  }): T | undefined {
    const platform = this.detectPlatform();
    
    if (platform === 'ios' && options.ios) {
      return options.ios();
    }
    
    if (platform === 'android' && options.android) {
      return options.android();
    }
    
    if (platform === 'web' && options.web) {
      return options.web();
    }
    
    if (options.fallback) {
      return options.fallback();
    }
    
    return undefined;
  }
}
