import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface DragState {
  isDragging: boolean;
  componentId: string;
  componentName: string;
  componentType: string;
  position?: { x: number; y: number };
  preview?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DragFeedbackService {
  private readonly dragStateSubject = new BehaviorSubject<DragState | null>(null);
  readonly dragState$ = this.dragStateSubject.asObservable();

  startDrag(params: { 
    id: string; 
    componentName: string;
    type: string;
    preview?: string;
  }): void {
    this.dragStateSubject.next({
      isDragging: true,
      componentId: params.id,
      componentName: params.componentName,
      componentType: params.type,
      preview: params.preview
    });
  }

  updatePosition(position: { x: number; y: number }): void {
    const currentState = this.dragStateSubject.value;
    if (currentState) {
      this.dragStateSubject.next({
        ...currentState,
        position
      });
    }
  }

  endDrag(): void {
    this.dragStateSubject.next(null);
  }
}
