import { Injectable, EventEmitter, Inject, Injector } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, tap, catchError, of, firstValueFrom, combineLatest, map } from 'rxjs';
import { BuilderComponent, isBuilderComponent } from '../interfaces/component.interface';
import { AppConfig, PageConfig, ConfigParams } from '../interfaces/config.interface';
import { createDefaultConfig, createDefaultPage } from '../utils/config.utils';

export interface ComponentStyle {
  position?: {
    x: number;
    y: number;
  };
  width?: number;
  height?: number;
  backgroundColor?: string;
  color?: string;
  fontSize?: string;
  fontWeight?: string;
  padding?: string;
  margin?: string;
  border?: string;
  borderRadius?: string;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  className?: string;
}

export interface ComponentProperties {
  id: string;
  type: 'container' | 'text' | 'button' | 'image';
  style?: {
    className?: string;
    container?: string;
    [key: string]: any;
  };
  text?: string;
  label?: string;  // For button text
  src?: string;    // For image source
  alt?: string;    // For image alt text
  children: ComponentProperties[];  // Make children required but can be empty array
  _version?: number;
}

const defaultPage: PageConfig = {
  id: 'page1',
  name: 'Home',
  components: []
};

const defaultConfig: AppConfig = {
  id: 'default',
  name: 'Default Configuration',
  pages: {
    page1: defaultPage
  }
};

@Injectable({
  providedIn: 'root'
})
export class ConfigService {
  private readonly defaultConfig = createDefaultConfig(
    createDefaultPage('page1', 'Home')
  );

  public readonly configUpdated = new EventEmitter<void>();

  // Public BehaviorSubjects
  public readonly config$ = new BehaviorSubject<AppConfig>(this.defaultConfig);
  public readonly selectedPageId$ = new BehaviorSubject<string | null>(null);
  public readonly selectedComponent$ = new BehaviorSubject<BuilderComponent | null>(null);
  public readonly connectedDropLists$ = new BehaviorSubject<string[]>([]);
  public readonly configParams$ = new BehaviorSubject<ConfigParams | null>(null);

  // Public observables for backward compatibility
  public readonly config = this.config$.asObservable();
  public readonly selectedPageId = this.selectedPageId$.asObservable();
  public readonly connectedDropLists = this.connectedDropLists$.asObservable();

  public readonly selectedPage$ = new BehaviorSubject<PageConfig | null>(null);

  private currentPage = new BehaviorSubject<PageConfig | null>(null);
  currentPage$ = this.currentPage.asObservable();

  private selectedComponentId = new BehaviorSubject<string | null>(null);
  selectedComponentId$ = this.selectedComponentId.asObservable();

  // Add viewport size functionality
  private viewportSizeSubject = new BehaviorSubject<'mobile' | 'tablet' | 'desktop'>('desktop');
  public readonly viewportSize$ = this.viewportSizeSubject.asObservable();
  //protected env: any
  constructor(private http: HttpClient, @Inject('environment') private env: any, private injector: Injector
) {
    // Initialize selected page based on config and selectedPageId changes
    combineLatest([this.config$, this.selectedPageId$]).pipe(
      map(([config, pageId]) => {
        if (!pageId || !config?.pages) return null;
        const page = config.pages[pageId];
        return page || null;
      })
    ).subscribe(page => {
      this.selectedPage$.next(page);
    });
  }

  private getTitleText(page: PageConfig): string {
    if (typeof page.title === 'string') return page.title;
    return page.title?.text || '';
  }

  private getSubtitleText(page: PageConfig): string {
    if (typeof page.subtitle === 'string') return page.subtitle;
    return page.subtitle?.text || '';
  }

  fetchConfig(params: ConfigParams): Observable<AppConfig> {
    console.log('[ConfigService] Fetching config from API:', params);
    const url = `https://esbdev.lp.run/internal/loyaltyapi/1.0.0/mobile/config?version=${params.version}&env=${params.env}&client=${params.client}`;

    return this.http.get<AppConfig>(url).pipe(
      tap(config => {
        console.log('[ConfigService] API Response:', config);
        this.config$.next(config);
        // Ensure we preserve any builder pages if they exist
        const currentConfig = this.config$.getValue();
        console.log('[ConfigService] Current config:', currentConfig);
        const builderPages = Object.entries(currentConfig.pages || {})
          .filter(([key]) => key.startsWith('builder_'))
          .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});
        console.log('[ConfigService] Builder pages:', builderPages);

        // Convert existing config pages to PageConfig format
        const convertedPages = Object.entries(config.pages || {}).reduce((acc, [key, page]) => {
          // Skip if it's already a builder page
          console.log('[ConfigService] Key:', key);
          console.log('ACCC', acc);
          if (key.startsWith('builder_')) return acc;
          console.log('--------------------------------');
          console.log('[ConfigService] Page:', page);
          console.log('--------------------------------');
          // Convert existing page to PageConfig format
          const convertedPage: PageConfig = {
            id: key,
            name: this.getTitleText(page) || this.getSubtitleText(page) || key,
            components: [],  // Initialize empty components array
            type: 'config',  // Mark as config page
            layout: 'fluid',
            styles: {
              backgroundColor: page.class || '',
              backgroundImage: page.icon || '',
              ...page.styles
            },
            originalConfig: page  // Store original config
          };
          console.log('--------------------------------');
          console.log('[ConfigService] Converted Page:', convertedPage);
          console.log('--------------------------------');
          return { ...acc, [key]: convertedPage };
        }, {});

        // Merge the builder pages with the converted config pages
        const mergedConfig = {
          ...config,
          pages: {
            ...convertedPages,
            ...builderPages
          }
        };

        console.log('[ConfigService] Merged config with converted pages:', mergedConfig);
        this.config$.next(mergedConfig);
      }),
      catchError(error => {
        console.error('[ConfigService] API Error:', error);
        throw error;
      })
    );
  }

  getConfig(): AppConfig {
    return this.config$.getValue();
  }

  setConfig(config: AppConfig): void {
    this.config$.next(config);
  }

  selectPage(pageId: string): void {
    console.log('[ConfigService] Selecting page:', pageId);
    const config = this.getConfig();
    const page = config.pages[pageId];

    if (!page) {
      console.error(`[ConfigService] Page ${pageId} not found`);
      return;
    }

    // Ensure page has required properties
    const normalizedPage: PageConfig = {
      ...page,
      id: pageId,
      components: page.components || [],
      type: page.type || 'config',
      layout: page.layout || 'fluid'
    };

    // Update both subjects to ensure synchronization
    this.selectedPageId$.next(pageId);
    this.selectedPage$.next(normalizedPage);

    // Clear selected component when changing pages
    this.selectComponent(null);

    console.log('[ConfigService] Selected page:', normalizedPage);
  }

  selectComponent(component: BuilderComponent | string | null): void {
    if (!component) {
      this.selectedComponent$.next(null);
      return;
    }

    const componentId = typeof component === 'string' ? component : component.id;
    const currentPage = this.getCurrentPage();
    const foundComponent = this.findComponentById(currentPage.components, componentId);

    if (foundComponent) {
      // If we're selecting by ID, use the found component, otherwise merge the provided component
      const completeComponent = typeof component === 'string' ? foundComponent : {
        ...foundComponent,
        ...component,
        // Preserve existing children and their properties
        children: component.children?.map(child => {
          const existingChild = foundComponent.children?.find(c => c.id === child.id);
          if (existingChild) {
            return {
              ...existingChild,
              ...child,
              properties: {
                ...existingChild.properties,
                ...child.properties
              }
            };
          }
          return child;
        }) || foundComponent.children || []
      };

      this.selectedComponent$.next(completeComponent);
    } else {
      console.warn(`Component with id ${componentId} not found`);
      this.selectedComponent$.next(null);
    }
  }

  getSelectedComponent(): BuilderComponent | null {
    return this.selectedComponent$.getValue();
  }

  getCurrentPage(): PageConfig {
    const page = this.selectedPage$.getValue();
    if (!page) {
      return this.defaultConfig.pages['page1'];
    }
    return page;
  }

  setCurrentPage(page: PageConfig) {
    // Initialize empty arrays if needed
    page.components = page.components || [];
    page.components.forEach(component => {
      if (component.type === 'container') {
        component.children = component.children || [];
      }
    });

    this.selectedPage$.next(page);
    this.selectedPageId$.next(page.id);
  }

  updateComponent(component: BuilderComponent): void {
    const currentPage = this.getCurrentPage();
    const config = this.getConfig();

    const updateComponentInTree = (components: BuilderComponent[]): BuilderComponent[] => {
      return components.map((c: BuilderComponent) => {
        if (c.id === component.id) {
          const existingComponent = this.findComponentById(currentPage.components, c.id);
          return {
            ...existingComponent,
            ...component,
            children: component.children?.map((child: BuilderComponent) => {
              const existingChild = existingComponent?.children?.find((ec: BuilderComponent) => ec.id === child.id);
              if (existingChild) {
                return {
                  ...existingChild,
                  ...child,
                  properties: {
                    ...existingChild.properties,
                    ...child.properties
                  }
                };
              }
              return child;
            }) || existingComponent?.children || []
          };
        }
        // Recursively search and update in children
        if (c.children?.length) {
          const updatedChildren = updateComponentInTree(c.children);
          if (updatedChildren.some((child, i) => child !== c.children?.[i])) {
            return {
              ...c,
              children: updatedChildren
            };
          }
        }
        return c;
      });
    };

    const updatedComponents = updateComponentInTree(currentPage.components);
    const updatedPage: PageConfig = {
      ...currentPage,
      components: updatedComponents
    };

    // Update config
    const updatedConfig: AppConfig = {
      ...config,
      pages: {
        ...config.pages,
        [currentPage.id]: updatedPage
      }
    };

    this.setConfig(updatedConfig);
    this.selectedPage$.next(updatedPage);

    // Re-select the component
    const updatedComponent = this.findComponentById(updatedComponents, component.id);
    if (updatedComponent) {
      this.selectedComponent$.next(updatedComponent);
    }
  }

  deleteComponent(componentId: string): void {
    const currentPage = this.selectedPage$.getValue();
    if (!currentPage) {
      console.error('No page selected');
      return;
    }

    const config = this.getConfig();
    const updatedComponents = currentPage.components.filter(c => c.id !== componentId);

    const updatedPage = {
      ...currentPage,
      components: updatedComponents
    };

    config.pages[currentPage.id] = updatedPage;
    this.setConfig(config);
  }

  updateComponents(components: BuilderComponent[]): void {
    console.log('[ConfigService] Updating components:', components);

    // Get current state
    const currentConfig = this.getConfig();
    const currentPageId = this.selectedPageId$.getValue();

    if (!currentPageId || !currentConfig.pages[currentPageId]) {
      console.warn('[ConfigService] No current page when updating components');
      return;
    }

    // Create new page reference with updated components
    const updatedPage: PageConfig = {
      ...currentConfig.pages[currentPageId],
      components: [...components]  // Create new array reference
    };

    // Update config with new page reference
    const updatedConfig = {
      ...currentConfig,
      pages: {
        ...currentConfig.pages,
        [currentPageId]: updatedPage
      }
    };

    // Update config and page without changing selection
    this.setConfig(updatedConfig);
    this.selectedPage$.next(updatedPage);
    this.configUpdated.emit();
  }

  getComponentById(id: string): BuilderComponent | null {
    console.log('[ConfigService] Getting component by id:', id);
    const page = this.selectedPage$.getValue();
    if (!page?.components) {
      console.warn('[ConfigService] No active page or components when getting component');
      return null;
    }

    const builderComponents = page.components
      .filter((component): component is BuilderComponent => isBuilderComponent(component));

    const component = this.findComponentInTree(builderComponents, id);
    console.log('[ConfigService] Found component:', component);
    return component;
  }

  private findComponentInTree(components: BuilderComponent[], id: string): BuilderComponent | null {
    if (!Array.isArray(components)) return null;

    for (const component of components) {
      if (component.id === id) return component;
      if (Array.isArray(component.children) && component.children.length > 0) {
        const found = this.findComponentInTree(component.children, id);
        if (found) return found;
      }
    }
    return null;
  }

  private updateComponentInArray(components: BuilderComponent[], updatedComponent: BuilderComponent): BuilderComponent[] {
    return components.map(component => {
      if (component.id === updatedComponent.id) {
        return updatedComponent;
      }
      if (component.children?.length) {
        return {
          ...component,
          children: this.updateComponentInArray(component.children, updatedComponent)
        };
      }
      return component;
    });
  }

  addComponent(component: BuilderComponent): void {
    const currentPage = this.selectedPage$.getValue();
    if (!currentPage) {
      console.error('No page selected');
      return;
    }

    const config = this.getConfig();
    const updatedPage = {
      ...currentPage,
      components: [...currentPage.components, component]
    };

    config.pages[currentPage.id] = updatedPage;
    this.setConfig(config);
  }

  removeComponent(componentId: string): void {
    console.log('[ConfigService] Removing component:', componentId);
    const config = this.getConfig();
    const pageId = this.selectedPageId$.getValue();
    if (!config || !pageId) return;

    const page = config.pages[pageId];
    if (!Array.isArray(page.components)) {
      page.components = [];
      return;
    }

    const removeFromTree = (components: BuilderComponent[]): boolean => {
      const index = components.findIndex(c => c.id === componentId);
      if (index !== -1) {
        components.splice(index, 1);
        return true;
      }

      for (const component of components) {
        if (component.children && removeFromTree(component.children)) {
          return true;
        }
      }
      return false;
    };

    if (removeFromTree(page.components)) {
      this.setConfig(config);
      if (this.selectedComponentId.getValue() === componentId) {
        this.selectComponent(null);
      }
    }
  }

  async setConfigParams(params: ConfigParams): Promise<void> {
    console.log('[ConfigService] Setting config params:', params);
    this.configParams$.next(params);
  }

  async loadConfig(): Promise<AppConfig> {
    console.log('[ConfigService] Loading config...');
    const params = this.configParams$.getValue();

    if (!params) {
      console.error('[ConfigService] No config params set');
      throw new Error('No config params set');
    }

    try {
      console.log('[ConfigService] Fetching config with params:', params);
      const config = await firstValueFrom(this.fetchConfig(params));
      console.log('[ConfigService] Config loaded successfully:', config);
      this.setConfig(config);
      return config;
    } catch (error) {
      console.error('[ConfigService] Error loading config:', error);
      throw error;
    }
  }

  duplicatePage(pageId: string): void {
    if (!pageId) {
      console.error('No page ID provided for duplication');
      return;
    }

    const config = this.getConfig();
    const sourcePage = config.pages[pageId];

    if (!sourcePage) {
      console.error(`Page ${pageId} not found`);
      return;
    }

    const newPageId = `${pageId}_copy_${Date.now()}`;
    const newPage: PageConfig = {
      ...sourcePage,
      id: newPageId,
      name: `${sourcePage.name} (Copy)`
    };

    const newConfig: AppConfig = {
      ...config,
      pages: {
        ...config.pages,
        [newPageId]: newPage
      }
    };

    this.setConfig(newConfig);
    this.selectPage(newPageId);
  }

  private getActivePage(): string | null {
    return this.selectedPageId$.getValue();
  }

  private updateConfig(config: AppConfig): void {
    this.setConfig(config);
  }

  private setSelectedComponent(component: BuilderComponent | null): void {
    this.selectedComponent$.next(component);
  }

  updateConnectedDropLists(containerIds: string[]) {
    this.connectedDropLists$.next(['componentPalette', ...containerIds]);
  }

  getConnectedDropLists(): string[] {
    return this.connectedDropLists$.getValue();
  }

  updatePage(page: PageConfig) {
    console.log('[ConfigService] Updating page:', page);
    const currentConfig = this.getConfig();

    // Ensure page has required properties
    const normalizedPage: PageConfig = {
      ...page,
      components: page.components || [],
      type: page.type || 'config',
      layout: page.layout || 'fluid'
    };

    // Update the page in the config
    currentConfig.pages[page.id] = normalizedPage;

    // Update the config
    this.setConfig(currentConfig);

    // If this is the current page, update selectedPage
    if (this.selectedPageId$.getValue() === page.id) {
      this.selectedPage$.next(normalizedPage);
    }

    console.log('[ConfigService] Updated page:', normalizedPage);
  }

  addPage(name: string): void {
    const config = this.getConfig();
    const newPageId = `page-${Date.now()}`;
    const newPage: PageConfig = {
      id: newPageId,
      name: name,
      components: [],
      type: 'config',
      layout: 'fluid'
    };

    const updatedConfig: AppConfig = {
      ...config,
      pages: {
        ...config.pages,
        [newPageId]: newPage
      }
    };

    this.setConfig(updatedConfig);
    this.selectPage(newPageId);
  }

  getPages(): PageConfig[] {
    const config = this.getConfig();
    return Object.values(config.pages || {});
  }

  getBuilderPages(): PageConfig[] {
    const config = this.getConfig();
    return Object.entries(config.pages || {})
      .filter(([key]) => key.startsWith('builder_'))
      .map(([_, page]) => page as PageConfig);
  }

  getConfigPages(): PageConfig[] {
    const config = this.getConfig();
    return Object.entries(config.pages || {})
      .filter(([key]) => !key.startsWith('builder_'))
      .map(([_, page]) => page as PageConfig);
  }

  getConfigParams(): ConfigParams | null {
    return this.configParams$.getValue();
  }

  async saveConfig(): Promise<void> {
    const currentParams = this.getConfigParams();
    if (!currentParams) {
      throw new Error('No config params available');
    }

    // Ensure version exists and increment it
    const currentVersion = currentParams.version || '1';
    const newVersion = (parseInt(currentVersion) + 1).toString();
    const newParams = { ...currentParams, version: newVersion };

    // Get current config
    const config = this.getConfig();
    if (!config) {
      throw new Error('No config available to save');
    }

    // Update version in config
    const updatedConfig = { ...config, version: newVersion };

    try {
      // Save to server
      const response = await this.http.post<AppConfig>(
        `${this.env.apiUrl}/config/${newParams.client}/${newParams.env}/${newParams.version}`,
        updatedConfig,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      ).toPromise();

      if (response) {
        // Update local config
        this.setConfig(response);
        // Update params
        await this.setConfigParams(newParams);
        // Add to history
        this.configUpdated.emit();
        return;
      }
      throw new Error('Failed to save config: No response from server');
    } catch (error: any) {
      console.error('Error saving config:', error);
      throw new Error(`Failed to save config: ${error.message || 'Unknown error'}`);
    }
  }

  getSelectedPageId(): string | null {
    return this.selectedPageId$.getValue();
  }

  getSelectedPage(): PageConfig {
    const page = this.selectedPage$.getValue();
    return page || this.defaultConfig.pages['page1'];
  }

  initializeDefaultPage(): void {
    const defaultPage: PageConfig = {
      id: 'page1',
      name: 'Home Page',
      components: [],
      type: 'config',
      layout: 'fluid'
    };

    const initialConfig: AppConfig = {
      id: 'default',
      name: 'Default Config',
      pages: {
        [defaultPage.id]: defaultPage
      },
      version: '1.0.0',
      env: 'development',
      client: 'default'
    };

    this.setConfig(initialConfig);
    this.selectedPageId$.next(defaultPage.id);
  }

  createBuilderPage(): void {
    const config = this.getConfig();
    const newPageId = `builder_${Date.now()}`;
    const newPage: PageConfig = {
      id: newPageId,
      name: 'New Builder Page',
      components: [],
      type: 'builder',
      layout: 'fluid',
      theme: 'default',
      themes: ['default', 'dark']
    };

    const updatedConfig: AppConfig = {
      ...config,
      pages: {
        ...config.pages,
        [newPageId]: newPage
      }
    };

    this.setConfig(updatedConfig);
    this.selectPage(newPageId);
  }

  private mergeComponents(existingComponents: BuilderComponent[], newComponents: BuilderComponent[]): BuilderComponent[] {
    return newComponents.map(component => {
      const existingComponent = existingComponents.find((ec: BuilderComponent) => ec.id === component.id);
      if (existingComponent) {
        return {
          ...existingComponent,
          ...component,
          properties: {
            ...existingComponent.properties,
            ...component.properties
          },
          children: component.children ?
            this.mergeComponents(existingComponent.children || [], component.children) :
            existingComponent.children
        };
      }
      return component;
    });
  }

  setViewportSize(size: 'mobile' | 'tablet' | 'desktop') {
    this.viewportSizeSubject.next(size);
  }

  getViewportSize(): 'mobile' | 'tablet' | 'desktop' {
    return this.viewportSizeSubject.getValue();
  }

  public findComponentById(components: BuilderComponent[], id: string): BuilderComponent | null {
    if (!Array.isArray(components)) return null;

    for (const component of components) {
      if (component.id === id) return component;
      if (Array.isArray(component.children) && component.children.length > 0) {
        const found = this.findComponentById(component.children, id);
        if (found) return found;
      }
    }
    return null;
  }

  // Add rootComponents$ observable that components can subscribe to
  private rootComponentsSubject = new BehaviorSubject<BuilderComponent[]>([]);
  public readonly rootComponents$ = this.rootComponentsSubject.asObservable();
  
  /**
   * Add a component to the root level
   */
  public addRootComponent(component: BuilderComponent): void {
    const rootComponents = [...this.rootComponentsSubject.getValue()];
    rootComponents.push(component);
    this.rootComponentsSubject.next(rootComponents);
  }
  
  /**
   * Add a child component to a parent
   */
  public addChildComponent(parentId: string, childComponent: BuilderComponent): void {
    const rootComponents = [...this.rootComponentsSubject.getValue()];
    
    // Set parent ID
    childComponent.parentId = parentId;
    
    // Helper function to find the parent and add the child
    const addChildToParent = (components: BuilderComponent[]): boolean => {
      for (let i = 0; i < components.length; i++) {
        const component = components[i];
        
        if (component.id === parentId) {
          // Add child to this parent
          if (!component.children) {
            component.children = [];
          }
          component.children.push(childComponent);
          return true;
        }
        
        // Check in children recursively
        if (component.children && component.children.length) {
          if (addChildToParent(component.children)) {
            return true;
          }
        }
      }
      
      return false;
    };
    
    // Add the child and update state if successful
    if (addChildToParent(rootComponents)) {
      this.rootComponentsSubject.next(rootComponents);
    }
  }
  
  /**
   * Reorder a component within its parent's children
   */
  public reorderComponent(componentId: string, parentId: string | null, newIndex: number): void {
    const rootComponents = [...this.rootComponentsSubject.getValue()];
    
    // Handle root level reordering
    if (!parentId) {
      const currentIndex = rootComponents.findIndex(c => c.id === componentId);
      if (currentIndex === -1) return;
      
      // Remove component and add at new position
      const [component] = rootComponents.splice(currentIndex, 1);
      rootComponents.splice(newIndex, 0, component);
      this.rootComponentsSubject.next(rootComponents);
      return;
    }
    
    // Helper function to find the parent and reorder the child
    const reorderInParent = (components: BuilderComponent[]): boolean => {
      for (let i = 0; i < components.length; i++) {
        const component = components[i];
        
        if (component.id === parentId && component.children) {
          // Find child index
          const childIndex = component.children.findIndex(c => c.id === componentId);
          if (childIndex === -1) return false;
          
          // Remove child and add at new position
          const [childComponent] = component.children.splice(childIndex, 1);
          component.children.splice(newIndex, 0, childComponent);
          return true;
        }
        
        // Check in children recursively
        if (component.children && component.children.length) {
          if (reorderInParent(component.children)) {
            return true;
          }
        }
      }
      
      return false;
    };
    
    // Reorder the component and update state if successful
    if (reorderInParent(rootComponents)) {
      this.rootComponentsSubject.next(rootComponents);
    }
  }

  /**
   * Provides an injector for dynamic component creation
   */
  public getInjector(): Injector {
    return this.injector;
  }

  duplicateComponent(sourceId: string, newComponent: BuilderComponent): void {
    // Find source component's parent
    const parentComponent = this.findParentComponent(sourceId);
    if (!parentComponent?.children) return;

    const index = parentComponent.children.findIndex(c => c.id === sourceId);
    if (index === -1) return;

    // Insert duplicated component after original
    parentComponent.children.splice(index + 1, 0, newComponent);
    this.updateComponent(parentComponent);
  }

  moveComponentUp(componentId: string): void {
    const parent = this.findParentComponent(componentId);
    if (!parent?.children) return;

    const index = parent.children.findIndex(c => c.id === componentId);
    if (index <= 0) return;

    // Swap with previous component
    const temp = parent.children[index - 1];
    parent.children[index - 1] = parent.children[index];
    parent.children[index] = temp;

    this.updateComponent(parent);
  }

  moveComponentDown(componentId: string): void {
    const parent = this.findParentComponent(componentId);
    if (!parent?.children) return;

    const index = parent.children.findIndex(c => c.id === componentId);
    if (index === -1 || index >= parent.children.length - 1) return;

    // Swap with next component
    const temp = parent.children[index + 1];
    parent.children[index + 1] = parent.children[index];
    parent.children[index] = temp;

    this.updateComponent(parent);
  }

  private findParentComponent(componentId: string): BuilderComponent | null {
    const page = this.getCurrentPage();
    if (!page?.components) return null;

    // Helper function to recursively search through component tree
    const searchInChildren = (components: BuilderComponent[], targetId: string): BuilderComponent | null => {
      for (const component of components) {
        // Check if any direct child matches the target ID
        if (component.children?.some(child => child.id === targetId)) {
          return component;
        }
        
        // Recursively search in children
        if (component.children?.length) {
          const found = searchInChildren(component.children, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    // Start search from root components
    return searchInChildren(page.components, componentId);
  }
}
