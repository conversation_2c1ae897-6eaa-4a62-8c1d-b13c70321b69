import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

// Import Angular CDK modules
import { DragDropModule } from '@angular/cdk/drag-drop';
import { OverlayModule } from '@angular/cdk/overlay';
import { PortalModule } from '@angular/cdk/portal';
import { A11yModule } from '@angular/cdk/a11y';

import { PlatformModule } from './modules/platform/platform.module';

// Import components and directives
import { CanvasComponent } from './components/canvas/canvas.component';
import { ContainerComponent } from './components/containers/container.component';
import { CanvasControlsComponent } from './components/canvas/canvas-controls/canvas-controls.component';
import { GridComponent } from './components/canvas/grid/grid.component';
import { IconComponent } from './shared/components/icon/icon.component';
import { DynamicComponentDirective } from './directives/dynamic-component.directive';
import { PlatformIfDirective } from './directives/platform-if.directive';
import { PlatformService } from './services/platform.service';
import { ComponentPaletteComponent } from './components/component-palette/component-palette.component';
import { SearchFilterComponent } from './components/component-palette/search-filter/search-filter.component';
import { ComponentItemComponent } from './components/component-palette/component-item/component-item.component';
import { ComponentPreviewComponent } from './components/component-palette/component-preview/component-preview.component';

/**
 * Builder module that provides components and services for the visual application builder
 * Integrates with Capacitor for cross-platform support
 */
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    DragDropModule,
    OverlayModule,
    PortalModule,
    A11yModule,
    PlatformModule,
    
    // Standalone components and directives
    DynamicComponentDirective,
    CanvasComponent,
    CanvasControlsComponent,
    GridComponent,
    IconComponent,
    ContainerComponent,
    
    // Component palette standalone components
    ComponentPaletteComponent,
    SearchFilterComponent,
    ComponentItemComponent,
    ComponentPreviewComponent,
  ],
  declarations: [
    PlatformIfDirective
  ],
  exports: [
    // Components
    CanvasComponent,
    CanvasControlsComponent,
    GridComponent,
    IconComponent,
    ContainerComponent,
    ComponentPaletteComponent,
    
    // Directives
    DynamicComponentDirective,
    PlatformIfDirective,
    
    // Modules
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DragDropModule,
  ],
  providers: [
    provideHttpClient(withInterceptorsFromDi()),
    PlatformService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class BuilderModule { }
