// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN
include ':capacitor-android'
project(':capacitor-android').projectDir = new File('../../../node_modules/@capacitor/android/capacitor')

include ':capacitor-community-barcode-scanner'
project(':capacitor-community-barcode-scanner').projectDir = new File('../../../node_modules/@capacitor-community/barcode-scanner/android')

include ':capacitor-app'
project(':capacitor-app').projectDir = new File('../../../node_modules/@capacitor/app/android')

include ':capacitor-device'
project(':capacitor-device').projectDir = new File('../../../node_modules/@capacitor/device/android')

include ':capacitor-filesystem'
project(':capacitor-filesystem').projectDir = new File('../../../node_modules/@capacitor/filesystem/android')

include ':capacitor-haptics'
project(':capacitor-haptics').projectDir = new File('../../../node_modules/@capacitor/haptics/android')

include ':capacitor-keyboard'
project(':capacitor-keyboard').projectDir = new File('../../../node_modules/@capacitor/keyboard/android')

include ':capacitor-network'
project(':capacitor-network').projectDir = new File('../../../node_modules/@capacitor/network/android')

include ':capacitor-preferences'
project(':capacitor-preferences').projectDir = new File('../../../node_modules/@capacitor/preferences/android')

include ':wap-print'
project(':wap-print').projectDir = new File('../../plugins/wap-print/android')
