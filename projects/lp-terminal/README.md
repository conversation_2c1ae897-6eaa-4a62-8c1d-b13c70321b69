![Logo](https://wappoint.co.za/wp-content/uploads/2021/02/new-logo600x107clr-180x32.png)

![Logo](https://www.loyaltyplus.cloud/wp-content/uploads/2021/12/Loyalty-Plus-Logo-e1638355189183.png)

# WAPPoint Terminal

The Loyalty Plus terminal application for WAPPoint devices.

## Authors

- [<PERSON>](https://gitlab.loyaltyplus.aero/matt)

## Build

To build this project run

```bash
  cd lp-angular
  ng build --project=lp-terminal --configuration=[env]

  cd lp-angular\projects\lp-terminal
  cap sync android
```

#### Environments

```
  dev
  qa
  prep
  production
```

## Deployment

Deployment of this application is done via the WiseEasy TMS interface, login requires an OTP.

[TMS](https://wisecloud.wiseasy.com/)

#### TMS Profiles

```
  Loyalty: <EMAIL>
  Mica: <EMAIL>
  Mahala: <EMAIL>
```

#### TMS App versioing and Deployment

The application is listed under the App List, the latest version shown
![TMS1](./help/tms_1.png)

Viewing the app detail will show the version history and allow for a new version to be uploaded
![TMS2](./help/tms_2.png)

The app can pushed via Mobile or WiFi. If a previous version is installed on a WAP device then the app must be of a higher version
![TMS3](./help/tms_3.png)

The list of device serial numbers must be provided when doing a push, this can be all of them or only specific devices.
![TMS4](./help/tms_4.png)
