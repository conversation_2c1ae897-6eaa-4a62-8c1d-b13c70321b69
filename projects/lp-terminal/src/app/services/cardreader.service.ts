import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { WapPrint } from 'wap-print';

/**
 *
 * This is an application specific service so it will not reside in the normal API project.
 */
@Injectable({
  providedIn: 'root',
})
export class CardService {
  public cardTracks = new BehaviorSubject<CardTracks>(<CardTracks>{});

  constructor() {
    WapPrint.addListener('readMagStripe', (res) => {
      this.cardTracks.next({
        track1: res.track1!,
        track2: res.track2!,
        track3: res.track3!,
      });
    });
  }

  async initCardReader() {
    await WapPrint.initCardReader();
  }

  async closeCardReader() {
    await WapPrint.closeCardReader();
  }

  async readMagStripe(timeout: number) {
    await WapPrint.readMagStripe({
      timeout: !timeout ? 30 : timeout,
    });
  }

  clearCardTracks(): void {
    this.cardTracks.next({ track1: '', track2: '', track3: '' });
  }
}

export interface CardTracks {
  track1: string;
  track2: string;
  track3: string;
}
