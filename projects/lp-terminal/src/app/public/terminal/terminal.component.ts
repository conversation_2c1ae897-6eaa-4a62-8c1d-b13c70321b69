import { Component, Injector } from '@angular/core';
import { Router } from '@angular/router';
import { AlertController } from '@ionic/angular';
import {
  Terminal,
  TerminalGuardService,
  TerminalService,
  ErrorCode,
  LogService,
} from 'lp-client-api';
import { AbstractFormComponent } from 'mobile-components';
import { Device } from '@capacitor/device';
import { WapPrint } from 'wap-print';

@Component({
  selector: 'app-terminal',
  templateUrl: 'terminal.component.html',
  styleUrls: ['terminal.component.scss'],
})
export class TerminalComponent extends AbstractFormComponent<Terminal> {
  terminal!: Terminal;
  activateFailed: boolean = false;

  constructor(
    injector: Injector,
    protected readonly router: Router,
    public terminalService: TerminalService,
    private terminalGuardService: TerminalGuardService,
    private alertController: AlertController,
    private logService: LogService
  ) {
    super(injector);
    this.generateForm();
  }

  ngOnInit(): void {}

  ionViewWillEnter() {
    this.isAccessAllowed();
    this.generateForm();
    this.dismissLoadingModal();
  }

  async isAccessAllowed() {
    if (await this.terminalGuardService.isAccessAllowed()) {
      this.router.navigate(['/secure']);
    }
  }

  generateForm(): void {
    this._form = this._formBuilder.group({
      terminalId: [''],
      pin: [''],
    });
  }

  async authenticate() {
    this.formData = this.getFormValues();
    if (
      this.formData?.terminalId !== undefined &&
      this.formData?.terminalId !== '' &&
      this.formData?.pin !== undefined &&
      this.formData?.pin !== ''
    ) {
      await this.showLoadingModal('Activating');

      let auth: boolean = await this.terminalService.authenticateTerminal(
        this.formData?.terminalId.toUpperCase(),
        await this.getDeviceSerial(),
        this.formData?.pin
      );

      if (auth) {
        this.router.navigate(['/secure']);
        this.logService.warn(
          'TerminalComponent.authenticate',
          'Terminal has been authenticated: ',
          this.formData?.terminalId.toUpperCase()
        );
      } else {
        this.logService.error(
          'TerminalComponent.authenticate',
          'Authentication failed',
          this.formData?.terminalId.toUpperCase(),
          this.formData?.pin
        );
        const alert = await this.alertController.create({
          header: 'Activation Error',
          message:
            'We were unable to activate this device, please try again or contact your administrator.',
          subHeader: 'Code: ' + ErrorCode.TERMINAL_AUTH.toString(),
          buttons: ['OK'],
          cssClass: 'system-alert',
        });

        await alert.present();
      }

      this.dismissLoadingModal();
    } else {
      this.logService.warn(
        'TerminalComponent.authenticate',
        'Terminal ID or PIN not provided',
        true
      );

      const alert = await this.alertController.create({
        header: 'Activation Error',
        message: 'Please enter a Terminal ID to be activated.',
        subHeader: 'Code: ' + ErrorCode.TERMINAL_IDREQUIRED.toString(),
        buttons: ['OK'],
        cssClass: 'system-alert',
      });

      await alert.present();
    }
  }

  async getDeviceSerial(): Promise<string> {
    let serial = await WapPrint.getDeviceSerial({ serial: '' });
    let deviceId: string = await (await Device.getId()).identifier;

    return serial.serial == null || serial.serial.length == 0
      ? deviceId
      : serial.serial;
  }
}
