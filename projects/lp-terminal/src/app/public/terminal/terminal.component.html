<ion-content class="ion-padding">
    <ion-grid style="margin-top: 0;">
        <ion-row style="padding-bottom: 50px;">
            <ion-col class="device-logo">
                <img src="{{terminalService.getLogo('../../../assets/images/lp_logo.png')}}" />
            </ion-col>
        </ion-row>
        <ion-row>
            <ion-col>
                <form class="form" [formGroup]="_form">
                    <ion-item lines="none">
                        <ion-label position="floating">Terminal ID</ion-label>
                        <ion-icon slot="start" name="construct-outline"></ion-icon>
                        <ion-input #terminalId type="text" formControlName="terminalId" (keyup.enter)="gotoNextField(pin)"></ion-input>
                    </ion-item>
                    <ion-item lines="none">
                        <ion-label position="floating">PIN</ion-label>
                        <ion-icon slot="start" name="finger-print-outline"></ion-icon>
                        <ion-input #pin type="password" inputmode="numeric" formControlName="pin" (keyup.enter)="authenticate()"></ion-input>
                    </ion-item>
                </form>
            </ion-col>
        </ion-row>
        <ion-row>
            <ion-col>
                <ion-label class="terminal-info">Please enter the Terminal ID that has been assigned to this device in order to activate it. Contact your administrator for assistance.</ion-label>
            </ion-col>
        </ion-row>
        <ion-row>
            <ion-col>
                <ion-button expand="block" class="clay" type="submit" (click)="authenticate()">Activate</ion-button>
            </ion-col>
        </ion-row>
    </ion-grid>
</ion-content>