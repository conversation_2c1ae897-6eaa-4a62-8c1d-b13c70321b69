import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { IonicModule } from '@ionic/angular';
import { ComponentsModule } from 'mobile-components';
import { IonIntlTelInputModule } from 'third-party-fix';
import { PublicRoutingModule } from './public-routing.module';
import { TerminalComponent } from './terminal/terminal.component';

@NgModule({
  declarations: [TerminalComponent],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    IonIntlTelInputModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    GoogleMapsModule,
    PublicRoutingModule,
    ComponentsModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PublicModule {}
