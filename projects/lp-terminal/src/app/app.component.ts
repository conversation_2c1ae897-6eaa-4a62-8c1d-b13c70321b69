import { Component, ElementRef, Injector } from '@angular/core';
import { Alert<PERSON>ontroller, NavController, Platform } from '@ionic/angular';
import { Router } from '@angular/router';
import { IdleService, Terminal, TerminalService } from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';
import { Observable } from 'rxjs';
import { Network } from '@capacitor/network';
import { BarcodeScanner } from '@capacitor-community/barcode-scanner';
import { CardService } from './services/cardreader.service';
import { WapPrint } from 'wap-print';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  styles: [
    `
      :root {
        --ion-color-primary: #e92436;
      }
    `,
  ],
})
export class AppComponent extends AbstractComponent {
  terminal!: Terminal;
  public appIsOnline?: Observable<boolean> = undefined;
  checkInterent?: boolean;

  constructor(
    injector: Injector,
    private platform: Platform,
    private alertController: AlertController,
    private navCtrl: NavController,
    protected readonly router: Router,
    private terminalService: TerminalService,
    private elRef: ElementRef,
    private idleService: IdleService,
    private cardService: CardService
  ) {
    super(injector);
    this.initializeApp();

    async () => {
      const status = await Network.getStatus();

      console.log('Network status:', status);

      if (!status.connected) {
        this.networkDisconectedAlert();
      }
    };
  }

  ngOnInit() {
    this.addGlobalSubscription(
      this.terminalService.terminal.subscribe((value) => {
        this.terminal = value;

        if (this.terminal.themeColor !== undefined) {
          this.elRef.nativeElement.style.setProperty(
            '--ion-color-primary',
            this.terminal.themeColor
          );
        }
      })
    );

    WapPrint.initSDK();

    this.idleService.start();
    this.cardService.initCardReader();
  }

  override ngOnDestroy() {
    super.ngOnDestroy();
    this.idleService.stop();
  }

  async initializeApp() {
    this.platform.ready().then(() => {
      Network.addListener('networkStatusChange', (status) => {
        if (!status.connected) {
          console.log('Network status changed', status);
          this.networkDisconectedAlert();
        }
      });

      this.platform.backButton.subscribeWithPriority(10, () => {
        if (
          this.router.url === '/public' ||
          this.router.url === '/secure' ||
          this.router.url === '/secure/home'
        ) {
          (navigator as any).app.exitApp();
        } else if (
          this.router.url === '/secure/search' ||
          this.router.url === '/secure/transaction'
        ) {
          this.router.navigate(['/secure/home']);
        } else {
          this.navCtrl.back();
        }
      });
    });
  }

  async networkDisconectedAlert() {
    const alert = await this.alertController.create({
      header: 'Network disconnected!',
      message:
        'An active network connection is required to use this application.',
      buttons: [
        {
          text: 'Exit',
          handler: () => {
            (navigator as any).app.exitApp();
          },
        },
      ],
      cssClass: 'system-alert',
    });

    await alert.present();
  }

  async stopScan() {
    BarcodeScanner.showBackground();
    BarcodeScanner.stopScan();
    document.body.classList.remove('qrscanner');
  }
}
