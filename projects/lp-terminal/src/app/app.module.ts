import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
import { CommonModule, DecimalPipe } from '@angular/common';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { RouteReuseStrategy } from '@angular/router';
import { InterceptorService, KeyCloakService, LssConfig } from 'lp-client-api';
import { environment } from '../environments/environment';
import { LogService } from 'lp-client-api';
import { LogPublishersService } from 'lp-client-api';

@NgModule({
  declarations: [AppComponent],
  imports: [
    CommonModule,
    BrowserModule,
    FontAwesomeModule,
    HttpClientModule,
    IonicModule.forRoot(),
    AppRoutingModule,
  ],
  providers: [
    { provide: 'environment', useValue: environment },
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    { provide: LssConfig, useValue: AppModule.createConfig() },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: InterceptorService,
      multi: true,
      deps: [KeyCloakService],
    },
    DecimalPipe,
    LogService,
    LogPublishersService,
  ],
  bootstrap: [AppComponent],
})
export class AppModule {
  public static createConfig(): LssConfig {
    const config: LssConfig = JSON.parse(JSON.stringify(environment.lssConfig));
    return config;
  }
}
