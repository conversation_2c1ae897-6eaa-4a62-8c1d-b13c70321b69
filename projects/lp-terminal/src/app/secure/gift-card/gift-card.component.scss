.grid-center {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
}

.grid-center ion-button {
    height: 70px;
    width: 80vw;
    font-size: 18px;
    margin: 10px 0 10px 0;
}

.search-results {
    font-size: 1.1rem;
}

.divider-text {
    font-size: 1.1rem;
    font-weight: 500;
}

.funded {
    font-weight: bold;
}

.slip-buttons ion-button {
    margin-top: 10px;
}

.card-outcome {
    font-size: 1.25rem;
    padding-top: 10px;
    font-weight: bold;
    text-decoration: underline;
    color: var(--ion-color-primary);
}