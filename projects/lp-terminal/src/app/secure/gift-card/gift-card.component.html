<ion-header>
    <ion-toolbar>
        <ion-title>Gift Cards</ion-title>
    </ion-toolbar>
</ion-header>
<ion-content class="ion-padding" color="light" *ngIf="!giftCard">
    <ion-grid class="grid-center">
        <ion-row>
            <ion-col>
                <ion-button expand="block" class="clay" type="submit" (click)="scanBarcode()">
                    <ion-icon slot="start" name="camera"></ion-icon>
                    Scan Gift Card
                </ion-button>
            </ion-col>
        </ion-row>
        <ion-row>
            <ion-col>
                <ion-button expand="block" class="clay" type="submit" (click)="back()">
                    Cancel
                </ion-button>
            </ion-col>
        </ion-row>
    </ion-grid>
</ion-content>
<ion-content class="ion-padding" color="light" *ngIf="giftCard">
    <ion-grid>
        <ion-row>
            <ion-col>
              <div class="search-results" style="text-align: center;">
                <p>Value: R{{giftCard.value}}</p>
                <p>Balance: R{{giftCard.balance}}</p>
                <p *ngIf="giftCard.validity">Validity: {{giftCard.validity | date: 'dd MMM yyyy'}}</p>
                <p *ngIf="redeemSuccess" class="card-outcome">Gift Card redeemed</p>
                <p *ngIf="fundSuccess" class="card-outcome">Gift Card funded</p>
              </div>
              <ion-grid *ngIf="valid">
                <ion-row>
                  <ion-col>
                    <div class="header-divider"></div>
                  </ion-col>
                </ion-row>
                <ion-row *ngIf="!fundSuccess && !redeemSuccess">
                    <ion-col>
                        <form class="form" [formGroup]="_form">
                            <ion-item lines="none" fill="outline">
                                <ion-icon slot="start" name="cash"></ion-icon>
                                <ion-input #giftValue label="Gift Value (R)" labelPlacement="floating" type="number" formControlName="giftValue" (keyup.enter)="gotoNextField(invoiceNumber)" (keypress)="decimalFilter($event)"></ion-input>
                            </ion-item>
                            <ion-item lines="none" *ngIf="isFormComponentInvalid('giftValue')">
                                <div *ngFor="let error of getComponentErrors('giftValue')" class="validator-error">
                                    {{ error }}
                                </div>
                            </ion-item>
                            <ion-item lines="none" fill="outline">
                                <ion-icon slot="start" name="document-text"></ion-icon>
                                <ion-input #invoiceNumber label="Invoice Number" labelPlacement="floating" type="text" formControlName="invoiceNumber"></ion-input>
                              </ion-item>
                              <ion-item lines="none" *ngIf="isFormComponentInvalid('invoiceNumber')">
                                <div *ngFor="let error of getComponentErrors('invoiceNumber')" class="validator-error">
                                    {{ error }}
                                </div>
                              </ion-item>
                        </form>
                    </ion-col>
                </ion-row>
                <ion-row *ngIf="!giftCard.canFund && giftCard.balance === 0 && !fundSuccess && !redeemSuccess">
                  <ion-col>
                    <p>Gift Card has no available funds.</p>
                  </ion-col>
                </ion-row>
                <ion-row *ngIf="giftCard.canFund">
                    <ion-col>
                      <ion-button expand="block" class="clay" type="submit" [disabled]="!isFormValid()" (click)="fundGiftCard()">
                        <ion-icon slot="start" name="cash-outline"></ion-icon>
                        Fund Gift Card
                      </ion-button>
                    </ion-col>
                  </ion-row>
                <ion-row *ngIf="!giftCard.canFund && giftCard.balance > 0 && !fundSuccess && !redeemSuccess">
                    <ion-col>
                      <ion-button expand="block" class="clay" type="submit" [disabled]="!isFormValid()" (click)="redeemGiftCard()">
                        <ion-icon slot="start" name="cash-outline"></ion-icon>
                        Redeem Gift Card
                      </ion-button>
                    </ion-col>
                  </ion-row>
                <ion-row>
                    <ion-col>
                      <ion-button expand="block" class="clay" type="submit" (click)="reset()" *ngIf="!fundSuccess && !redeemSuccess">
                        Back
                      </ion-button>
                    </ion-col>
                  </ion-row>
              </ion-grid>
              <ion-grid *ngIf="canPrintSlip">
                <ion-row>
                    <ion-col class="slip-buttons">
                      <ion-button expand="block" class="clay" (click)="printSlip('member')">Print Customer Slip</ion-button>
                      <ion-button expand="block" class="clay" (click)="printSlip('store')">Print Store Slip</ion-button>
                    </ion-col>
                  </ion-row>
                  <ion-row>
                    <ion-col>
                      <ion-button expand="block" class="clay" [routerLink]="['/secure/home']">Finish</ion-button>
                    </ion-col>
                  </ion-row>
              </ion-grid>
            </ion-col>
          </ion-row>
    </ion-grid>
</ion-content>