import { Component, Injector } from '@angular/core';
import { Router } from '@angular/router';
import {
  BasicProfile,
  IdleService,
  LogService,
  SystemService,
  Terminal,
  TerminalService,
  ErrorCode,
  MemberCommObject,
  GiftCard,
  GiftCardRequest,
  MemberService,
  WAPSlipConfig,
  WAPSlipData,
} from 'lp-client-api';
import { AbstractFormComponent } from 'mobile-components';
import { CardService } from '../../services/cardreader.service';
import {
  BarcodeScanner,
  SupportedFormat,
} from '@capacitor-community/barcode-scanner';
import { AlertController } from '@ionic/angular';
import { Validators } from '@angular/forms';
import { WapPrint } from 'wap-print';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-gift-card',
  templateUrl: 'gift-card.component.html',
  styleUrls: ['gift-card.component.scss'],
})
export class GiftCardComponent extends AbstractFormComponent<any> {
  terminal!: Terminal;
  attendant?: BasicProfile | null;
  giftCard?: GiftCard;
  private timedOut: boolean = false;
  valid: boolean = false;
  canPrintSlip: boolean = false;
  slipData: WAPSlipData = {};
  fundSuccess: boolean = false;
  redeemSuccess: boolean = false;

  constructor(
    injector: Injector,
    private router: Router,
    private logService: LogService,
    private systemService: SystemService,
    private terminalService: TerminalService,
    private cardService: CardService,
    private idleService: IdleService,
    private alertController: AlertController,
    private memberService: MemberService
  ) {
    super(injector);
    this.generateForm();
  }

  ionViewWillEnter(): void {
    this.timedOut = false;

    // Subscribe to the session terminal
    this.addGlobalSubscription(
      this.terminalService.terminal.subscribe(
        (value) => (this.terminal = value)
      )
    );
    // Subscribe to the session attendant
    this.addGlobalSubscription(
      this.terminalService.attendant.subscribe(
        (value) => (this.attendant = value)
      )
    );
    // Subscribe to the timeout
    this.addViewSubscription(
      this.idleService.timeout.subscribe((timeout) => {
        if (timeout >= 300) {
          this.timedOut = true;
          this.back();
        }
      })
    );

    if (this.attendant === undefined || this.attendant === null) {
      this.router.navigate(['/secure/attendant']);
    }
  }

  ionViewWillLeave(): void {
    this.reset();
  }

  generateForm(): void {
    this._form = this._formBuilder.group({
      giftValue: ['', [Validators.required]],
      invoiceNumber: ['', [Validators.required]],
    });
  }

  back(): void {
    this.reset();
    this.router.navigate(['/secure/home']);
  }

  reset(): void {
    this._form.reset({
      giftValue: '',
      invoiceNumber: '',
    });
    this.formData = undefined;
    this.slipData = {};
    this.giftCard = undefined;
    this.canPrintSlip = false;
    this.fundSuccess = false;
    this.redeemSuccess = false;
    this.cardService.clearCardTracks();
    this.cardService.closeCardReader();
  }

  isValid(): boolean {
    let now = new Date();

    if (this.giftCard !== undefined && this.giftCard.status !== 'STAA') {
      return false;
    }

    if (
      this.giftCard !== undefined &&
      this.giftCard.validity !== undefined &&
      this.giftCard.validity >= now
    ) {
      return false;
    }

    return true;
  }

  async scanBarcode(): Promise<void> {
    const status = await BarcodeScanner.checkPermission({ force: true });
    if (status.granted) {
      await this.startScan();
    } else {
      const noPermission = await this.alertController.create({
        header: 'Unable to access camera',
        message:
          'Access to the camera has been prohibited; please enable it in the settings to continue scanning.',
        subHeader: 'Code: ' + ErrorCode.CAMERA_PERMISSION.toString(),
        buttons: ['OK'],
        cssClass: 'system-alert',
      });
      noPermission.present();
    }
  }

  async startScan() {
    await BarcodeScanner.hideBackground();
    document.body.classList.add('qrscanner'); // add the qrscanner class to body
    const result = await BarcodeScanner.startScan({
      targetedFormats: [SupportedFormat.CODE_128],
    });
    document.body.classList.remove('qrscanner'); // remove the qrscanner from the body
    if (result.hasContent) {
      // TODO: look up gift card
      this.fetchGiftCard(result.content.trim());
      this.logService.info(
        'GiftCardComponent.startScan',
        'Gift card has been scanned: ',
        result.content
      );
    }
  }

  async fetchGiftCard(giftCode: string) {
    await this.showLoadingModal('Please wait...');

    let memberCommObject: MemberCommObject = {};
    memberCommObject.apiId = this.terminal.apiId?.apiId;

    memberCommObject.uniqueId = this.systemService.getUniqueId(
      this.terminal.apiId!.secretStart,
      this.terminal.apiId!.secretEnd,
      giftCode
    );

    const alert = await this.alertController.create({
      header: 'Gift Card Error',
      message: 'Error scanning Gift Card.',
      subHeader: 'Code: ' + ErrorCode.CAMERA_PERMISSION.toString(),
      buttons: ['OK'],
      cssClass: 'system-alert',
    });

    let result: GiftCard;

    await this.memberService
      .getGiftCard(memberCommObject, this.terminal.terminalId, giftCode)
      .subscribe({
        next: (n) => {
          result = n;
        },
        error: (e) => {
          this.logService.error('GiftCardComponent.search', e.message);
          this.dismissLoadingModal();
          this._formState = this._formStateType.fail;

          alert.message =
            e.error.detail !== undefined
              ? e.error.detail
              : 'Error scanning Gift Card';
          alert.present();
        },
        complete: () => {
          if (result !== undefined) {
            this._formState = this._formStateType.pass;
            this.giftCard = result;
            this.valid = this.isValid();
            console.log(this.giftCard);
          } else {
            this._formState = this._formStateType.fail;
            //alert.present();
          }
          this.dismissLoadingModal();
        },
      });
  }

  decimalFilter(event: any): void {
    const decimalRegex = /^-?\d*(\.\d{0,2})?$/;
    let input = event.target.value + String.fromCharCode(event.charCode);

    if (!decimalRegex.test(input)) {
      event.preventDefault();
    }
  }

  async fundGiftCard() {
    await this.showLoadingModal('Please wait...');
    this.formData = this.getFormValues();

    let memberCommObject: MemberCommObject = {};
    memberCommObject.apiId = this.terminal.apiId?.apiId;
    memberCommObject.uniqueId = this.systemService.getUniqueId(
      this.terminal.apiId!.secretStart,
      this.terminal.apiId!.secretEnd,
      this.giftCard!.giftCode
    );

    let giftCardBody: GiftCardRequest = {
      giftCode: this.giftCard!.giftCode,
      amount: this.formData.giftValue,
      invoiceNumber: this.formData.invoiceNumber,
    };

    let result: any;

    const alert = await this.alertController.create({
      header: 'Transaction Error',
      message:
        'Unable to process the transaction, please try again or contact the administrator.',
      buttons: ['OK'],
      cssClass: 'system-alert',
    });

    await this.memberService
      .fundGiftCard(memberCommObject, this.terminal.terminalId, giftCardBody)
      .subscribe({
        next: (n) => {
          result = n;
        },
        error: (error) => {
          this.logService.error(
            'GiftCardComponent.fundGiftCard',
            error.message
          );
          this.dismissLoadingModal();
          this._formState = this._formStateType.fail;

          alert.message =
            error.error.detail !== undefined
              ? error.error.detail
              : 'An error occurred during transaction processing';
          alert.present();
        },
        complete: () => {
          if (
            result !== undefined &&
            result.giftCode === this.giftCard!.giftCode
          ) {
            this._formState = this._formStateType.pass;
            this.giftCard = result;
            this.canPrintSlip = true;
            this.generateSlipData();
            this.fundSuccess = true;
          } else {
            this._formState = this._formStateType.fail;
            alert.present();
          }
          this.dismissLoadingModal();
        },
      });
  }

  private async generateSlipData() {
    let timeStamp: Date =
      this.formData?.receiptDateTime === undefined
        ? new Date()
        : this.formData?.receiptDateTime;

    this.slipData.timeStamp = formatDate(
      timeStamp,
      'dd MMM yyyy HH:mm:ss',
      'en-GB'
    );
    this.slipData.logo = this.terminal.slipLogobase64;
    this.slipData.partnerName = this.terminal.partnerName;
    this.slipData.amount = this.formData!.giftValue;
    this.slipData.invoiceNumber = this.formData!.invoiceNumber;
    this.slipData.expiry =
      this.giftCard?.validity === undefined
        ? 'None'
        : formatDate(this.giftCard?.validity, 'dd MMM yyyy', 'en-GB');
    this.slipData.attendant =
      this.attendant?.firstname + ' ' + this.attendant?.lastname;
    this.slipData.attendantNumber = this.attendant?.membershipNumber;
    this.slipData.currencyBalance = this.giftCard?.balance;

    this.logService.info(
      'GiftCardComponent.generateSlipData',
      'Slip has been generated for invoice: ',
      this.formData!.receiptNumber,
      this.slipData
    );
  }

  async printSlip(type: string) {
    await this.showLoadingModal('Printing Slip');
    let printConfig: WAPSlipConfig[] | undefined = [];

    printConfig = this.terminalService.getTerminalSlip(
      this.terminal,
      type,
      'GIFT'
    );

    let options = {
      printConfig: JSON.stringify(printConfig),
      printData: JSON.stringify(this.slipData),
      result: false,
    };

    let printResult = await WapPrint.print(options);
    let result: any = printResult.result;
    console.log(result);
    this.logService.info(
      'GiftCardComponent.printSlip',
      'Print result: ',
      printResult.result
    );
    this.dismissLoadingModal();
  }

  async redeemGiftCard() {
    await this.showLoadingModal('Please wait...');
    this.formData = this.getFormValues();

    let memberCommObject: MemberCommObject = {};
    memberCommObject.apiId = this.terminal.apiId?.apiId;
    memberCommObject.uniqueId = this.systemService.getUniqueId(
      this.terminal.apiId!.secretStart,
      this.terminal.apiId!.secretEnd,
      this.giftCard!.giftCode
    );

    let giftCardBody: GiftCardRequest = {
      giftCode: this.giftCard!.giftCode,
      amount: this.formData.giftValue,
      invoiceNumber: this.formData.invoiceNumber,
    };

    let result: any;

    const alert = await this.alertController.create({
      header: 'Transaction Error',
      message:
        'Unable to process the transaction, please try again or contact the administrator.',
      buttons: ['OK'],
      cssClass: 'system-alert',
    });

    await this.memberService
      .redeemGiftCard(memberCommObject, this.terminal.terminalId, giftCardBody)
      .subscribe({
        next: (n) => {
          result = n;
        },
        error: (error) => {
          this.logService.error(
            'GiftCardComponent.redeemGiftCard',
            error.message
          );
          this.dismissLoadingModal();
          this._formState = this._formStateType.fail;

          alert.message =
            error.error.detail !== undefined
              ? error.error.detail
              : 'An error occurred during transaction processing';
          alert.present();
        },
        complete: () => {
          if (
            result !== undefined &&
            result.giftCode === this.giftCard!.giftCode
          ) {
            this._formState = this._formStateType.pass;
            this.giftCard = result;
            this.generateSlipData();
            this.canPrintSlip = true;
            this.redeemSuccess = true;
          } else {
            this._formState = this._formStateType.fail;
            alert.present();
          }
          this.dismissLoadingModal();
        },
      });
  }
}
