<ion-header *ngIf="!memberFound">
  <ion-toolbar>
    <ion-title>Find a Member</ion-title>
  </ion-toolbar>
</ion-header>
<ion-header *ngIf="memberFound && basicProfile">
  <ion-toolbar>
    <ion-title>Member Details</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding" color="light">
  <ion-grid>
    <ion-row *ngIf="!memberFound">
      <ion-col>
        <ion-button expand="block" class="clay" type="submit" (click)="scanBarcode()">
          <ion-icon slot="start" name="camera"></ion-icon>
          Scan Membership Card
        </ion-button>
        <p class="divider-text">OR</p>
        <form class="form" [formGroup]="_form">
          <ion-item lines="none" fill="outline">
            <ion-icon slot="start" name="person"></ion-icon>
            <ion-input #membershipNumber label="Membership Number" labelPlacement="floating" type="text" inputmode="numeric" formControlName="membershipNumber" [clearInput]="true" enterkeyhint="Search" (keyup.enter)="search('')"></ion-input>
          </ion-item>
          <ion-item lines="none" fill="outline">
            <ion-icon slot="start" name="document"></ion-icon>
            <ion-input #idNumber label="ID Number" labelPlacement="floating" type="text" inputmode="numeric" formControlName="idNumber" [clearInput]="true" enterkeyhint="Search" (keyup.enter)="search('')"></ion-input>
          </ion-item>
          <ion-label>Please complete one of the search fields above</ion-label>
          <br/>
          <br/>
          <ion-button #submit expand="block" class="clay" type="submit" [disabled]="!isFormValid()" (click)="search('')">
            <ion-icon slot="start" name="search"></ion-icon>
            Search
          </ion-button>
        </form>
        
      </ion-col>
    </ion-row>
    <ion-row *ngIf="!memberFound">
      <ion-col>
        <ion-button expand="block" class="clay" (click)="cancel()" [RouterLink]="['/secure/home']">Cancel</ion-button>
      </ion-col>
    </ion-row>
    <ion-row *ngIf="memberFound && basicProfile">
      <ion-col>
        <div class="search-results" style="text-align: center;">
          <p>{{basicProfile.firstname}} {{basicProfile.lastname}} - {{basicProfile.membershipNumber}}</p>
          <p>Points: {{basicProfile.availUnits}}</p>
          <p>Balance: R{{basicProfile.availRands}}</p>
        </div>
        <ion-grid>
          <ion-row>
            <ion-col>
              <div class="header-divider"></div>
            </ion-col>
          </ion-row>
          <ion-row *ngIf="terminal.allowAccrual">
            <ion-col>
              <ion-button expand="block" class="clay" (click)="actionClick('Accrual')">
                <ion-icon slot="start" name="cart-outline"></ion-icon>
                Earn Points
              </ion-button>
            </ion-col>
          </ion-row>
          <ion-row *ngIf="(basicProfile.availRands !== undefined && basicProfile.availRands > 0 && terminal.allowRedemption)">
            <ion-col>
              <ion-button expand="block" class="clay" (click)="actionClick('Redemption')">
                <ion-icon slot="start" name="card-outline"></ion-icon>
                Pay with Points
              </ion-button>
            </ion-col>
          </ion-row>
          <ion-row *ngIf="terminal.allowAccrual || terminal.allowRedemption">
            <ion-col>
              <ion-button expand="block" class="clay" (click)="actionClick('Slips')">
                <ion-icon slot="start" name="newspaper-outline"></ion-icon>
                Slips
              </ion-button>
            </ion-col>
          </ion-row>
          <ion-row *ngIf="terminal.allowAccrual || (basicProfile.availRands !== undefined && basicProfile.availRands > 0 && terminal.allowRedemption)">
            <ion-col>
              <div class="header-divider"></div>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <ion-button expand="block" class="clay" (click)="reset()">
                <ion-icon slot="start" name="search"></ion-icon>
                Search Again
              </ion-button>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <ion-button expand="block" class="clay" (click)="cancel()">Cancel</ion-button>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>