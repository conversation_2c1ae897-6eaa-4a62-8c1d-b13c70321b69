import { Component, Injector } from '@angular/core';
import { Router } from '@angular/router';
import {
  BasicProfile,
  CustomValidators,
  ErrorCode,
  IdleService,
  LPMemberEntityTools,
  LogService,
  MemberCommObject,
  MemberService,
  SystemService,
  Terminal,
  TerminalService,
} from 'lp-client-api';
import { AlertController, ModalController } from '@ionic/angular';
import { AbstractFormComponent } from 'mobile-components';
import { AttendantComponent } from '../attendant/attendant.component';
import { AbstractControlOptions, Validators } from '@angular/forms';
import {
  BarcodeScanner,
  SupportedFormat,
} from '@capacitor-community/barcode-scanner';
import { CardService, CardTracks } from '../../services/cardreader.service';

@Component({
  selector: 'search-home',
  templateUrl: 'search.component.html',
  styleUrls: ['search.component.scss'],
})
export class SearchComponent extends AbstractFormComponent<BasicProfile> {
  terminal!: Terminal;
  attendant?: BasicProfile | null;
  memberFound: boolean = false;
  basicProfile?: BasicProfile;
  private timedOut: boolean = false;
  private cardTracks!: CardTracks;

  constructor(
    injector: Injector,
    public terminalService: TerminalService,
    private memberService: MemberService,
    private systemService: SystemService,
    protected readonly router: Router,
    private alertController: AlertController,
    private modalCtrl: ModalController,
    private idleService: IdleService,
    private logService: LogService,
    private cardService: CardService
  ) {
    super(injector);
    this.generateForm();
  }

  ionViewWillEnter(): void {
    this.timedOut = false;

    // Subscribe to the session terminal
    this.addGlobalSubscription(
      this.terminalService.terminal.subscribe(
        (value) => (this.terminal = value)
      )
    );
    // Subscribe to the session attendant
    this.addGlobalSubscription(
      this.terminalService.attendant.subscribe(
        (value) => (this.attendant = value)
      )
    );
    // Subscribe to the timeout
    this.addViewSubscription(
      this.idleService.timeout.subscribe((timeout) => {
        if (timeout >= 120) {
          this.timedOut = true;
          this.cancel();
        }
      })
    );
    // Subscribe to the magnetic card reader results
    this.addGlobalSubscription(
      this.cardService.cardTracks.subscribe((value) => {
        this.cardTracks = value;

        if (this.cardTracks.track2) {
          //this._form.patchValue({ membershipNumber: this.cardTracks.track2 });
          this.search(this.cardTracks.track2);
        }
      })
    );

    this.cardService.readMagStripe(120);

    if (
      this.attendant === undefined ||
      this.attendant === null ||
      this.attendant.membershipNumber === undefined
    ) {
      this.router.navigate(['/secure/attendant']);
    }

    if (history.state.data) {
      this.basicProfile = history.state.data;
      this.memberFound = true;
    }

    this.logService.info(
      'SearchComponent.ionViewWillEnter',
      'Attendant: ',
      JSON.stringify(this.attendant)
    );
  }

  ionViewWillLeave(): void {
    this.reset();
  }

  generateForm(): void {
    this._form = this._formBuilder.group(
      {
        membershipNumber: [''],
        idNumber: [''],
        passortNum: [''],
        mobileNumber: [
          LPMemberEntityTools.getIONTelephoneFromLPTel(
            LPMemberEntityTools.getEmptyPhone('CELL')
          ),
        ],
      },
      {
        validators: Validators.compose([
          CustomValidators.atLeastOneRequired([
            'membershipNumber',
            'idNumber',
            'passortNum',
          ]),
        ]),
      } as AbstractControlOptions
    );
  }

  reset(): void {
    this._form.reset({
      membershipNumber: '',
      idNumber: '',
      passortNum: '',
      mobileNumber: '',
    });
    this.memberFound = false;
    this.basicProfile = undefined;
    this.formData = undefined;
    this.cardService.clearCardTracks();
    this.cardService.closeCardReader();
  }

  async search(searchMember: string) {
    this.formData = this.getFormValues();

    const alert = await this.alertController.create({
      header: 'Search Error',
      message:
        'No member found for the provided search criteria, please try again or contact the administrator for assistance.',
      subHeader: 'Code: ' + ErrorCode.NO_MEMBER.toString(),
      buttons: ['OK'],
      cssClass: 'system-alert',
    });

    await this.showLoadingModal('Searching');

    if (searchMember === undefined || searchMember === '') {
      searchMember =
        this.formData?.membershipNumber === undefined ||
        this.formData?.membershipNumber === ''
          ? this.formData?.idNumber === undefined ||
            this.formData?.idNumber === ''
            ? ''
            : this.formData?.idNumber
          : this.formData?.membershipNumber;
    }

    let memberCommObject: MemberCommObject = {};
    memberCommObject.apiId = this.terminal.apiId?.apiId;
    memberCommObject.membershipNumber = searchMember;
    memberCommObject.uniqueId = this.systemService.getUniqueId(
      this.terminal.apiId!.secretStart,
      this.terminal.apiId!.secretEnd,
      searchMember
    );

    let result: BasicProfile[];

    await this.memberService.search(memberCommObject).subscribe({
      next: (n) => {
        result = n;
      },
      error: (e) => {
        this.logService.error('SearchComponent.search', e.message);
        this.dismissLoadingModal();
        this._formState = this._formStateType.fail;
        alert.present();
        this.cardService.clearCardTracks();
        this.cardService.readMagStripe(120);
      },
      complete: () => {
        if (result !== undefined && result.length === 1) {
          this._formState = this._formStateType.pass;
          this.basicProfile = result[0];
          this.memberFound = true;
        } else {
          this._formState = this._formStateType.fail;
          alert.present();
          this.cardService.clearCardTracks();
          this.cardService.readMagStripe(120);
        }
        this.dismissLoadingModal();
      },
    });
  }

  actionClick(transactionType: string): void {
    if (transactionType === 'Slips') {
      this.router.navigate(['/secure/slips'], {
        state: { data: this.basicProfile },
      });
    } else {
      this.router.navigate(['/secure/transaction'], {
        state: { data: this.basicProfile, transactionType: transactionType },
      });
    }
  }

  async startScan() {
    await BarcodeScanner.hideBackground();
    document.body.classList.add('qrscanner'); // add the qrscanner class to body
    const result = await BarcodeScanner.startScan({
      targetedFormats: [SupportedFormat.CODE_128],
    });
    document.body.classList.remove('qrscanner'); // remove the qrscanner from the body
    if (result.hasContent) {
      this.search(result.content);
      this.logService.info(
        'SearchComponent.startScan',
        'Membership card has been scanned: ',
        result.content
      );
    }
  }

  async scanBarcode(): Promise<void> {
    const status = await BarcodeScanner.checkPermission({ force: true });
    if (status.granted) {
      await this.startScan();
    }
  }

  async showAttendant() {
    const modal = await this.modalCtrl.create({
      id: 'showAttendant',
      component: AttendantComponent,
    });
    modal.present();
  }

  async cancel() {
    await this.cardService.closeCardReader();
    await this.terminalService.attendant.next(null);
    this.router.navigate(['/secure/home']);

    if (this.timedOut) {
      const alert = await this.alertController.create({
        header: 'Session Expired',
        message: 'Your session has expired.',
        subHeader: 'Code: ' + ErrorCode.SESSION_TIMEOUT.toString(),
        buttons: ['OK'],
        cssClass: 'system-alert',
      });

      alert.present();
    }
  }
}
