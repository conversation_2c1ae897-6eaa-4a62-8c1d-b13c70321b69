import { formatDate } from '@angular/common';
import { Component, Inject, Injector } from '@angular/core';
import { Directory, Encoding, Filesystem } from '@capacitor/filesystem';
import { AbstractFormComponent } from 'mobile-components';
import { LogPublisherConfig } from 'projects/lp-client-api/src/public-api';

@Component({
  selector: 'app-logs',
  templateUrl: 'logs.component.html',
  styleUrls: ['logs.component.scss'],
})
export class LogsComponent extends AbstractFormComponent<any> {
  logData: string = '';
  logItems: LogItemDisplay[] = [];
  logs: boolean = true;

  constructor(
    injector: Injector,
    @Inject('environment')
    private environment: any
  ) {
    super(injector);
    this.generateForm();
    this.getLogData('', false);
  }

  generateForm(): void {
    this._form = this._formBuilder.group({
      fileDate: [new Date()],
      showErrors: false,
    });
  }

  async search() {
    let fileDate: Date = this._form.controls['fileDate'].value;
    let showErrors: boolean = this._form.controls['showErrors'].value;

    this.getLogData(formatDate(fileDate, 'yyyy-MM-dd', 'en'), showErrors);
  }

  private async getLogData(fileDate: string, showErrors: boolean) {
    this.logItems = [];
    this.logs = false;

    if (!fileDate) {
      fileDate = formatDate(new Date(), 'yyyy-MM-dd', 'en');
    }

    let publishers: LogPublisherConfig[] =
      this.environment.lssConfig.logConfig.publishers;

    let filepublisher: LogPublisherConfig[] = publishers.filter((p) => {
      return p.loggerName === 'filestorage';
    });

    if (filepublisher) {
      console.log(`${filepublisher[0].loggerLocation}/log-${fileDate}.txt`);

      this.logData = await Filesystem.readFile({
        path: `${filepublisher[0].loggerLocation}/log-${fileDate}.txt`,
        directory: Directory.Documents,
        encoding: Encoding.UTF8,
      })
        .then((result) => {
          return result.data;
        })
        .catch((error) => {
          return '';
        });

      if (this.logData) {
        this.ceateLogItems(this.logData.split('\n').reverse(), showErrors);
        this.logs = true;
      } else {
        this.logs = false;
      }
    }
  }

  private ceateLogItems(logMessages: string[], showErrors: boolean) {
    console.log('showErrors', showErrors);
    logMessages.forEach((element, index) => {
      let tmpItem: string[] = element.split(' ');
      let itemSplit: string[] = tmpItem.splice(0, 4);
      let message: string = element.substring(
        element.indexOf(']') + 1,
        element.length
      );

      if (
        !showErrors ||
        (showErrors &&
          itemSplit[2] &&
          (itemSplit[2].toUpperCase() === 'ERROR' ||
            itemSplit[2].toUpperCase() === 'FATAL'))
      ) {
        this.logItems.push({
          timeStamp: itemSplit[0] + ' ' + itemSplit[1],
          level: itemSplit[2],
          method: itemSplit[3],
          message: message,
        });
      }
    });
  }

  public copyLog() {
    let selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = this.logData;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
  }
}

export interface LogItemDisplay {
  timeStamp?: String;
  level?: String;
  method?: string;
  message?: String;
}
