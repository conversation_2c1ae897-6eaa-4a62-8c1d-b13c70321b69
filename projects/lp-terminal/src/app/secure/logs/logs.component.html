<ion-header>
  <ion-toolbar>
    <ion-title>Device Logs</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding" color="light">
  <form [formGroup]="_form">
    <ion-item class="file-date" lines="none" fill="outline">
      <ion-icon slot="start" name="calendar-outline"></ion-icon>
      <ion-input
        label="Date"
        formControlName="fileDate"
        type="date"
        class="file-date"></ion-input>
      <ion-button slot="end" (click)="search()">Submit</ion-button>
    </ion-item>
    <ion-item lines="none">
      <ion-checkbox formControlName="showErrors" (ionChange)="search()">Show only errors</ion-checkbox>
    </ion-item>
  </form>
  <br/>
  <ion-list class="log-list">
    <ion-item lines="none">
      <h4 slot="start">Logs</h4>
      <ion-button *ngIf="logData" slot="end" fill="clear" (click)="copyLog()">Copy</ion-button>
    </ion-item>
    <ion-item *ngFor="let item of logItems">
      <ion-label class="ion-text-wrap log-item log-item-{{ item.level }}">
        <h3><b>{{ item.level }}</b> {{ item.timeStamp }}</h3>
        <h3></h3>
        <p>{{ item.message }}</p></ion-label>
    </ion-item>
    <ion-item *ngIf="!logs">
      <h3>No logs available</h3>
    </ion-item>
  </ion-list>
  
</ion-content>

