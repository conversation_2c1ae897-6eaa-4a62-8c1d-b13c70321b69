<ion-header>
    <ion-toolbar>
        <ion-buttons slot="start">
            <ion-button *ngIf="selectedSlip" (click)="reset()">< Back</ion-button>
            <ion-button *ngIf="!selectedSlip" (click)="back()">< Back</ion-button>
        </ion-buttons>
        <ion-title>Member Slips</ion-title>
    </ion-toolbar>
  </ion-header>
<ion-content class="ion-padding" color="light" *ngIf="!selectedSlip">
    <form class="form" [formGroup]="_form">
        <ion-item lines="none" fill="outline">
            <ion-icon slot="start" name="calendar-outline"></ion-icon>
            <ion-input
            label="Date"
            formControlName="printDate"
            type="date"
            class="file-date"></ion-input>
        </ion-item>
        <ion-item lines="none" fill="outline">
            <ion-icon slot="start" name="document-text"></ion-icon>
            <ion-input label="Invoice Number" labelPlacement="floating" type="text" formControlName="invoiceNumber"></ion-input>
        </ion-item>
        <ion-button expand="block" class="clay" type="submit" (click)="listSlips()">Search</ion-button>
        <ion-button expand="block" class="clay" (click)="cancel()">Cancel</ion-button>
    </form>
    <br/>
    <ion-list>
        <ion-item *ngFor="let slip of slips" detail="true" button (click)="selectSlip(slip)">
            <ion-label class="ion-text-wrap">
            <p>[<b>{{ slip.invoiceNumber }}</b>] {{slip.transactionType}} - {{slip.printDate}}</p></ion-label>
        </ion-item>
        <ion-item *ngIf="!slips || slips.length === 0" detail="true">
            No slips found
        </ion-item>
    </ion-list>
</ion-content>
<ion-content class="ion-padding" color="light" *ngIf="selectedSlip">
    <ion-grid>
        <!-- Device ID -->
        <ion-row>
            <ion-col size="6">
                <span class="setting-key">Transaction Type</span>:
            </ion-col>
            <ion-col size="6">
                <span class="setting-value">{{selectedSlip.transactionType}}</span>
            </ion-col>
        </ion-row>
        <!-- Invoice Number -->
        <ion-row>
            <ion-col size="6">
                <span class="setting-key">Invoice Number</span>:
            </ion-col>
            <ion-col size="6">
                <span class="setting-value">{{selectedSlip.invoiceNumber}}</span>
            </ion-col>
        </ion-row>
        <!-- Print Date -->
        <ion-row>
            <ion-col size="6">
                <span class="setting-key">Original Print Date</span>:
            </ion-col>
            <ion-col size="6">
                <span class="setting-value">{{selectedSlip.printDate}}</span>
            </ion-col>
        </ion-row>
    </ion-grid>
    <br/>
    <br/>
    <ion-grid>
        <ion-row>
            <ion-col>
                <ion-button expand="block" class="clay" (click)="printSlip('member')">Print Customer Slip</ion-button>
                <ion-button expand="block" class="clay" (click)="printSlip('store')">Print Store Slip</ion-button>
            </ion-col>
        </ion-row>
    </ion-grid>
</ion-content>