import { formatDate } from '@angular/common';
import { Component, Injector } from '@angular/core';
import { Router } from '@angular/router';
import {
  BasicProfile,
  LogService,
  MemberCommObject,
  Slip,
  SystemService,
  Terminal,
  TerminalService,
  WAPSlipConfig,
} from 'lp-client-api';
import { AbstractFormComponent } from 'mobile-components';
import { WapPrint } from 'wap-print';

@Component({
  selector: 'app-slips',
  templateUrl: 'slips.component.html',
  styleUrls: ['slips.component.scss'],
})
export class SlipsComponent extends AbstractFormComponent<any> {
  terminal!: Terminal;
  attendant?: BasicProfile | null;
  member?: BasicProfile;
  slips?: Slip[] = [];
  selectedSlip?: Slip;

  constructor(
    injector: Injector,
    private router: Router,
    private logService: LogService,
    private systemService: SystemService,
    private terminalService: TerminalService
  ) {
    super(injector);
    this.generateForm();
  }

  ionViewWillEnter(): void {
    if (history.state.data === undefined) {
      this.router.navigate(['/secure/home']); // Navigate back home if invalid state
    } else {
      this.member = history.state.data;
    }

    // Subscribe to the session terminal
    this.addGlobalSubscription(
      this.terminalService.terminal.subscribe(
        (value) => (this.terminal = value)
      )
    );
    // Subscribe to the session attendant
    this.addGlobalSubscription(
      this.terminalService.attendant.subscribe(
        (value) => (this.attendant = value)
      )
    );

    if (
      this.attendant === undefined ||
      this.attendant === null ||
      this.attendant.membershipNumber === undefined
    ) {
      this.router.navigate(['/secure/attendant']);
    }

    this.selectedSlip = undefined;
    this.listSlips();
  }

  generateForm(): void {
    this._form = this._formBuilder.group({
      printDate: [],
      invoiceNumber: [''],
    });
  }

  async listSlips() {
    this.slips = [];
    let printDate = this._form.controls['printDate'].value;
    if (!printDate || printDate === null) {
      printDate = formatDate(new Date(), 'yyyy-MM-dd', 'en-GB');
    }

    let memberCommObject: MemberCommObject = {};
    memberCommObject.apiId = this.terminal.apiId?.apiId;
    memberCommObject.membershipNumber = this.member!.membershipNumber?.trim();
    memberCommObject.uniqueId = this.systemService.getUniqueId(
      this.terminal.apiId!.secretStart,
      this.terminal.apiId!.secretEnd,
      this.member!.membershipNumber!.trim()
    );

    await this.terminalService
      .listSlips(
        this.terminal.terminalId,
        printDate,
        this._form.controls['invoiceNumber'].value,
        memberCommObject
      )
      .subscribe({
        next: (n) => {
          this.slips = n;
        },
        error: (e) => {
          this.logService.error('SlipsComponent.listSlips', e.message);
        },
        complete: () => {
          // Convert the raw JSON slip into the relevant WAPSlipData object
          this.slips?.forEach((s) => {
            s.slip = JSON.parse(s.slipRaw!);
          });
          this.logService.debug(
            'SlipsComponent.listSlips',
            'Slips: ',
            this.slips
          );
        },
      });
  }

  search() {}

  selectSlip(slip: Slip): void {
    this.selectedSlip = slip;
    console.log(slip);
  }

  async printSlip(type: string) {
    await this.showLoadingModal('Printing Slip');
    let printConfig: WAPSlipConfig[] | undefined = [];

    printConfig = this.terminalService.getTerminalSlip(
      this.terminal,
      type,
      this.selectedSlip?.transactionType === 'Redemption' ? 'REDM' : 'ACCR'
    );

    this.selectedSlip!.slip!.logo = this.terminal.slipLogobase64;

    let options = {
      printConfig: JSON.stringify(printConfig),
      printData: JSON.stringify(this.selectedSlip?.slip),
      result: false,
    };

    let printResult = await WapPrint.print(options);
    let slipState: boolean = printResult.result;
    this.logService.info(
      'TransactionComponent.printSlip',
      'Print result: ',
      slipState
    );

    this.dismissLoadingModal();
  }

  reset(): void {
    this.selectedSlip = undefined;
  }

  back(): void {
    this.router.navigate(['/secure/search'], {
      state: { data: this.member },
    });
  }

  cancel() {
    this.router.navigate(['/secure/search']);
  }
}
