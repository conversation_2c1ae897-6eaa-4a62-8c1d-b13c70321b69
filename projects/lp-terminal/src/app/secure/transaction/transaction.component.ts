import { formatDate } from '@angular/common';
import { Component, Injector } from '@angular/core';
import { Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AlertController, IonInput } from '@ionic/angular';
import {
  BasicProfile,
  IdleService,
  LoadTransactionRequest,
  LogService,
  MemberCommObject,
  MemberService,
  PartnerProduct,
  PartnerProductListRequest,
  PartnerService,
  Slip,
  SystemService,
  Terminal,
  TerminalService,
  TransactionReponse,
  WAPSlipConfig,
  WAPSlipData,
} from 'lp-client-api';
import { AbstractFormComponent } from 'mobile-components';
import { WapPrint } from 'wap-print';
import { ViewChild } from '@angular/core';
import { Device } from '@capacitor/device';

@Component({
  selector: 'transaction-home',
  templateUrl: 'transaction.component.html',
  styleUrls: ['transaction.component.scss'],
})
export class TransactionComponent extends AbstractFormComponent<LoadTransactionRequest> {
  terminal!: Terminal;
  attendant?: BasicProfile | null;
  basicProfile?: BasicProfile;
  loaded: boolean = false;
  transactionType?: string;
  authType?: string;
  authCode?: string;
  private pin?: string;
  pintAttempts: number = 0;
  amount: number = 0;
  originalBalance: number = 0;
  newBalance: number = 0;
  slipData: WAPSlipData = {};
  storeSlipState: boolean = false;
  partnerProducts?: PartnerProduct[];
  productAlertOptions = {
    header: 'Products',
    subHeader: 'Select a product',
    translucent: true,
  };
  uniqueSequence: number = 0;
  hasPrinter: boolean = true;

  @ViewChild('amount', { static: false }) amountInput!: IonInput;

  constructor(
    injector: Injector,
    public terminalService: TerminalService,
    private router: Router,
    private memberService: MemberService,
    private systemService: SystemService,
    private alertController: AlertController,
    private partnerService: PartnerService,
    private idleService: IdleService,
    private logService: LogService
  ) {
    super(injector);
    this.generateForm();
    this.printStatus();
  }

  ionViewWillEnter(): void {
    if (history.state.data === undefined) {
      this.router.navigate(['/secure/home']); // Navigate back home if invalid state
    } else {
      this.basicProfile = history.state.data;
      this.transactionType = history.state.transactionType;
    }

    this._formState = this._formStateType.create;
    this.loaded = false;
    this.pin = '';
    this.pintAttempts = 0;

    // Generate unique transaction sequence based on the timestamp
    var date = new Date();
    this.uniqueSequence =
      date.getHours() * 60 * 60 * 1000 +
      date.getMinutes() * 60 * 1000 +
      date.getSeconds() * 1000 +
      date.getMilliseconds();

    // Subscribe to the session terminal
    this.addGlobalSubscription(
      this.terminalService.terminal.subscribe(
        (value) => (this.terminal = value)
      )
    );
    // Subscribe to the session attendant
    this.addGlobalSubscription(
      this.terminalService.attendant.subscribe(
        (value) => (this.attendant = value)
      )
    );
    // Subscribe to the timeout
    this.addViewSubscription(
      this.idleService.timeout.subscribe((timeout) => {
        // 5 minutes timeout
        if (timeout >= 120) {
          this.cancel();
        }
      })
    );

    if (this.attendant?.membershipNumber === undefined) {
      this.router.navigate(['/secure/home']); // Navigate back home if no attendant is set
    }

    this.originalBalance =
      this.basicProfile?.baseUnits! +
      this.basicProfile?.bonusUnits! -
      (this.basicProfile?.usedUnits! + this.basicProfile?.expUnits!);

    this.generateForm();
  }

  async generateForm() {
    if (this.transactionType === 'Accrual') {
      if (this.terminal.calculationMethod === 'PERC') {
        this._form = this._formBuilder.group({
          amount: ['', [Validators.required]],
          receiptNumber: ['', [Validators.required]],
        });
      } else if (this.terminal.calculationMethod === 'PROD') {
        this._form = this._formBuilder.group({
          amount: ['', [Validators.required]],
          receiptNumber: ['', [Validators.required]],
          productCode: ['', [Validators.required]],
          quantity: ['', [Validators.required]],
        });
        await this.getPartnerProducts();
      }
    } else if (this.transactionType === 'Redemption') {
      this._form = this._formBuilder.group({
        amount: ['', [Validators.required]],
        receiptNumber: ['', [Validators.required]],
      });
    } else {
      // Default form - error if we got here
      this._form = this._formBuilder.group({
        amount: [''],
        receiptNumber: [''],
      });
    }
  }

  async printStatus() {
    let deviceInfo = await Device.getInfo();

    this.hasPrinter =
      deviceInfo.model == 'WPOS-3' ||
      deviceInfo.model == 'P5SE' ||
      deviceInfo.model == 'P5MAX' ||
      deviceInfo.model == 'P052'
        ? true
        : false;

    console.log('This is the device model: ', deviceInfo.model);
    console.log('This is the device print status: ', this.hasPrinter);
  }

  getPartnerProducts() {
    let productRequest: PartnerProductListRequest = {
      memberCommObject: {
        apiId: this.terminal.apiId?.apiId,
        membershipNumber: this.terminal.terminalId,
        uniqueId: this.systemService.getUniqueId(
          this.terminal.apiId!.secretStart,
          this.terminal.apiId!.secretEnd,
          this.terminal.terminalId
        ),
        terminalId: this.terminal.terminalId,
        productId: this.terminal.productId,
      },
      partnerId: this.terminal.partnerId,
    };

    let result: PartnerProduct[] = [];
    this.partnerService.getPartnerProducts(productRequest).subscribe({
      next: (n) => {
        result = n;
      },
      error: (n) => {
        result = n;
        this.logService.error(
          'TransactionComponent.getPartnerProducts',
          n.message
        );
      },
      complete: () => {
        this.partnerProducts = result;
      },
    });
  }

  async validateTransaction() {
    this.formData = this.getFormValues();

    if (
      this.formData &&
      this.basicProfile &&
      this.basicProfile.membershipNumber &&
      this.transactionType !== undefined
    ) {
      await this.showLoadingModal('Processing');

      if (this.transactionType === 'Redemption') {
        if (this.terminal.pinRequired) {
          // PIN is entered before submit of redemption
          this.logService.info(
            'TransactionComponent.validateTransaction',
            'PIN required'
          );
          this._formState = this._formStateType.auth;
          this.authType = 'PIN';
          this.dismissLoadingModal();
          return;
        } else if (this.terminal.otpRequired) {
          // OTP is entered after submission redemption, redemption in pending state until OTP entered
          this.logService.info(
            'TransactionComponent.validateTransaction',
            'OTP required'
          );
          this._formState = this._formStateType.auth;
          this.authType = 'OTP';
        }
      }

      await this.processTransaction();
    }
  }

  amountConvert(event: any): void {
    const target: HTMLIonInputElement = event.target;
    let input: any = target.value ?? '';

    input = input.replace('.', '');
    input = (input / 100).toFixed(2);

    this.amountInput.value = this.amount = input;

    this.focusCursor(event);
  }

  focusCursor(event: any): void {
    const target: HTMLIonInputElement = event.target;

    target.getInputElement().then((element) => {
      setTimeout(() => {
        element.type = 'text';
        element.setSelectionRange(element.value.length, element.value.length);
        element.type = 'number';
      }, 10);
    });
  }

  async processPin(pin: string) {
    this.formData!.pin = pin;
    this.processTransaction();
  }

  async processTransaction() {
    if (this.formData && this.basicProfile) {
      const alert = await this.alertController.create({
        header: 'Transaction Error',
        message:
          'Unable to process the transaction, please try again or contact the administrator.',
        buttons: ['OK'],
        cssClass: 'system-alert',
      });

      let amount: number = parseInt((this.amount * 100.0).toFixed(2));
      console.log('processTransaction amount: ' + amount);

      if (amount <= 0) {
        alert.message = 'Transaction value must be greater than zero.';
        alert.present();
        this.logService.error(
          'TransactionComponent.processTransaction',
          'Transaction value must be greater than zero: ' + amount
        );
        this.dismissLoadingModal();
        return;
      }

      let request: LoadTransactionRequest = {
        apiId: this.terminal.apiId?.apiId,
        terminalID: this.terminal.terminalId,
        authkey: this.systemService.getUniqueId(
          this.terminal.apiId!.secretStart,
          this.terminal.apiId!.secretEnd,
          this.basicProfile.membershipNumber!
        ),
        //productId: this.basicProfile?.productId,
        cardNumber: this.basicProfile?.membershipNumber,
        pin: this.formData!.pin,
        attendant: this.attendant?.membershipNumber,
        receiptDateTime: new Date(),
        sequenceNumber: this.uniqueSequence,
        type: this.transactionType as LoadTransactionRequest['type'],
        transactionDetail: [
          {
            linenumber: 1,
            quantity:
              this.transactionType === 'Accrual' &&
              this.terminal.calculationMethod === 'PROD'
                ? this._form.controls['quantity'].value
                : 1,
            productCode:
              this.transactionType === 'Accrual' &&
              this.terminal.calculationMethod === 'PROD'
                ? this._form.controls['productCode'].value
                : '',
            amount: amount,
            description: '',
          },
        ],
        amount: amount,
        receiptNumber: this.formData.receiptNumber,
      };

      let result: TransactionReponse;

      await this.memberService.loadTransaction(request).subscribe({
        next: (n) => {
          result = n;
        },
        error: (error) => {
          this.logService.error(
            'TransactionComponent.processTransaction',
            error.message
          );

          this.dismissLoadingModal();

          alert.message =
            error.error.detail !== undefined
              ? error.error.detail
              : 'An error occurred during transaction processing';

          if (error.error.detail === 'INVALID MEMBER PIN PROVIDED') {
            if (this.pintAttempts === 3) {
              alert.message =
                'An invalid pin number has been entered too many times, this transaction is unable to be completed.';
              alert.present();
              this.cancel();
              this.logService.error(
                'TransactionComponent.processTransaction',
                'Invalid PIN provided 3 times, cancelling transaction'
              );
            } else {
              this.pintAttempts += 1;
              this.pin = '';
              this._formState = this._formStateType.auth;
              alert.present();
              this.logService.error(
                'TransactionComponent.processTransaction',
                'Invalid PIN provided, attempts: ',
                this.pintAttempts
              );
              return;
            }
          }

          alert.present();
          this._formState = this._formStateType.fail;
        },
        complete: () => {
          this.dismissLoadingModal();

          if (result !== undefined) {
            this.completeTransaction(result);
          } else {
            this._formState = this._formStateType.fail;
            alert.present();
          }
        },
      });
    }
  }

  async cancel() {
    this.router.navigate(['/secure/search']);
  }

  async finish() {
    await this.terminalService.attendant.next(null);

    history.state.data = undefined;
    this.loaded = true;
    this._formState = this._formStateType.pass;
  }

  pinTimeout() {
    this._formState = this._formStateType.create;
  }

  private async completeTransaction(result: TransactionReponse) {
    this.newBalance =
      result.basicProfile?.baseUnits! +
      result.basicProfile?.bonusUnits! -
      (result.basicProfile?.usedUnits! + result.basicProfile?.expUnits!);
    this.formData!.receiptDateTime = new Date();
    this.basicProfile = result.basicProfile;
    this.authCode = result.authCode;
    await this.generateSlipData(result);
    await this.saveSlip();
    this.finish();
  }

  private async generateSlipData(result: TransactionReponse) {
    console.log('originalBalance', this.originalBalance);
    console.log('newBalance', this.newBalance);

    let timeStamp: Date =
      this.formData?.receiptDateTime === undefined
        ? new Date()
        : this.formData?.receiptDateTime;

    this.slipData.timeStamp = formatDate(
      timeStamp,
      'dd MMM yyyy HH:mm:ss',
      'en-GB'
    );
    this.slipData.logo = this.terminal.slipLogobase64;
    this.slipData.partnerName = this.terminal.partnerName;
    this.slipData.amount = this.amount;
    this.slipData.balance = result.basicProfile?.availUnits;
    this.slipData.currencyBalance = result.basicProfile?.availRands;
    this.slipData.invoiceNumber = this.formData!.receiptNumber;
    this.slipData.authCode = this.authCode;
    this.slipData.cardNumber = this.basicProfile?.membershipNumber;
    this.slipData.firstName = this.basicProfile?.firstname;
    this.slipData.lastName = this.basicProfile?.lastname;
    this.slipData.pcuMovement = Math.abs(
      this.originalBalance - this.newBalance
    );
    this.slipData.attendant =
      this.attendant?.firstname + ' ' + this.attendant?.lastname;
    this.slipData.attendantNumber = this.attendant?.membershipNumber;

    this.logService.info(
      'TransactionComponent.generateSlipData',
      'Slip has been generated for invoice: ',
      this.formData!.receiptNumber,
      this.slipData
    );
  }

  private async saveSlip() {
    let timeStamp: Date =
      this.formData?.receiptDateTime === undefined
        ? new Date()
        : this.formData?.receiptDateTime;

    let slip: Slip = {
      invoiceNumber: this.slipData.invoiceNumber!,
      printDate: timeStamp,
      slip: this.slipData,
    };

    let memberCommObject: MemberCommObject = {};
    memberCommObject.apiId = this.terminal.apiId?.apiId;
    memberCommObject.membershipNumber = this.basicProfile?.membershipNumber;
    memberCommObject.uniqueId = this.systemService.getUniqueId(
      this.terminal.apiId!.secretStart,
      this.terminal.apiId!.secretEnd,
      this.basicProfile?.membershipNumber!
    );

    await this.terminalService
      .saveSlip(
        this.terminal.terminalId,
        slip,
        this.transactionType!,
        this.attendant?.membershipNumber!,
        memberCommObject
      )
      .subscribe({
        next: (n) => {},
        error: (error) => {
          this.logService.error('TransactionComponent.saveSlip', error);
        },
        complete: () => {},
      });
  }

  async printSlip(type: string) {
    await this.showLoadingModal('Printing Slip');
    let printConfig: WAPSlipConfig[] | undefined = [];

    printConfig = this.terminalService.getTerminalSlip(
      this.terminal,
      type,
      this.transactionType === 'Redemption' ? 'REDM' : 'ACCR'
    );

    let options = {
      printConfig: JSON.stringify(printConfig),
      printData: JSON.stringify(this.slipData),
      result: false,
    };

    let printResult = await WapPrint.print(options);
    this.storeSlipState = printResult.result;
    this.logService.info(
      'TransactionComponent.printSlip',
      'Print result: ',
      printResult.result
    );
    this.dismissLoadingModal();
  }

  back(): void {
    this.router.navigate(['/secure/search'], {
      state: { data: this.basicProfile },
    });
  }
}
