<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="back()">< Back</ion-button>
    </ion-buttons>
    <ion-title *ngIf="!loaded && transactionType === 'Accrual'">Earn Points</ion-title>
    <ion-title *ngIf="!loaded && transactionType === 'Redemption'">Pay with Points</ion-title>
    <ion-title *ngIf="loaded">Transaction Complete</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding" *ngIf="terminal" color="light">
  <ion-grid [fixed]="true">
    <ion-row *ngIf="!loaded && _formState === _formStateType.create">
      <ion-col>
        <ion-grid>
          <ion-row>
            <ion-col>
              <form class="form" [formGroup]="_form" *ngIf="basicProfile">
                <ion-item lines="none">
                  <ion-icon slot="start" name="person"></ion-icon>
                  <ion-input label="Membership Number" labelPlacement="floating" type="text" value="{{basicProfile.membershipNumber}}" [disabled]="true"></ion-input>
                </ion-item>
                <ion-item lines="none" fill="outline" *ngIf="terminal.calculationMethod === 'PROD' && partnerProducts">
                  <ion-select placeholder="Select Product" formControlName="productCode" [interfaceOptions]="productAlertOptions" interface="alert" okText="Select Product" cancelText="Cancel">
                    <ion-select-option *ngFor="let item of partnerProducts;" value="{{item.productCode}}">{{item.productName}}</ion-select-option>
                  </ion-select>
                </ion-item>
                <ion-item lines="none" *ngIf="isFormComponentInvalid('productCode')">
                  <div *ngFor="let error of getComponentErrors('productCode')" class="validator-error">
                      {{ error }}
                  </div>
                </ion-item>
                <ion-item lines="none" fill="outline" *ngIf="transactionType === 'Accrual' && terminal.calculationMethod === 'PROD'">
                  <ion-icon slot="start" name="pricetags-outline"></ion-icon>
                  <ion-input label="Quantity" labelPlacement="floating" type="number" formControlName="quantity"></ion-input>
                </ion-item>
                <ion-item lines="none" *ngIf="isFormComponentInvalid('quantity')">
                  <div *ngFor="let error of getComponentErrors('quantity')" class="validator-error">
                      {{ error }}
                  </div>
                </ion-item>
                <ion-item lines="none" fill="outline">
                  <ion-icon slot="start" name="document-text"></ion-icon>
                  <ion-input #receiptNumber label="Invoice Number" labelPlacement="floating" type="text" formControlName="receiptNumber" (keyup.enter)="gotoNextField(amount)"></ion-input>
                </ion-item>
                <ion-item lines="none" *ngIf="isFormComponentInvalid('receiptNumber')">
                  <div *ngFor="let error of getComponentErrors('receiptNumber')" class="validator-error">
                      {{ error }}
                  </div>
                </ion-item>
                <ion-item lines="none" fill="outline">
                  <ion-icon slot="start" name="cash"></ion-icon>
                  <ion-input #amount id="amount" label="Transaction Value (R)" labelPlacement="floating" type="number" formControlName="amount" (ionInput)="amountConvert($event)" (ionFocus)="focusCursor($event)"></ion-input>
                </ion-item>
                <ion-item lines="none" *ngIf="isFormComponentInvalid('amount')">
                  <div *ngFor="let error of getComponentErrors('amount')" class="validator-error">
                      {{ error }}
                  </div>
                </ion-item>
                <ion-button expand="block" class="clay" type="submit" [disabled]="!isFormValid()" (click)="validateTransaction()">Submit</ion-button>
              </form>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <ion-button expand="block" class="clay" (click)="cancel()">Cancel</ion-button>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-col>
    </ion-row>
    <ion-row class="pin-row" *ngIf="_formState === _formStateType.auth && authType === 'PIN'">
      <ion-col>
        <lp-pin header="PIN Authorization" subheader="Please enter your pin code" invalidLabel="Invalid pin" [pinLength]="5" [timeout]="120"
        (cancel)="cancel()" (timedOut)="pinTimeout()" (submit)="processPin($event)"></lp-pin>
      </ion-col>
    </ion-row>
    <ion-row *ngIf="_formState === _formStateType.auth && authType === 'OTP'">
      OTP required
    </ion-row>
    <ion-row *ngIf="_formState === _formStateType.pass && basicProfile !== undefined && transactionType === 'Accrual'">
      <ion-col>
        <ion-grid>
          <ion-row>
            <ion-col>
              <div class="" style="text-align: center;">
                <p>The transaction was successfully processed to the member's account.</p>
                <br/>
                <p>{{basicProfile.firstname}} {{basicProfile.lastname}} - {{basicProfile.membershipNumber}}</p>
                <br/>
                <p>Please note that certain transactions may require approval and Points earned reflect within 24 hours.</p>
              </div>
            </ion-col>
          </ion-row>
          <ion-row *ngIf="hasPrinter">
            <ion-col>
              <ion-button expand="block" class="clay" (click)="printSlip('member')">Print Customer Slip</ion-button>
              <ion-button expand="block" class="clay" (click)="printSlip('store')">Print Store Slip</ion-button>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <ion-button expand="block" class="clay" [routerLink]="['/secure/home']">Finish</ion-button>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-col>
    </ion-row>
    <ion-row *ngIf="_formState === _formStateType.pass && basicProfile !== undefined && transactionType === 'Redemption'">
      <ion-col>
        <ion-grid>
          <ion-row>
            <ion-col>
              <div class="" style="text-align: center;">
                <p>Points were successfully deducted from the members account.</p>
                <br/>
                <p>{{basicProfile.firstname}} {{basicProfile.lastname}} - {{basicProfile.membershipNumber}}</p>
                <p>Authorization Code: {{authCode}}</p>
              </div>
            </ion-col>
          </ion-row>
          <ion-row *ngIf="hasPrinter">
            <ion-col>
              <ion-button expand="block" class="clay" (click)="printSlip('member')">Print Customer Slip</ion-button>
              <ion-button expand="block" class="clay" (click)="printSlip('store')">Print Store Slip</ion-button>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <ion-button expand="block" class="clay" [routerLink]="['/secure/home']">Finish</ion-button>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>