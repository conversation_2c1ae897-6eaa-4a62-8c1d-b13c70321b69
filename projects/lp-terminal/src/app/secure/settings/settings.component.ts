import { Component, Injector } from '@angular/core';
import { AlertController } from '@ionic/angular';
import { IdleService, Terminal, TerminalService } from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';
import { App } from '@capacitor/app';
import { Router } from '@angular/router';

@Component({
  selector: 'app-settings',
  templateUrl: 'settings.component.html',
  styleUrls: ['settings.component.scss'],
})
export class SettingsComponent extends AbstractComponent {
  terminal!: Terminal;
  hasAccess: boolean = false;
  eventState: string = '';
  invalid: boolean = false;

  constructor(
    injector: Injector,
    public terminalService: TerminalService,
    private alertController: AlertController,
    protected readonly router: Router,
    private idleService: IdleService
  ) {
    super(injector);
  }

  ionViewWillEnter() {
    this.addGlobalSubscription(
      this.terminalService.terminal.subscribe(
        (value) => (this.terminal = value)
      )
    );

    this.hasAccess = false;
    this.invalid = false;
    // Subscribe to the timeout
    this.addViewSubscription(
      this.idleService.timeout.subscribe((timeout) => {
        // 5 minutes timeout
        if (timeout >= 120) {
          this.cancel();
        }
      })
    );
  }

  async showExitPrompt() {
    const alert = await this.alertController.create({
      header: 'Device Reset',
      message: 'Please confirm that you would like to reset this device?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
        },
        {
          text: 'OK',
          role: 'confirm',
          handler: () => {
            this.reset();
          },
        },
      ],
      cssClass: 'system-alert',
    });

    await alert.present();
  }

  async reset() {
    await this.terminalService.logout(false);
    this.router.navigate(['/public']);
  }

  async showRefreshPrompt() {
    const alert = await this.alertController.create({
      header: 'Device Refresh',
      message:
        'Please confirm that you would like to refresh the settings for this device, the application will automatically close to enable the refresh.',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
        },
        {
          text: 'OK',
          role: 'confirm',
          handler: () => {
            this.refresh();
          },
        },
      ],
      cssClass: 'system-alert',
    });

    await alert.present();
  }

  async refresh() {
    this.showLoadingModal('Refreshing');
    await this.terminalService.authenticateTerminal(
      this.terminal.terminalId,
      this.terminal.deviceId,
      this.terminal.pin
    );
    this.dismissLoadingModal();
    App.exitApp();
  }

  async cancel() {
    this.router.navigate(['/secure/home']);
  }

  async validatePin(pin: string) {
    if (pin === this.terminal.pin) {
      this.hasAccess = true;
    } else {
      this.invalid = true;
    }
  }

  public viewLogs(): void {
    this.router.navigate(['/secure/logs']);
  }
}
