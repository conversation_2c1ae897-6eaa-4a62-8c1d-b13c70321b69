<ion-content class="ion-padding" *ngIf="!hasAccess">
  <lp-pin header="Device Authorization" subheader="Please enter the device PIN" invalidLabel="Invalid PIN" 
    [pinLength]="5" [invalid]="invalid" [eventState]="eventState"
    (cancel)="cancel()" (timedOut)="cancel()" (submit)="validatePin($event)"></lp-pin>
</ion-content>
<ion-header *ngIf="hasAccess">
  <ion-toolbar>
    <ion-title>Terminal Settings</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding" *ngIf="hasAccess">
  <ion-grid>
    <!-- Device ID -->
    <ion-row>
      <ion-col size="4">
        <span class="setting-key">Device ID</span>:
      </ion-col>
      <ion-col size="8">
        <span class="setting-value">{{terminal.deviceId}}</span>
      </ion-col>
    </ion-row>
    <!-- API ID -->
    <ion-row>
      <ion-col size="4">
        <span class="setting-key">API ID</span>:
      </ion-col>
      <ion-col size="8">
        <span class="setting-value">{{terminal.apiId!.apiId}}</span>
      </ion-col>
    </ion-row>
    <!-- Terminal ID -->
    <ion-row>
      <ion-col size="4">
        <span class="setting-key">Terminal ID</span>:
      </ion-col>
      <ion-col size="8">
        <span class="setting-value">{{terminal.terminalId}}</span>
      </ion-col>
    </ion-row>
    <!-- Store -->
    <ion-row>
      <ion-col size="4">
        <span class="setting-key">Store</span>:
      </ion-col>
      <ion-col size="8">
        <span class="setting-value">{{terminal.partnerName}}</span>
      </ion-col>
    </ion-row>
    <!-- Floor Limit -->
    <ion-row>
      <ion-col size="4">
        <span class="setting-key">Floor Limit</span>:
      </ion-col>
      <ion-col size="8">
        <span class="setting-value">R{{terminal.floorLimit}}</span>
      </ion-col>
    </ion-row>
    <!-- Accrual -->
    <ion-row>
      <ion-col size="4">
        <span class="setting-key">Accrual</span>:
      </ion-col>
      <ion-col size="8">
        <span class="setting-value">{{terminal.allowAccrual}}</span>
      </ion-col>
    </ion-row>
    <!-- Redemption -->
    <ion-row>
      <ion-col size="4">
        <span class="setting-key">Redemption</span>:
      </ion-col>
      <ion-col size="8">
        <span class="setting-value">{{terminal.allowRedemption}}</span>
      </ion-col>
    </ion-row>
    <!-- Registration -->
    <ion-row>
      <ion-col size="4">
        <span class="setting-key">Registration</span>:
      </ion-col>
      <ion-col size-md="8">
        <span class="setting-value">{{terminal.allowRegistration}}</span>
      </ion-col>
    </ion-row>
    <ion-row class="ion-justify-content-center" style="padding-top: 20px;">
      <ion-col size="auto">
        <ion-button expand="block" class="clay" (click)="viewLogs()">View Logs</ion-button>
        <ion-button expand="block" class="clay" (click)="showRefreshPrompt()">Refresh Settings</ion-button>
        <ion-button expand="block" class="clay" (click)="showExitPrompt()">Reset Device</ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>