import { Component, Injector } from '@angular/core';
import {
  BasicProfile,
  CustomValidators,
  IdleService,
  LPMemberEntityTools,
  LogService,
  MemberProfile,
  MemberService,
  SystemService,
  Terminal,
  TerminalService,
} from 'lp-client-api';
import { AbstractFormComponent } from 'mobile-components';
import { AbstractControlOptions, Validators } from '@angular/forms';
import { AlertController } from '@ionic/angular';
import { Router } from '@angular/router';
import {
  BarcodeScanner,
  SupportedFormat,
} from '@capacitor-community/barcode-scanner';
import { CardService, CardTracks } from '../../services/cardreader.service';

@Component({
  selector: 'register-home',
  templateUrl: 'register.component.html',
  styleUrls: ['register.component.scss'],
})
export class RegisterComponent extends AbstractFormComponent<MemberProfile> {
  terminal!: Terminal;
  registerComplete: boolean = false;
  attendant?: BasicProfile | null;
  private cardTracks!: CardTracks;

  constructor(
    injector: Injector,
    private router: Router,
    public terminalService: TerminalService,
    private memberService: MemberService,
    private systemService: SystemService,
    private alertController: AlertController,
    private idleService: IdleService,
    private logService: LogService,
    private cardService: CardService
  ) {
    super(injector);
    this.generateForm();
  }

  ionViewWillEnter(): void {
    this._formState = this._formStateType.create;
    this.registerComplete = false;

    // Subscribe to the session terminal
    this.addGlobalSubscription(
      this.terminalService.terminal.subscribe(
        (value) => (this.terminal = value)
      )
    );
    // Subscribe to the session attendant
    this.addGlobalSubscription(
      this.terminalService.attendant.subscribe(
        (value) => (this.attendant = value)
      )
    );
    // Subscribe to the timeout
    this.addViewSubscription(
      this.idleService.timeout.subscribe((timeout) => {
        if (timeout >= 120) {
          this.cancel();
        }
      })
    );
    // Subscribe to the magnetic card reader results
    this.addGlobalSubscription(
      this.cardService.cardTracks.subscribe((value) => {
        this.cardTracks = value;

        console.log('cardTracks', this.cardTracks);
        if (this.cardTracks.track2) {
          let pan = this.cardTracks.track2;
          if (pan.includes('=')) {
            pan = pan.substring(0, pan.indexOf('='));
          }
          console.log('pan', pan);
          this._form.patchValue({ cardNumber: pan });
        }
      })
    );

    this.cardService.readMagStripe(120);
  }

  override ionViewDidLeave(): void {
    super.ionViewDidLeave();
    this.reset();
  }

  ionViewWillLeave(): void {
    this.reset();
  }

  ngOnInit(): void {
    this._form.get('passortNum')?.valueChanges.subscribe(() => {
      this._form.get('issueCountry')?.updateValueAndValidity();
      this._form.get('expiryDate')?.updateValueAndValidity();
    });
  }

  reset() {
    this._form.reset({
      mpacc: '',
      cardNumber: '',
      givenNames: '',
      surname: '',
      nationalIdNum: '',
      emailAddress: '',
      mobileNumber: '',
      passortNum: '',
      issueCountry: '',
      expiryDate: '',
    });
    this.registerComplete = false;
    this.cardService.clearCardTracks();
    this.cardService.closeCardReader();
  }

  generateForm(): void {
    this._form = this._formBuilder.group(
      {
        mpacc: [''],
        cardNumber: [''],
        givenNames: ['', [Validators.required]],
        surname: ['', [Validators.required]],
        nationalIdNum: [''],
        passortNum: [''],
        issueCountry: [
          '',
          [CustomValidators.requiredIfValidator('passortNum')],
        ],
        expiryDate: ['', [CustomValidators.requiredIfValidator('passortNum')]],
        emailAddress: [
          '',
          Validators.compose([
            Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+.[a-z]{2,4}$'),
          ]),
        ],
        mobileNumber: [
          LPMemberEntityTools.getIONTelephoneFromLPTel(
            LPMemberEntityTools.getEmptyPhone('CELL')
          ),
          [Validators.required],
        ],
      },
      {
        validators: Validators.compose([
          CustomValidators.atLeastOneRequired(['nationalIdNum', 'passortNum']),
        ]),
      } as AbstractControlOptions
    );
  }

  passportMinDate() {
    let todayDate = new Date();
    todayDate.setDate(todayDate.getDate() + 1);
    return todayDate.toISOString().split('T')[0];
  }
  async startScan() {
    await BarcodeScanner.hideBackground();
    document.body.classList.add('qrscanner'); // add the qrscanner class to body
    const result = await BarcodeScanner.startScan({
      targetedFormats: [SupportedFormat.CODE_128],
    });
    document.body.classList.remove('qrscanner'); // remove the qrscanner from the body
    if (result.hasContent) {
      this._form.patchValue({
        cardNumber: result.content,
      });
      this.logService.info(
        'RegisterComponent.startScan',
        'Membership card has been scanned: ',
        result.content
      );
    }
  }
  async scanBarcode(): Promise<void> {
    const status = await BarcodeScanner.checkPermission({ force: true });
    if (status.granted) {
      await this.startScan();
    }
  }

  async cancel() {
    this.reset();
    this.router.navigate(['/secure/home']);
  }

  async register() {
    this.formData = this.getFormValues();
    let cardNumber: string = this._form.controls['cardNumber'].value;

    let memberProfile: MemberProfile = {
      apiId: this.terminal.apiId?.apiId!,
      uniqueId: this.systemService.getUniqueId(
        this.terminal.apiId!.secretStart,
        this.terminal.apiId!.secretEnd,
        cardNumber
      ),
      //productId: this.terminal.productId,
    };

    memberProfile.terminalId = this.terminal.terminalId;
    memberProfile.givenNames = this.formData?.givenNames;
    memberProfile.surname = this.formData?.surname;
    memberProfile.nationalIdNum = this.formData?.nationalIdNum;
    memberProfile.emailAddress = this.formData?.emailAddress;
    memberProfile.personTelephone = [
      {
        telephoneType: 'CELL',
        countryCode: this._form.controls['mobileNumber'].value['dialCode'],
        telephoneNumber: this._form.controls['mobileNumber'].value[
          'nationalNumber'
        ].replace(/\s/g, ''),
      },
    ];
    memberProfile.membershipNumber = cardNumber; // pre-printed card enrollment
    memberProfile.userId = this.attendant?.membershipNumber?.trim();
    memberProfile.passortNum = this.formData?.passortNum;
    memberProfile.issueCountry = this.formData?.issueCountry;

    if (this.formData?.expiryDate) {
      memberProfile.expiryDate = this.formData?.expiryDate;
    }

    let result: any;
    const alert = await this.alertController.create({
      header: 'Registration Error',
      message: '',
      buttons: ['OK'],
      cssClass: 'system-alert',
    });

    await this.showLoadingModal('Registering');

    if (this.formData) {
      await this.memberService.register(memberProfile).subscribe({
        next: (n) => {
          result = n;
          this.logService.info(
            'RegisterComponent.register',
            'Registration: ',
            JSON.stringify(result)
          );
        },
        error: (e) => {
          this.logService.error('RegisterComponent.register', e.message);
          this.dismissLoadingModal();
          this._formState = this._formStateType.fail;
          alert.message = e.error.detail;
          alert.present();
        },
        complete: () => {
          this.dismissLoadingModal();
          this._formState = this._formStateType.pass;
          this.registerComplete = true;
        },
      });
    }
  }
}
