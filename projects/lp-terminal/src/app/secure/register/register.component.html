<ion-header>
  <ion-toolbar>
    <ion-title>Member Registration</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding" *ngIf="!registerComplete" color="light">
  <ion-grid [fixed]="true">
    <ion-row>
      <ion-col>
        <ion-button expand="block" class="clay" type="submit" (click)="scanBarcode()">
          <ion-icon slot="start" name="camera"></ion-icon>
          Scan Membership Card
        </ion-button>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <div class="header-divider"></div>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <form class="form" [formGroup]="_form">
          <ion-item lines="none" fill="outline">
            <ion-icon slot="start" name="card-outline"></ion-icon>
            <ion-input label="Card Number" labelPlacement="floating" formControlName="cardNumber" [readonly]="true" [clearInput]="true" (keyup.enter)="gotoNextField(giveNames)"></ion-input>
          </ion-item>
          <ion-item lines="none" fill="outline">
            <ion-icon slot="start" name="person"></ion-icon>
            <ion-input #giveNames label="First Name/s *" labelPlacement="floating" type="text" formControlName="givenNames" (keyup.enter)="gotoNextField(surname)"></ion-input>
          </ion-item>
          <ion-item lines="none" *ngIf="isFormComponentInvalid('givenNames')">
              <div *ngFor="let error of getComponentErrors('givenNames')" class="validator-error">
                  {{ error }}
              </div>
          </ion-item>
          <ion-item lines="none" fill="outline">
              <ion-icon slot="start" name="person"></ion-icon>
              <ion-input #surname label="Last Name *" labelPlacement="floating" type="text" formControlName="surname" (keyup.enter)="gotoNextField(nationalIdNum)"></ion-input>
          </ion-item>
          <ion-item lines="none" *ngIf="isFormComponentInvalid('surname')">
              <div *ngFor="let error of getComponentErrors('surname')" class="validator-error">
                  {{ error }}
              </div>
          </ion-item>
          <ion-item lines="none" fill="outline">
            <ion-icon slot="start" name="finger-print-outline"></ion-icon>
            <ion-input #nationalIdNum id="nationalIdNum" label="ID Number" labelPlacement="floating" type="text" inputmode="numeric" formControlName="nationalIdNum" (keyup.enter)="gotoNextField(passortNum)"></ion-input>
          </ion-item>
          <ion-item lines="none" *ngIf="isFormComponentInvalid('nationalIdNum')">
              <div *ngFor="let error of getComponentErrors('nationalIdNum')" class="validator-error">
                  {{ error }}
              </div>
          </ion-item>
          
          <ion-item lines="none" fill="outline">
            <ion-icon slot="start" name="finger-print-outline"></ion-icon>
            <ion-input #passortNum label="Passport Number" labelPlacement="floating" type="text" inputmode="text" formControlName="passortNum" (keyup.enter)="gotoNextField(issueCountry)"></ion-input>
          </ion-item>
          <ion-item lines="none" *ngIf="isFormComponentInvalid('passortNum')">
              <div *ngFor="let error of getComponentErrors('passortNum')" class="validator-error">
                  {{ error }}
              </div>
          </ion-item>

          <ion-item>
            <ion-icon slot="start" name="location-outline"></ion-icon>
            <ion-select #issueCountry label="Issue Country" labelPlacement="floating" formControlName="issueCountry" placeholder="Please select..">
              <ion-select-option *ngFor="let codeItem of getCodeList('LAND') | async" [value]="codeItem.codeId" >{{ codeItem.description }}</ion-select-option>
            </ion-select>
          </ion-item>
          <ion-item lines="none" *ngIf="isFormComponentInvalid('issueCountry')">
            <div *ngFor="let error of getComponentErrors('issueCountry')" class="validator-error">
                {{ error }}
            </div>
          </ion-item>

          <ion-item id="open-date-input" lines="inset">
            <ion-icon slot="start" name="calendar-outline"></ion-icon>
            <ion-input #expiryDate label="Expiry Date" labelPlacement="floating" formControlName="expiryDate" type="date" [min]="passportMinDate()"></ion-input>
          </ion-item>
          <ion-item lines="none" *ngIf="isFormComponentInvalid('expiryDate')">
            <div *ngFor="let error of getComponentErrors('expiryDate')" class="validator-error">
                {{ error }}
            </div>
          </ion-item>

          <ion-item lines="none" fill="outline">
            <ion-icon slot="start" name="finger-print-outline"></ion-icon>
            <ion-input #emailAddress label="Email" labelPlacement="floating" type="email" formControlName="emailAddress" (keyup.enter)="gotoNextField(mobileNumber)"></ion-input>
          </ion-item>
          <ion-item lines="none" *ngIf="isFormComponentInvalid('emailAddress')">
              <div *ngFor="let error of getComponentErrors('emailAddress')" class="validator-error">
                  Invalid email address
              </div>
          </ion-item>
          <ion-item lines="none" fill="outline">
            <ion-label position="stacked">Mobile Number *</ion-label>
            <ion-intl-tel-input #mobileNumber id="phone-number" name="phone-number" formControlName="mobileNumber" [enableAutoCountrySelect]="false" [defaultCountryiso]="'za'" [onlyCountries]="['za']"></ion-intl-tel-input>
          </ion-item>
          <ion-item lines="none" *ngIf="isFormComponentInvalid('mobileNumber')">
              <div *ngFor="let error of getComponentErrors('mobileNumber')" class="validator-error">
                  {{ error }}
              </div>
          </ion-item>
          <br/>
          <ion-button expand="block" class="clay" type="submit" [disabled]="!isFormValid()" (click)="register()">
            <ion-icon slot="start" name="person-add-outline"></ion-icon>
            Register
          </ion-button>
          <ion-button expand="block" class="clay" (click)="cancel()">Cancel</ion-button>
        </form>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
<ion-content class="signup" *ngIf="registerComplete">
  <div class="state-success">
    <div class="success-text">
        <h3>Register Complete!</h3>
        <p>You will receive additional communication containing all your account details</p>
        <ion-button [routerLink]="['/secure/home']">FINISH</ion-button>
    </div>
</div>
</ion-content>