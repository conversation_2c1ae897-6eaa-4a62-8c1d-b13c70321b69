import { Component, Injector } from '@angular/core';
import { Router } from '@angular/router';
import { App } from '@capacitor/app';
import { AbstractComponent } from 'mobile-components';

@Component({
  selector: 'app-tabs',
  templateUrl: 'app-tabs.page.html',
  styleUrls: ['app-tabs.page.scss'],
})
export class AppTabsPage extends AbstractComponent {
  constructor(injector: Injector, protected readonly router: Router) {
    super(injector);
  }

  closeApp() {
    App.exitApp();
  }
}
