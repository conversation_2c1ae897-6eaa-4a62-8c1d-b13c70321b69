<ion-tabs>
  <ion-tab-bar slot="bottom" mode="ios">
    <ion-tab-button tab="home" #home>
      <ion-icon [name]="home.selected ? 'home' : 'home-outline'"></ion-icon>
      <ion-label>Home</ion-label>
    </ion-tab-button>
    <ion-tab-button tab="settings" #settings>
      <ion-icon [name]="'settings-outline'"></ion-icon>
      <ion-label>Settings</ion-label>
    </ion-tab-button>
    <ion-tab-button (click)="closeApp()">
      <ion-icon [name]="'exit-outline'"></ion-icon>
      <ion-label>Exit</ion-label>
    </ion-tab-button>
  </ion-tab-bar>

  <div class="indicator"></div>
</ion-tabs>
