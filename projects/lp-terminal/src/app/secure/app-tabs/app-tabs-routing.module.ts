import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from '../home/<USER>';
import { AppTabsPage } from './app-tabs.page';
import { TerminalGuardService } from 'lp-client-api';
import { SearchComponent } from '../search/search.component';
import { TransactionComponent } from '../transaction/transaction.component';
import { AttendantComponent } from '../attendant/attendant.component';
import { RegisterComponent } from '../register/register.component';
import { SettingsComponent } from '../settings/settings.component';
import { AttendantHomeComponent } from '../attendant-home/attendant-home.component';
import { LogsComponent } from '../logs/logs.component';
import { SlipsComponent } from '../slips/slips.component';
import { GiftCardComponent } from '../gift-card/gift-card.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full',
  },
  {
    path: '',
    component: AppTabsPage,
    children: [
      {
        path: 'home',
        component: HomeComponent,
        canActivate: [TerminalGuardService],
      },
      {
        path: 'attendant',
        component: AttendantComponent,
        canActivate: [TerminalGuardService],
      },
      {
        path: 'attendant-home',
        component: AttendantHomeComponent,
        canActivate: [TerminalGuardService],
      },
      {
        path: 'search',
        component: SearchComponent,
        canActivate: [TerminalGuardService],
      },
      {
        path: 'transaction',
        component: TransactionComponent,
        canActivate: [TerminalGuardService],
      },
      {
        path: 'register',
        component: RegisterComponent,
        canActivate: [TerminalGuardService],
      },
      {
        path: 'settings',
        component: SettingsComponent,
        canActivate: [TerminalGuardService],
      },
      {
        path: 'logs',
        component: LogsComponent,
        canActivate: [TerminalGuardService],
      },
      {
        path: 'slips',
        component: SlipsComponent,
        canActivate: [TerminalGuardService],
      },
      {
        path: 'gift-card',
        component: GiftCardComponent,
        canActivate: [TerminalGuardService],
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
})
export class TabsPageRoutingModule {}
