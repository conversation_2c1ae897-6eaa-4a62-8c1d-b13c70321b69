import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { IonicModule } from '@ionic/angular';
import { ComponentsModule } from 'mobile-components';
import { AttendantComponent } from './attendant/attendant.component';
import { HomeComponent } from './home/<USER>';
import { SearchComponent } from './search/search.component';
import { SecureRoutingModule } from './secure-routing.module';
import { TransactionComponent } from './transaction/transaction.component';
import { RegisterComponent } from './register/register.component';
import { SettingsComponent } from './settings/settings.component';
import { IonIntlTelInputModule } from 'third-party-fix';
import { AttendantHomeComponent } from './attendant-home/attendant-home.component';
import { LogsComponent } from './logs/logs.component';
import { SlipsComponent } from './slips/slips.component';
import { GiftCardComponent } from './gift-card/gift-card.component';

@NgModule({
  declarations: [
    HomeComponent,
    SearchComponent,
    TransactionComponent,
    AttendantComponent,
    RegisterComponent,
    SettingsComponent,
    AttendantHomeComponent,
    LogsComponent,
    SlipsComponent,
    GiftCardComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    IonIntlTelInputModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    GoogleMapsModule,
    SecureRoutingModule,
    ComponentsModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SecureModule {}
