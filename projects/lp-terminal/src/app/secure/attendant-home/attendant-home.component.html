<ion-content class="ion-padding" *ngIf="terminal" color="light">
  <ion-grid>
    <ion-row class="home-logo">
      <ion-col class="device-logo">
          <img src="{{terminalService.getLogo('../../../assets/images/lp_logo.png')}}" />
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-button expand="block" class="clay" [routerLink]="['/secure/search']">
          <ion-icon slot="start" name="search"></ion-icon>
          Search
        </ion-button>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-button expand="block" class="clay" [routerLink]="['/secure/register']" *ngIf="terminal.allowRegistration">
          <ion-icon slot="start" name="person-add-outline"></ion-icon>
          Register
        </ion-button>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-button expand="block" class="clay" [routerLink]="['/secure/gift-card']" *ngIf="terminal.clientId.toLowerCase().startsWith('rmic')">
          <ion-icon slot="start" name="gift-outline"></ion-icon>
          Gift Card
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>