ion-button {
    height: 70px;
    width: 80vw;
    font-size: 18px;
    margin: 10px 0 10px 0;
}

ion-grid {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
}

.home-logo {
    padding-bottom: 50px;
}

@media only screen and (max-width: 450px) {
    ion-button {
        margin: 4px 0 4px 0;
        height: 65px;
        width: 75vw;
    }

    .home-logo {
        padding-bottom: 0px;
    }
}