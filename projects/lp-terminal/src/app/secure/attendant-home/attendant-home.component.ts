import { Component, Injector } from '@angular/core';
import { Router } from '@angular/router';
import {
  IdleService,
  LssConfig,
  Terminal,
  TerminalService,
} from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';

@Component({
  selector: 'app-attendant-home',
  templateUrl: 'attendant-home.component.html',
  styleUrls: ['attendant-home.component.scss'],
})
export class AttendantHomeComponent extends AbstractComponent {
  allowRegistration = false;
  terminal!: Terminal;

  constructor(
    injector: Injector,
    protected readonly router: Router,
    private lssConfig: LssConfig,
    public terminalService: TerminalService,
    private idleService: IdleService
  ) {
    super(injector);
  }

  override ionViewDidEnter() {
    super.ionViewDidEnter();

    this.addGlobalSubscription(
      this.terminalService.terminal.subscribe(
        (value) => (this.terminal = value)
      )
    );

    // Subscribe to the timeout
    this.addViewSubscription(
      this.idleService.timeout.subscribe((timeout) => {
        if (timeout >= 120) {
          this.router.navigate(['/secure/home']);
        }
      })
    );

    this.allowRegistration = this.terminal.allowRegistration;
  }
}
