import { Component, Injector } from '@angular/core';
import { Router } from '@angular/router';
import { AlertController } from '@ionic/angular';
import {
  BasicProfile,
  ErrorCode,
  IdleService,
  LogService,
  MemberCommObject,
  MemberService,
  SystemService,
  Terminal,
  TerminalService,
} from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';

@Component({
  selector: 'app-attendant',
  templateUrl: 'attendant.component.html',
  styleUrls: ['attendant.component.scss'],
})
export class AttendantComponent extends AbstractComponent {
  terminal!: Terminal;
  attendant!: BasicProfile | null;
  invalid: boolean = false;
  eventState: string = '';
  private timedOut: boolean = false;

  constructor(
    injector: Injector,
    protected readonly router: Router,
    private systemService: SystemService,
    private memberService: MemberService,
    public terminalService: TerminalService,
    private alertController: AlertController,
    private idleService: IdleService,
    private logService: LogService
  ) {
    super(injector);
  }

  ngOnInit() {}

  ionViewWillEnter(): void {
    // Subscribe to the session terminal
    this.addGlobalSubscription(
      this.terminalService.terminal.subscribe(
        (value) => (this.terminal = value)
      )
    );
    this.eventState = 'start';

    // Subscribe to the timeout
    this.addViewSubscription(
      this.idleService.timeout.subscribe((timeout) => {
        if (timeout >= 120) {
          this.timedOut = true;
          this.cancel();
        }
      })
    );
  }

  async cancel() {
    this.router.navigate(['/secure/home']);

    if (this.timedOut) {
      const alert = await this.alertController.create({
        header: 'Session Expired',
        message: 'Your session has expired.',
        subHeader: 'Code: ' + ErrorCode.SESSION_TIMEOUT.toString(),
        buttons: ['OK'],
        cssClass: 'system-alert',
      });
      await alert.present();
    }
  }

  async validateAttendant(pin: string) {
    await this.showLoadingModal('Authorizing');
    this.invalid = false;

    let memberCommObject: MemberCommObject = {};
    memberCommObject.apiId = this.terminal.apiId?.apiId;
    memberCommObject.membershipNumber = pin;
    memberCommObject.uniqueId = this.systemService.getUniqueId(
      this.terminal.apiId!.secretStart,
      this.terminal.apiId!.secretEnd,
      pin
    );
    memberCommObject.terminalId = this.terminal.terminalId;

    let result: BasicProfile[] = [];

    await this.memberService.search(memberCommObject, 60).subscribe({
      next: (n) => {
        result = n;
      },
      error: (e) => {
        this.logService.error(
          'AttendantComponent.validateAttendant',
          e.message
        );
        this.dismissLoadingModal();
        this.invalid = true;
      },
      complete: () => {
        this.dismissLoadingModal();
        if (result !== undefined && result.length === 1) {
          this.loadAttendant(result[0]);
        } else if (result !== undefined && result.length >= 1) {
          let filterResult = result.filter((item) => {
            return item.memberType === 'CASH';
          });

          if (filterResult !== undefined && filterResult.length === 1) {
            this.loadAttendant(filterResult[0]);
          }
        } else {
          this.invalid = true;
          this.logService.error(
            'AttendantComponent.validateAttendant',
            'Invalid attendant pin'
          );
        }
      },
    });
  }

  private loadAttendant(attendant: BasicProfile): void {
    this.eventState = 'stop';
    this.terminalService.attendant.next(attendant);
    this.logService.info(
      'AttendantComponent.loadAttendant',
      'attendant push to service: ',
      attendant
    );
    this.router.navigate(['/secure/attendant-home']);
  }
}
