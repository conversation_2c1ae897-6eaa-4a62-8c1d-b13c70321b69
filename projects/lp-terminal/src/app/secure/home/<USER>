import { Component, Injector } from '@angular/core';
import { TerminalService } from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';

@Component({
  selector: 'app-home',
  templateUrl: 'home.component.html',
  styleUrls: ['home.component.scss'],
})
export class HomeComponent extends AbstractComponent {
  constructor(injector: Injector, public terminalService: TerminalService) {
    super(injector);
  }
}
