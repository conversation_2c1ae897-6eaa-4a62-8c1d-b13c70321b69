ion-tabs {
    --background: none;
    background: none;
}

ion-tab-bar {
    --background: #fff;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.2), 0px -0.3px 0px rgba(0, 0, 0, 0.2);
    bottom: 8px;
    position: relative;
    border-radius: 20px;
    width: 94%;
    border-top: none;
    margin: 0 auto;
    height: 70px;
  }
  
ion-tab-button {
    //--color: #d4d4d4;
    --color-selected: var(--ion-color-primary);
    --padding-bottom: 8px;
    --padding-top: 8px;
    border-bottom: none;

    ion-icon {
        font-size: 34px;
    }

    &::before {
        background-color: transparent;
        display: block;
        content: "";
        margin: 0 auto;
        width: 30px;
        height: 2px;
    }

    &.tab-selected {
        font-size: 12px;
    }

    &.tab-selected::before {
        background-color: var(--ion-color-primary);
    }
}
  