/* https://codeadrian.github.io/clay.css/ */
.clay_old {
    --background:var(--clay-background,rgba(0,0,0,.005));
    --border-radius:var(--clay-border-radius,12px);
    --box-shadow:var(--clay-shadow-outset,8px 8px 16px 0 rgba(0,0,0,.25)),
                    inset var(--clay-shadow-inset-primary,-8px -8px 16px 0 rgba(0,0,0,.25)),
                    inset var(--clay-shadow-inset-secondary,8px 8px 16px 0 hsla(0,0%,100%,.2));

    background:var(--clay-background,rgba(0,0,0,.005));
    border-radius:var(--clay-border-radius,12px);
    box-shadow:var(--clay-shadow-outset,8px 8px 16px 0 rgba(0,0,0,.25)),
                    inset var(--clay-shadow-inset-primary,-8px -8px 16px 0 rgba(0,0,0,.25)),
                    inset var(--clay-shadow-inset-secondary,8px 8px 16px 0 hsla(0,0%,100%,.2));
}

.clay {
    --border-radius:var(--clay-border-radius,8px);
    --box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;

    border-radius:var(--clay-border-radius,8px);
    box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
}