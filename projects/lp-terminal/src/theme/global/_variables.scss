.page-title {
    text-align: center;
    font-size: 22px;

    p {
        margin-top: 0;
        margin-block-start: 0;
    }    
}

.ios {
    --ion-safe-area-top: 44pt;

    ion-tab-bar {
        margin-bottom: 6pt;
    }
}

.md {
    --ion-safe-area-top: 0;
}

html { 
    //background: linear-gradient(rgba(255,255,255,0.65), rgba(255,255,255,0.65)), url("../../assets/images/background.jpg") center/cover no-repeat !important;
    --ion-background-color: #fff;
}

ion-popover {
    --background: #fff;
    --ion-background-color: #fff;

    & ion-item {
        --background-focused: var(--ion-color-primary);
    }
}

.system-alert  {
    .alert-head, .alert-message {
        text-align: center;
    }

    .alert-button-group {
        justify-content: center;
    }

    .alert-button {
        width: 60%;
        color: #fff;
        --background: var(--clay-background,rgba(0,0,0,.005));
        --border-radius: var(--clay-border-radius,20px);
        --box-shadow: var(--clay-shadow-outset,8px 8px 16px 0 rgba(0,0,0,.25)), inset var(--clay-shadow-inset-primary,-8px -8px 16px 0 rgba(0,0,0,.25)), inset var(--clay-shadow-inset-secondary,8px 8px 16px 0 hsla(0,0%,100%,.2));
        background: var(--ion-color-primary);
        background: var(--clay-background, rgba(0, 0, 0, 0.005));
        border-radius: 20px;
        border-radius: var(--clay-border-radius, 20px);
        box-shadow: 8px 8px 16px 0 rgb(0 0 0 / 25%), inset -8px -8px 16px 0 rgb(0 0 0 / 25%), inset 8px 8px 16px 0 hsl(0deg 0% 100% / 20%);
        box-shadow: var(--clay-shadow-outset, 8px 8px 16px 0 rgba(0, 0, 0, 0.25)), inset var(--clay-shadow-inset-primary, -8px -8px 16px 0 rgba(0, 0, 0, 0.25)), inset var(--clay-shadow-inset-secondary, 8px 8px 16px 0 hsla(0deg, 0%, 100%, 0.2));
        margin: 4px 0;

        .alert-button-inner {
            justify-content: center;
        }
    }
}

.device-logo {
    display: flex;
    justify-content: center;
    img {
        max-height: 150px;
        object-fit: contain;
    }
}

ion-title {
    text-align: center;
}

.header-divider {
    margin: 0 auto;
    width: 100%;
    border-top: 2px solid #ccc;
    padding-bottom: 2px;
    margin-top: 2px;
}

.divider-text {
    overflow: hidden;
    text-align: center;

    &:before, &:after {
        background-color: #ccc;
        content: "";
        display: inline-block;
        height: 2px;
        position: relative;
        vertical-align: middle;
        width: 50%;
    }

    &:before {
        right: 0.5em;
        margin-left: -50%;
    }

    &:after {
        left: 0.5em;
        margin-right: -50%;
    }
}