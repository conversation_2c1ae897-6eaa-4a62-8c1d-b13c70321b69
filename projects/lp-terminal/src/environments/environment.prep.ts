// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  env: 'PREP',
  lssConfig: {
    terminalPoll: 14400000, // 4 Hours in milliseconds
    googleApiKey: 'AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0',
    apiId: '812275411',
    apiIdKeyStart: 'ITMANQA_038958288_START',
    apiIdKeyEnd: 'ITMANQA_039785672_END',
    appCode: 'start',
    appName: 'Loyalty Plus Terminal',
    appVersion: '0.0.1',
    useAuth: true,
    useION: true,
    useISO: false,
    defaultNotAuthURL: '',
    autoLogout: true,
    autoLogoutTimeout: 180,
    autoLogoutWarning: 120,
    defaultLat: -28.83688693522886,
    defaultLng: 25.49975999318031,
    loadIdentity: false,
    identityBaseUrl: 'http://payroll.dv.lss.si/servlet/systemImage',
    apiBaseUrl: 'https://connect-prep.loyaltyplus.aero/',
    apiBaseUrlMask: 'https://{clientId}prep.loyaltyplus.aero/',
    logEndpoint:
      'https://{clientId}prep.loyaltyplus.aero/extsecure/tools/logservice',
    configAPIUrl: 'http://{clientId}/config/api/v1/',
    authConfig: {
      // Url of the Identity Provider
      issuer: 'https://authqa.loyaltyplus.aero/auth/realms/AgriBonus',
      redirectUri: location.origin + '/lp-pos', //window.location.href.substring(0, window.location.href.lastIndexOf('/')),
      responseType: 'code',
      clientId: 'mobile-app',
      scope: 'openid profile email offline_access',
      requireHttps: false,
      logoutUrl: '/',
      url: 'https://authqa.loyaltyplus.aero/auth',
      realm: 'ZIADA',
      initOptions: {
        adapter: 'default',
        responseMode: 'query',
        redirectUri: 'http://localhost:8100/app',
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri:
          window.location.origin + '/assets/silent-check-sso.html',
      },
    },
    logConfig: {
      level: 0,
      publishers: [
        {
          loggerName: 'console',
          loggerLocation: '',
          isActive: true,
        },
        {
          loggerName: 'localstorage',
          loggerLocation: 'logging',
          isActive: true,
        },
        {
          loggerName: 'webapi',
          loggerLocation: '/api/log',
          isActive: false,
        },
        {
          loggerName: 'filestorage',
          loggerLocation: 'aero.loyaltyplus.app.terminal/log/',
          isActive: false,
        },
      ],
    },
  },
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
