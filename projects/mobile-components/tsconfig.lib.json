/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "outDir": "../../out-tsc/lib",
    "declaration": true,
    "declarationMap": true,
    "inlineSources": true,
    "types": [],
    "skipLibCheck": true,
    "paths": {
      "lp-client-api": [
        "../../dist/lp-client-api"
      ],
      "third-party-fix": [
        "../../dist/third-party-fix"
      ],
      "mobile-components": [
        "../../dist/mobile-components"
      ]
    }
  },
  "exclude": [
    "src/test.ts",
    "**/*.spec.ts"
  ]
}
