import { Directive, ElementRef, Input, OnInit, Renderer2 } from '@angular/core';

@Directive({
  selector: '[libUiButton]',
  standalone: true
})
export class UiButtonDirective implements OnInit {
  @Input() variant: 'primary' | 'secondary' | 'danger' | 'success' = 'primary';
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() block: boolean = false;

  constructor(
    private el: ElementRef,
    private renderer: Renderer2
  ) {}

  ngOnInit() {
    // Add base button class
    this.renderer.addClass(this.el.nativeElement, 'ui-btn');
    
    // Add variant class
    this.renderer.addClass(this.el.nativeElement, `ui-btn-${this.variant}`);
    
    // Add size class
    this.renderer.addClass(this.el.nativeElement, `ui-btn-${this.size}`);
    
    // Add block class if needed
    if (this.block) {
      this.renderer.addClass(this.el.nativeElement, 'ui-btn-block');
    }
  }
}