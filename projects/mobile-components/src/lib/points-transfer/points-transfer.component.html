<div [ngClass]="containerClass">
  <h2 [ngClass]="titleClass">Transfer Points</h2>

  <form [formGroup]="transferForm" (ngSubmit)="onSubmit()">
    <div [ngClass]="formGroupClass" *ngIf="!membershipNumber">
      <label [ngClass]="labelClass" for="fromMembershipNumber">Your Membership Number</label>
      <input
        [ngClass]="inputClass"
        type="text"
        id="fromMembershipNumber"
        formControlName="fromMembershipNumber"
        placeholder="Enter your membership number"
      />
      <div *ngIf="hasError('fromMembershipNumber', 'required')" [ngClass]="errorClass">
        Membership number is required
      </div>
      <div *ngIf="hasError('fromMembershipNumber', 'minlength')" [ngClass]="errorClass">
        Membership number is too short
      </div>
    </div>

    <div [ngClass]="formGroupClass">
      <label [ngClass]="labelClass" for="toMembershipNumber">Recipient Membership Number</label>
      <input
        [ngClass]="inputClass"
        type="text"
        id="toMembershipNumber"
        formControlName="toMembershipNumber"
        placeholder="Enter recipient's membership number"
      />
      <div *ngIf="hasError('toMembershipNumber', 'required')" [ngClass]="errorClass">
        Recipient membership number is required
      </div>
      <div *ngIf="hasError('toMembershipNumber', 'minlength')" [ngClass]="errorClass">
        Recipient membership number is too short
      </div>
    </div>

    <div [ngClass]="formGroupClass">
      <label [ngClass]="labelClass" for="pointsAmount">Points to Transfer</label>
      <input
        [ngClass]="inputClass"
        type="number"
        id="pointsAmount"
        formControlName="pointsAmount"
        placeholder="Enter points amount"
        min="1"
      />
      <div *ngIf="hasError('pointsAmount', 'required')" [ngClass]="errorClass">
        Points amount is required
      </div>
      <div *ngIf="hasError('pointsAmount', 'min')" [ngClass]="errorClass">
        Points must be at least 1
      </div>
    </div>

    <button
      [ngClass]="buttonClass"
      type="submit"
      [disabled]="isSubmitting"
    >
      {{ isSubmitting ? 'Transferring...' : 'Transfer Points' }}
    </button>
  </form>

  <div *ngIf="successMessage" class="mt-4 p-3 bg-green-100 text-green-700 rounded">
    {{ successMessage }}
  </div>

  <div *ngIf="errorMessage" class="mt-4 p-3 bg-red-100 text-red-700 rounded">
    {{ errorMessage }}
  </div>
</div>
