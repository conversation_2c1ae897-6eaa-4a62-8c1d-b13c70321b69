import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { PointsTransferService } from 'lp-client-api';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'lib-points-transfer',
  templateUrl: './points-transfer.component.html',
  styleUrls: ['./points-transfer.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class PointsTransferComponent implements OnInit {
  @Input() containerClass = 'w-full max-w-md mx-auto bg-white rounded-lg';
  @Input() titleClass = 'text-xl font-bold mb-4 text-center';
  @Input() formGroupClass = 'mb-4 bg-blue-500 text-white p-2 rounded-lg';
  @Input() labelClass = 'block text-sm font-medium text-gray-700 mb-1';
  @Input() inputClass = 'w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500';
  @Input() buttonClass = 'w-full py-2 px-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50';
  @Input() errorClass = 'text-sm text-red-600 mt-1';
  @Input() membershipNumber = '';
  @Output() transferSuccess = new EventEmitter<any>();
  @Output() transferError = new EventEmitter<any>();

  transferForm: FormGroup = {} as FormGroup; // Initialize to avoid TS2564
  isSubmitting = false;
  errorMessage = '';
  successMessage = '';

  constructor(
    private fb: FormBuilder,
    private pointsTransferService: PointsTransferService
  ) { }

  ngOnInit(): void {
    this.initForm();
    console.log('Membership number:', this.membershipNumber);
    // If membershipNumber is provided, set it as the fromMembershipNumber
    if (this.membershipNumber) {
      this.transferForm.patchValue({
        fromMembershipNumber: this.membershipNumber
      });
    }
  }

  private initForm(): void {
    this.transferForm = this.fb.group({
      fromMembershipNumber: ['', [Validators.required, Validators.minLength(5)]],
      toMembershipNumber: ['', [Validators.required, Validators.minLength(5)]],
      pointsAmount: ['', [Validators.required, Validators.min(1)]]
    });
  }

  onSubmit(): void {
    console.log('Transfer form:', this.transferForm.value);
    console.log('valid', this.transferForm.valid);
    if (this.transferForm.invalid) {
      this.markFormGroupTouched(this.transferForm);
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';
    this.successMessage = '';

    const fromMembershipNumber = this.membershipNumber || this.transferForm.value.fromMembershipNumber;
    const { toMembershipNumber, pointsAmount } = this.transferForm.value;

    // Validate required fields
    if (!fromMembershipNumber || !toMembershipNumber || !pointsAmount) {
      this.isSubmitting = false;
      this.errorMessage = 'All fields are required';
      return;
    }

    // Prevent transferring to the same account
    if (fromMembershipNumber === toMembershipNumber) {
      this.isSubmitting = false;
      this.errorMessage = 'Cannot transfer points to the same account';
      return;
    }

    this.pointsTransferService.transferPoints(
      fromMembershipNumber,
      toMembershipNumber,
      pointsAmount
    ).subscribe({
      next: (response) => {
        this.isSubmitting = false;
        this.successMessage = 'Points transferred successfully!';
        this.transferSuccess.emit(response);
        this.transferForm.reset();
        
        // If membershipNumber is provided, set it back in the form
        if (this.membershipNumber) {
          this.transferForm.patchValue({
            fromMembershipNumber: this.membershipNumber
          });
        }
      },
      error: (error) => {
        this.isSubmitting = false;
        console.error('Transfer error:', error);
        this.errorMessage = error?.error?.message || 'Failed to transfer points. Please try again.';
        this.transferError.emit(error);
      }
    });
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  hasError(controlName: string, errorType: string): boolean {
    const control = this.transferForm.get(controlName);
    return !!control && control.touched && control.hasError(errorType);
  }
}
