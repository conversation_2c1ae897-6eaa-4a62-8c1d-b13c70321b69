/* Component-specific styles */
:host {
  display: block;
  margin: 1rem 0;
}

form {
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 8px;
  background-color: var(--ion-color-primary);
  color: white;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--ion-color-dark);
}

input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--ion-color-medium);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
  color: var(--ion-color-dark);
  
  &:focus {
    border-color: var(--ion-color-primary);
    box-shadow: 0 0 0 2px rgba(var(--ion-color-primary-rgb), 0.25);
    outline: none;
  }
  
  &:disabled {
    background-color: var(--ion-color-light-shade);
    cursor: not-allowed;
  }

  &::placeholder {
    color: var(--ion-color-medium); /* Medium gray for placeholder text */
  }
}

button {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: var(--ion-color-primary);
  color: white;
  font-weight: 600;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  
  &:hover:not(:disabled) {
    background-color: var(--ion-color-primary-shade);
  }
  
  &:active:not(:disabled) {
    transform: translateY(1px);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--ion-color-dark);
  text-align: center;
  margin-bottom: 1.5rem;
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.success-message {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: rgba(var(--ion-color-success-rgb), 0.15);
  color: var(--ion-color-success-shade);
  border-radius: 6px;
  font-weight: 500;
}

.error-alert {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: rgba(var(--ion-color-danger-rgb), 0.15);
  color: var(--ion-color-danger-shade);
  border-radius: 6px;
  font-weight: 500;
}
