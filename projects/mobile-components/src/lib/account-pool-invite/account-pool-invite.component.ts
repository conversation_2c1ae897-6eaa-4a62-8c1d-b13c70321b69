import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { AccountPoolService } from 'lp-client-api';

interface InviteResponse {
  status: string;
}

interface PoolResponse {
  ENTITYID: number;
  POOLNAME: string;
  [key: string]: any;
}

interface ApiError {
  error?: {
    message?: string;
  };
  [key: string]: any;
}

@Component({
  selector: 'lib-account-pool-invite',
  templateUrl: './account-pool-invite.component.html',
  styleUrls: ['./account-pool-invite.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class AccountPoolInviteComponent implements OnInit {
  @Input() membershipNumber!: string;
  @Input() containerClass = 'w-full max-w-md mx-auto p-4 bg-white rounded-lg shadow';
  @Input() titleClass = 'text-xl font-bold mb-4';
  @Input() buttonClass = 'py-2 px-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 mr-2';
  @Input() cancelButtonClass = 'py-2 px-4 bg-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50';
  @Input() terminology: any = {
    singular: 'Pool',
    plural: 'Pools',
    invitation: 'Pool Invitation',
    inviteAcceptedToast: 'Pool invitation accepted successfully!',
    inviteDeclinedToast: 'Pool invitation declined',
    errorOccurred: 'An error occurred with the account pool',
    noInfoAvailable: 'No pool information available.',
    failedToAccept: 'Failed to accept invitation',
    failedToDecline: 'Failed to decline invitation',
    checkingStatus: 'Failed to check invite status',
    loadingDetails: 'Failed to load pool details',
    invitedToJoin: 'You have been invited to join the account pool:',
    acceptInfoText: 'By accepting this invitation, you will become a member of this pool and can participate in pool activities.',
    totalMembers: 'Total Members',
    poolStatus: 'Pool Status'
  };
  
  @Output() inviteAccepted = new EventEmitter<any>();
  @Output() inviteDeclined = new EventEmitter<any>();
  @Output() error = new EventEmitter<any>();

  hasInvite = false;
  poolInfo: any = null;
  isLoading = false;
  errorMessage = '';
  successMessage = '';

  constructor(
    private accountPoolService: AccountPoolService,
    private location: Location
  ) { }

  ngOnInit(): void {
    this.checkForInvite();
  }

  checkForInvite(): void {
    if (!this.membershipNumber) {
      return;
    }

    this.isLoading = true;

    this.accountPoolService.checkInviteStatus(this.membershipNumber).subscribe({
      next: (response: InviteResponse) => {
        this.hasInvite = response?.status === 'Y';
        
        if (this.hasInvite) {
          this.findPoolDetails();
        } else {
          this.isLoading = false;
        }
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, this.terminology.checkingStatus);
        this.error.emit(error);
      }
    });
  }

  private findPoolDetails(): void {
    // When checking for invitations, we need to find which pool has invited this member
    // This is a bit tricky since the findPool API returns the pool the member is currently in
    // For now, we'll try to get any pool info available
    this.accountPoolService.findPool(this.membershipNumber).subscribe({
      next: (response: PoolResponse) => {
        // Check if this member is in the members list with INVT status
        if (response && response['members']) {
          const invitedMember = response['members'].find(
            (member: any) => member.MPACC.trim() === this.membershipNumber && member.INVITESTATUS === 'INVT'
          );
          
          if (invitedMember) {
            this.poolInfo = response;
          }
        }
        this.isLoading = false;
      },
      error: (error: ApiError) => {
        // If we get a 404, it might mean the user isn't in a pool yet but has an invitation
        // In a real-world scenario, we'd need a separate API to get pending invitations
        this.isLoading = false;
        
        // Don't show error for 404 as it's expected when user has invite but not yet in pool
        if (error?.['status'] !== 404) {
          this.errorMessage = this.handleApiError(error, this.terminology.loadingDetails);
          this.error.emit(error);
        }
      }
    });
  }

  private handleApiError(error: any, fallback: string): string {
    if (error?.error?.message) {
      return error.error.message;
    }
    if (error?.message) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return fallback;
  }

  /**
   * Formats status code to human readable text
   * @param status The status code to format
   * @returns Formatted status text
   */
  formatStatus(status: string): string {
    const statusMap: { [key: string]: string } = {
      'STAA': 'Active',
      'INAC': 'Inactive',
      'PEND': 'Pending',
      'SUSP': 'Suspended'
    };
    return statusMap[status] || status;
  }

  acceptInvite(): void {
    console.log('=== ACCEPT INVITE CLICKED ===');
    console.log('Pool Info available:', !!this.poolInfo);
    console.log('Pool ENTITYID:', this.poolInfo?.ENTITYID);
    console.log('Pool Name:', this.poolInfo?.POOLNAME);
    console.log('Membership Number:', this.membershipNumber);
    
    if (!this.poolInfo?.ENTITYID) {
      console.error('Cannot accept invite - no pool ENTITYID');
      this.errorMessage = this.terminology.noInfoAvailable;
      return;
    }

    console.log('Setting loading state and clearing messages');
    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    console.log('Calling processPoolInvite API with:', {
      entityId: this.poolInfo!.ENTITYID,
      membershipNumber: this.membershipNumber,
      action: 'ACCP',
      auditUser: this.membershipNumber
    });

    this.accountPoolService.processPoolInvite(
      this.poolInfo!.ENTITYID,
      this.membershipNumber,
      'ACCP', // Accept
      this.membershipNumber
    ).subscribe({
      next: (response: any) => {
        console.log('=== INVITE ACCEPTED SUCCESSFULLY ===');
        console.log('API Response:', response);
        console.log('Setting isLoading to false');
        this.isLoading = false;
        console.log('Setting success message');
        this.successMessage = this.terminology.inviteAcceptedToast;
        console.log('Setting hasInvite to false');
        this.hasInvite = false;
        console.log('Emitting inviteAccepted event with response:', response);
        this.inviteAccepted.emit(response);
        
        // Navigate back one page after successful invite acceptance
        console.log('=== NAVIGATION DEBUG START ===');
        console.log('About to navigate back one page after invite acceptance');
        console.log('Location service available:', !!this.location);
        console.log('Window.history available:', !!window.history);
        console.log('Window.history.length:', window.history.length);
        setTimeout(() => {
          console.log('=== EXECUTING NAVIGATION BACK ===');
          console.log('Calling this.location.back()...');
          this.location.back();
          console.log('location.back() called successfully');
          console.log('=== NAVIGATION DEBUG END ===');
        }, 1000); // Slightly longer delay to ensure any toasts are shown
        
        console.log('=== INVITE ACCEPTANCE COMPLETE IN COMPONENT ===');
      },
      error: (error: ApiError) => {
        console.error('=== INVITE ACCEPTANCE ERROR ===');
        console.error('Error:', error);
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, this.terminology.failedToAccept);
        console.error('Emitting error event');
        this.error.emit(error);
      }
    });
  }

  declineInvite(): void {
    if (!this.poolInfo?.ENTITYID) {
      this.errorMessage = this.terminology.noInfoAvailable;
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.accountPoolService.processPoolInvite(
      this.poolInfo!.ENTITYID,
      this.membershipNumber,
      'EXIT', // Decline by exiting
      this.membershipNumber
    ).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        this.successMessage = this.terminology.inviteDeclinedToast;
        this.hasInvite = false;
        this.inviteDeclined.emit(response);
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, this.terminology.failedToDecline);
        this.error.emit(error);
      }
    });
  }
}
