<div *ngIf="hasInvite" [ngClass]="containerClass">
  <h2 [ngClass]="titleClass">{{ terminology.invitation }}</h2>
  
  <div *ngIf="isLoading" class="text-center text-gray-500 my-4">Loading...</div>
  <p *ngIf="errorMessage" class="text-sm text-red-600 mt-1">{{ errorMessage }}</p>
  <p *ngIf="successMessage" class="text-sm text-green-600 mt-1">{{ successMessage }}</p>

  <div *ngIf="poolInfo && !isLoading && !successMessage" class="mb-4">
    <p class="mb-2">{{ terminology.invitedToJoin }}</p>
    <div class="bg-gray-50 p-4 rounded-lg mb-4">
      <p class="font-semibold text-lg">{{ poolInfo.POOLNAME }}</p>
      <p class="text-sm text-gray-600">{{ terminology.singular }} ID: {{ poolInfo.ENTITYID }}</p>
      <p class="text-sm text-gray-600">{{ terminology.totalMembers }}: {{ poolInfo.members?.length || 0 }}</p>
      <p class="text-sm text-gray-600">{{ terminology.singular }} Status: {{ formatStatus(poolInfo.STATUS) }}</p>
    </div>
    
    <p class="text-sm text-gray-700 mb-4">
      {{ terminology.acceptInfoText }}
    </p>
    
    <div class="flex mt-4">
      <button [ngClass]="buttonClass" (click)="acceptInvite()">Accept Invitation</button>
      <button [ngClass]="cancelButtonClass" (click)="declineInvite()">Decline</button>
    </div>
  </div>
</div>
