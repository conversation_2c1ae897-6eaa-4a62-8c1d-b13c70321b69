import { Component, OnInit, Input } from '@angular/core';
import { PointsTransferService } from 'lp-client-api';
import { CommonModule, DatePipe } from '@angular/common';

@Component({
  selector: 'lib-points-transfer-history',
  templateUrl: './points-transfer-history.component.html',
  styleUrls: ['./points-transfer-history.component.scss'],
  standalone: true,
  imports: [CommonModule, DatePipe]
})
export class PointsTransferHistoryComponent implements OnInit {
  @Input() membershipNumber!: string;
  @Input() containerClass = 'w-full max-w-md mx-auto p-4 bg-white rounded-lg shadow';
  @Input() titleClass = 'text-xl font-bold mb-4';
  @Input() tableClass = 'w-full border-collapse';
  @Input() tableHeaderClass = 'bg-gray-100 text-left p-2 border';
  @Input() tableCellClass = 'p-2 border';
  @Input() emptyStateClass = 'text-center text-gray-500 my-4';
  @Input() loadingClass = 'text-center text-gray-500 my-4';
  @Input() errorClass = 'text-center text-red-500 my-4';

  transferHistory: any[] = [];
  isLoading = false;
  errorMessage = '';

  constructor(private pointsTransferService: PointsTransferService) { }

  ngOnInit(): void {
    this.loadTransferHistory();
  }

  ngOnChanges(): void {
    if (this.membershipNumber) {
      this.loadTransferHistory();
    }
  }

  loadTransferHistory(): void {
    if (!this.membershipNumber) {
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    this.pointsTransferService.getTransferHistory(this.membershipNumber)
      .subscribe({
        next: (response) => {
          this.transferHistory = response?.data || [];
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = 'Failed to load transfer history';
          this.isLoading = false;
          console.error('Error loading transfer history:', error);
        }
      });
  }

  refreshHistory(): void {
    this.loadTransferHistory();
  }
}
