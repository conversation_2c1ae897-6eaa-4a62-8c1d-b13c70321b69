<div [ngClass]="containerClass">
  <h2 [ngClass]="titleClass">Points Transfer History</h2>

  <div *ngIf="isLoading" [ngClass]="loadingClass">
    Loading transfer history...
  </div>

  <div *ngIf="errorMessage" [ngClass]="errorClass">
    {{ errorMessage }}
    <button (click)="refreshHistory()" class="text-blue-500 underline ml-2">Try again</button>
  </div>

  <div *ngIf="!isLoading && !errorMessage">
    <table *ngIf="transferHistory.length > 0" [ngClass]="tableClass">
      <thead>
        <tr>
          <th [ngClass]="tableHeaderClass">Date</th>
          <th [ngClass]="tableHeaderClass">From</th>
          <th [ngClass]="tableHeaderClass">To</th>
          <th [ngClass]="tableHeaderClass">Points</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let transfer of transferHistory">
          <td [ngClass]="tableCellClass">{{ transfer.date | date:'short' }}</td>
          <td [ngClass]="tableCellClass">{{ transfer.fromAccount }}</td>
          <td [ngClass]="tableCellClass">{{ transfer.toAccount }}</td>
          <td [ngClass]="tableCellClass">{{ transfer.points }}</td>
        </tr>
      </tbody>
    </table>

    <div *ngIf="transferHistory.length === 0" [ngClass]="emptyStateClass">
      No transfer history found
    </div>
  </div>
</div>
