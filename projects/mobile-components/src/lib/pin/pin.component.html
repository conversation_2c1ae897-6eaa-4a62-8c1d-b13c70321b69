<div class="pin-lock">
  <div class="lock-label-row">
    {{header}}
  </div>
  <div class="lock-label-row small">
    {{subheader}}
  </div>
  <div class="lock-label-row invalid" *ngIf="invalid">
      {{invalidLabel}}
    </div>
  <div class="lock-circles-row" [ngClass]="invalid ?  'lock-shake' : ''">
      <div class="lock-circle" *ngFor="let col of [].constructor(pinLength); let i = index" [ngClass]=" pin.length>i ? 'lock-full' : ''"></div>  
  </div>
  <div class="lock-numbers-row">
    <div (click)="digit(1)" class="lock-digit">1</div>
    <div (click)="digit(2)" class="lock-digit">2</div>
    <div (click)="digit(3)" class="lock-digit">3</div>
  </div>
  <div class="lock-numbers-row">
    <div (click)="digit(4)" class="lock-digit">4</div>
    <div (click)="digit(5)" class="lock-digit">5</div>
    <div (click)="digit(6)" class="lock-digit">6</div>
  </div>
  <div class="lock-numbers-row">
    <div (click)="digit(7)" class="lock-digit">7</div>
    <div (click)="digit(8)" class="lock-digit">8</div>
    <div (click)="digit(9)" class="lock-digit">9</div>
  </div>
  <div class="lock-numbers-row">
    <div (click)="clear()" class="lock-digit lock-ac"><ion-icon slot="start" name="refresh-outline"></ion-icon></div>
    <div (click)="digit(0)" class="lock-digit">0</div>
    <div (click)="remove()" class="lock-digit lock-del"><ion-icon slot="start" name="backspace-outline"></ion-icon></div>
  </div>
  <div class="lock-numbers-row">
      <div (click)="this.cancel.emit(true)" class="lock-digit lock-cancel">Cancel</div>
  </div>
</div>