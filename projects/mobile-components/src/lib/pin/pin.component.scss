/* Animations*/
@keyframes lock-shake {
    from, to {
      transform: translate3d(0, 0, 0);
    }
    10%, 30%, 50%, 70%, 90% {
      transform: translate3d(-10px, 0, 0);
    }
    20%, 40%, 60%, 80% {
      transform: translate3d(10px, 0, 0);
    }
  }
@keyframes lock-buttonPress {
    0% {
        background-color: #E0E0E0;
    }
    100% {
        background-color: var(--ion-color-primary);
    }
}
/* Lock Screen Layout*/
.pin-lock {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    height: 100%;
    z-index: 999;

    .lock-label-row {
        height: 50px;
        width: 100%;
        text-align: center;
        font-size: 26px;
        
        color: var(--ion-text-color);
    }
    .small {
        font-size: 18px;
    }
    .invalid {
        color: var(--ion-color-danger);
        font-size: 24px;
    }
    .lock-circles-row {
        display: flex;
        flex-direction: row;
        justify-content: center;
        width: 100%;
        height: 45px;
    }
    .lock-circle {
        border-radius: 50%;
        width: 14px;
        height: 14px;
        border:solid 2px var(--ion-text-color);
        margin: 0 15px;
    }
    .lock-numbers-row {
        display: flex;
        flex-direction: row;
        justify-content: center;
        width: 100%;
        height: 100px;
    }
    .lock-digit {
        margin: 0 8px;
        width: 75px;
        border-radius: 10%;
        height: 75px;
        text-align: center;
        font-size: 26px;
        color: #fff;
        background-color: var(--ion-color-primary);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .lock-digit:active {
        transform: scale(0.95);
        -webkit-box-shadow: 0px 0px 10px 5px rgba(0,0,0,0.4);
        -moz-box-shadow: 0px 0px 10px 5px rgba(0,0,0,0.4);
        box-shadow: 0px 0px 10px 5px rgba(0,0,0,0.4);
    }
    .lock-cancel {
        height: 50px;
        width: 180px;
        font-size: 20px;
    }
    .lock-ac, .lock-del {
        color: var(--ion-text-color);
        background-color: transparent;
        font-size: 28px;
    }
    .lock-full {
        background-color:var(--ion-text-color)!important;
    }
    .lock-shake {
        -webkit-animation-name: lock-shake;
        animation-name: lock-shake;
        -webkit-animation-duration: 0.6;
        animation-duration: 0.6s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
    }
}

@media only screen and (max-width: 450px) {
    .pin-lock {
        .lock-digit {
            width: 58px !important;
            height: 58px !important;
        }
        .lock-cancel {
            width: 180px !important;
        }
    }
}