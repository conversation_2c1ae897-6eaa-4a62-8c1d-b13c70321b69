import {
  Component,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { IdleService } from 'lp-client-api';
import { AbstractComponent } from '../abstract.component';
import { Haptics } from '@capacitor/haptics';
import { Subscription } from 'rxjs';

@Component({
  selector: 'lp-pin',
  templateUrl: './pin.component.html',
  styleUrls: ['./pin.component.scss'],
})
export class PinComponent extends AbstractComponent implements OnChanges {
  @Input() header: string = 'PIN Authorization';
  @Input() subheader: string = 'Please enter your pin code';
  @Input() invalidLabel: string = 'Invalid pin';
  @Input() pinLength: number = 5;
  @Input() timeout: number = 0; // Timeout in seconds
  @Input() invalid: boolean = false;
  @Input() eventState: string = 'start';
  @Output() submit = new EventEmitter<string>();
  @Output() timedOut = new EventEmitter<boolean>();
  @Output() cancel = new EventEmitter<boolean>();

  protected pin: string = '';
  protected pinSub?: Subscription;
  constructor(injector: Injector, private idleService: IdleService) {
    super(injector);
  }

  ionViewWillEnter(): void {
    // Subscribe to the timeout
    if (this.timeout > 0) {
      this.addViewSubscription(
        this.idleService.timeout.subscribe((timeoutCheck) => {
          if (timeoutCheck >= this.timeout) {
            this.timedOut.emit(true);
          }
        })
      );
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (this.invalid) {
      this.invalidAttempt();
    }

    if (changes['eventState']) {
      let previousEvent: string = changes['eventState'].previousValue;
      let currentEvent: string = changes['eventState'].currentValue;

      if (
        previousEvent !== '' &&
        currentEvent !== '' &&
        previousEvent !== currentEvent
      ) {
        console.log('Pin old eventState: ', previousEvent);
        console.log('Pin new eventState: ', currentEvent);
        this.pin = '';
        this.invalid = false;
      }
    } else {
      console.log('changes ', changes);
    }
  }

  protected digit(digit: any) {
    Haptics.vibrate();
    this.pin += '' + digit;

    if (this.pin.length === this.pinLength) {
      this.submit.emit(this.pin); // Emit the PIN that has been entered so that the parent caller can validate it
      this.pin = '';
    }
  }

  protected invalidAttempt() {
    setTimeout(() => {
      this.pin = '';
      this.invalid = false; // Reset back to valid so that the screen resets
    }, 1000); // After 1 second reset attendant auth screen to default
  }

  protected clear() {
    this.pin = '';
    this.invalid = false;
  }

  protected remove() {
    this.pin = this.pin.slice(0, -1);
  }
}
