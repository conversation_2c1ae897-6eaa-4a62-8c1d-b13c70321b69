import { Component, Injector, Input, OnInit } from '@angular/core';

@Component({
  selector: 'lib-head-logo',
  templateUrl: './head-logo.component.html',
  styleUrls: ['./head-logo.component.css']
})
export class HeadLogoComponent implements OnInit {
  @Input() 
  names?: string;
  @Input()
  balance?: number;
  @Input()
  membership?: string;
  @Input()
  src?: string;
  @Input()
  phone?: string
  @Input()
  type?: string = 'balance'
  @Input()
  storeName?: string
  @Input()
  poolsTerminology?: string = 'Pools'

typeOf = 'balance';
text?: string = this.balance ? 'Balance' + ' ' + this.balance : '0';

get useText (){
  let text = ''


    if(this.type  === 'balance'){
      text = this.balance ? 'Balance' + ' ' + this.balance : '0';


    }

    if(this.type === 'membership'){
      text = this.balance ? '#' + ' ' + this.membership : '0';
    }

  return text
}
  showText (){
    let typf = 'balance'
    if(this.typeOf === 'balance'){
      this.text = this.balance ? 'Balance' + ' ' + this.balance : '0';
      this.typeOf = 'membership'
      typf = 'membership';

    }

    if(this.typeOf === 'membership'){
      this.text = this.balance ? '#' + ' ' + this.membership : '0';
      this.typeOf = 'balance'
    }

  }

  ngOnInit(): void {
    this.showText()
  }

  getTimeGreeting(): string {
    return 'Welcome';
  }

  getTimeIcon(): string {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 12) {
      return 'sunny-outline';
    } else if (hour >= 12 && hour < 17) {
      return 'partly-sunny-outline';
    } else if (hour >= 17 && hour < 20) {
      return 'cloudy-outline';
    } else {
      return 'moon-outline';
    }
  }
}

