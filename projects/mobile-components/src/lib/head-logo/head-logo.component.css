/* Modern Header Design */
.modern-header {
    position: relative;
    padding: 24px 16px 40px;
    background: #0069b4;
    overflow: hidden;
}

/* Header content - flex layout */
.header-content {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Text content - left aligned */
.text-content {
    text-align: left;
    flex: 1;
}

.time-greeting {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 8px;
}

.time-greeting ion-icon {
    font-size: 20px;
    vertical-align: middle;
    margin-right: 8px;
}

.user-name {
    font-size: 32px;
    font-weight: 700;
    color: white;
    margin: 0;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.logo-badge {
    width: 140px;
    height: 140px;
    border-radius: 24px;
    padding: 16px;
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.logo-badge:active {
    transform: scale(0.95);
}

.logo-badge img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Legacy styles for compatibility */
.center {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.points {
    --background: var(--ion-color-primary);
    width: 100%;
    margin-left: 20px;
    height: 40px;
    box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.6);
}

.logo {
    height: 80px;
    margin-right: 15px;
}

.welcome {
    width: 100%;
    text-align: center;
    margin-top: -10px;
}

.welcome h5 {
    text-align: center;
    margin: 0;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
}

.welcome .front-text {
    display: block;
    text-align: center;
    font-size: 32px;
    font-weight: 700;
    color: white;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Profile Header Specific */
.profile-header .time-greeting {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
}

.profile-header .time-greeting ion-icon {
    font-size: 20px;
    margin-right: 0;
}

/* Card Header Specific */
.card-header .time-greeting {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
}

.card-header .time-greeting ion-icon {
    font-size: 20px;
    margin-right: 0;
}

/* Transactions Header Specific */
.transactions-header .time-greeting {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
}

.transactions-header .time-greeting ion-icon {
    font-size: 20px;
    margin-right: 0;
}

/* Stores Header Specific */
.stores-header .time-greeting {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
}

.stores-header .time-greeting ion-icon {
    font-size: 20px;
    margin-right: 0;
}

/* Store Detail Header Specific */
.store-detail-header .time-greeting {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
}

.store-detail-header .time-greeting ion-icon {
    font-size: 20px;
    margin-right: 0;
}

.store-detail-header .store-name {
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin: 0;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    text-transform: lowercase;
    text-align: left;
    line-height: 1.3;
}

.store-detail-header .store-name:first-letter {
    text-transform: uppercase;
}

/* Security Header Specific */
.security-header .time-greeting {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
}

.security-header .time-greeting ion-icon {
    font-size: 20px;
    margin-right: 0;
}

/* Pools Header Specific */
.pools-header .time-greeting {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
}

.pools-header .time-greeting ion-icon {
    font-size: 20px;
    margin-right: 0;
}

/* Points Header Specific */
.points-header .time-greeting {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
}

.points-header .time-greeting ion-icon {
    font-size: 20px;
    margin-right: 0;
}

/* Contact Header Specific */
.contact-header .time-greeting {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
}

.contact-header .time-greeting ion-icon {
    font-size: 20px;
    margin-right: 0;
}

/* Gift Card Header Specific */
.giftcard-header .time-greeting {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
}

.giftcard-header .time-greeting ion-icon {
    font-size: 20px;
    margin-right: 0;
}

/* Notification Header Specific */
.notification-header .time-greeting {
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
}

.notification-header .time-greeting ion-icon {
    font-size: 20px;
    margin-right: 0;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Responsive adjustments */
@media (max-width: 320px) {
    .user-name {
        font-size: 26px;
    }
    
    .logo-badge {
        width: 110px;
        height: 110px;
        padding: 14px;
    }
}