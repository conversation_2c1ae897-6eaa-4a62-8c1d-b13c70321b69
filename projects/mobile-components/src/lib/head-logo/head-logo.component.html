<!-- Modern Header with Animation -->
<div class="modern-header" *ngIf="type == 'welcome'">
  <div class="header-content">
    <div class="text-content">
      <p class="time-greeting">
        {{ getTimeGreeting() }}
      </p>
      <h1 class="user-name">{{ names }}</h1>
    </div>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Profile Header -->
<div class="modern-header profile-header" *ngIf="type == 'profile'">
  <div class="header-content">
    <div class="text-content">
      <p class="time-greeting">
        <ion-icon name="person-circle-outline"></ion-icon>
        Your Profile
      </p>
      <h1 class="user-name">{{ names }}</h1>
    </div>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Card Header -->
<div class="modern-header card-header" *ngIf="type == 'card'">
  <div class="header-content">
    <div class="text-content">
      <p class="time-greeting">
        <ion-icon name="card-outline"></ion-icon>
        Your Virtual Card
      </p>
      <h1 class="user-name">{{ names }}</h1>
    </div>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Transactions Header -->
<div class="modern-header transactions-header" *ngIf="type == 'transactions'">
  <div class="header-content">
    <div class="text-content">
      <p class="time-greeting">
        <ion-icon name="receipt-outline"></ion-icon>
        Transaction History
      </p>
      <h1 class="user-name">{{ names }}</h1>
    </div>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Stores Header -->
<div class="modern-header stores-header" *ngIf="type == 'stores'">
  <div class="header-content">
    <div class="text-content">
      <p class="time-greeting">
        <ion-icon name="location-outline"></ion-icon>
        Find Stores Near You
      </p>
      <h1 class="user-name">{{ names }}</h1>
    </div>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Store Detail Header -->
<div class="modern-header store-detail-header" *ngIf="type == 'store-detail'">
  <div class="header-content">
    <div class="text-content">
      <p class="time-greeting">
        <ion-icon name="storefront-outline"></ion-icon>
        Store Details
      </p>
      <h1 class="store-name">{{ storeName || 'Store' }}</h1>
    </div>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Security Header -->
<div class="modern-header security-header" *ngIf="type == 'security'">
  <div class="header-content">
    <div class="text-content">
      <p class="time-greeting">
        <ion-icon name="shield-checkmark-outline"></ion-icon>
        Account Security
      </p>
      <h1 class="user-name" *ngIf="names">{{ names }}</h1>
    </div>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Pools Header -->
<div class="modern-header pools-header" *ngIf="type == 'pools'">
  <div class="header-content">
    <div class="text-content">
      <p class="time-greeting">
        <ion-icon name="people-outline"></ion-icon>
        Account {{ poolsTerminology }}
      </p>
      <h1 class="user-name" *ngIf="names">{{ names }}</h1>
    </div>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Points Header -->
<div class="modern-header points-header" *ngIf="type == 'points'">
  <div class="header-content">
    <div class="text-content">
      <p class="time-greeting">
        <ion-icon name="wallet-outline"></ion-icon>
        Points & Transfers
      </p>
      <h1 class="user-name" *ngIf="names">{{ names }}</h1>
    </div>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Contact Header -->
<div class="modern-header contact-header" *ngIf="type == 'contact'">
  <div class="header-content">
    <div class="text-content">
      <p class="time-greeting">
        <ion-icon name="chatbubbles-outline"></ion-icon>
        Contact Us
      </p>
      <h1 class="user-name" *ngIf="names">{{ names }}</h1>
    </div>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Gift Card Header -->
<div class="modern-header giftcard-header" *ngIf="type == 'giftcard'">
  <div class="header-content">
    <div class="text-content">
      <p class="time-greeting">
        <ion-icon name="gift-outline"></ion-icon>
        Claim Gift Card
      </p>
      <h1 class="user-name" *ngIf="names">{{ names }}</h1>
    </div>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Notification Settings Header -->
<div class="modern-header notification-header" *ngIf="type == 'notification'">
  <div class="header-content">
    <div class="text-content">
      <p class="time-greeting">
        <ion-icon name="notifications-outline"></ion-icon>
        Notification Settings
      </p>
      <h1 class="user-name" *ngIf="names">{{ names }}</h1>
    </div>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Legacy layout for other types -->
<ion-grid *ngIf="type != 'welcome' && type != 'profile' && type != 'card' && type != 'transactions' && type != 'stores' && type != 'store-detail' && type != 'security' && type != 'pools' && type != 'points' && type != 'contact' && type != 'giftcard' && type != 'notification'">
  <ion-row class="center">
    <div>
      <ion-button
        (click)="showText()"
        *ngIf="type == 'balance' || type == 'membership'"
        expand="block"
        class="points"
        >{{ useText }}</ion-button
      >
      <a
        *ngIf="type == 'phone'"
        style="text-decoration: none"
        href="{{ 'tel:' + phone }}"
      >
        <ion-button (click)="showText()" expand="block" class="points">
          <ion-icon size="25px" class="icon-call" name="call"></ion-icon>

          {{ phone }}</ion-button
        >
      </a>
    </div>

    <div class="logo-col">
      <img class="logo" [src]="src" alt="" />
    </div>
  </ion-row>
</ion-grid>
