import { DatePipe } from '@angular/common';
import { Component, Injector, Input, OnInit, Type } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { KeyCloakService, LPCode, LPCodeGroup, SystemService, ValidationService } from 'lp-client-api';
import { AbstractComponent } from './abstract.component';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';

enum FormState {
  create,
  read,
  update,
  delete,
  clone,
  search,
  auth,
  reset, // Used for password reset
  terms, // Terms and Conditions
  pass, // Successful outcome
  fail, // Failure outcome
}

@Component({
  template: '',
})
export abstract class AbstractFormComponent<T> extends AbstractComponent {
  @Input() public formData?: T;
  _keyCloak?: KeyCloakService;
  _formValidations: ValidationService;
  _systemService: SystemService;
  _formBuilder: FormBuilder;
  _form: FormGroup;
  _dateMap: any = {};
  _formState = FormState.create; // Always default to create state
  _formStateType = FormState;

  constructor(injector: Injector) {
    super(injector);
    this._formValidations = injector.get<ValidationService>(
      ValidationService as Type<ValidationService>
    );
    this._formBuilder = injector.get<FormBuilder>(
      FormBuilder as Type<FormBuilder>
    );
    this._keyCloak = injector.get<KeyCloakService>(
      KeyCloakService as Type<KeyCloakService>
    );
    this._systemService = injector.get<SystemService>(
      SystemService as Type<SystemService>
    );
    this._form = this._formBuilder.group({});
  }

  get form(): any {
    return this._form.controls;
  }

  get formState(): FormState {
    return this._formState;
  }

  setFormState(formState: FormState) {
    this._formState === formState;
  }

  isformState(formState: FormState): boolean {
    return this._formState === formState;
  }

  isFormValid(): boolean {
    return this._form == undefined ? false : this._form.valid;
  }

  setDate(event: any, component: string): void {
    event.confirm(true); // Close the popover
    this._form?.controls[component].patchValue(
      new DatePipe(this.locale).transform(event.value)
    ); // Patch formatted date to form
    this._dateMap[component] = event.value; // Store original date in map to patch upon submit
  }

  getFormValues(): any {
    return { ...this._form.value, ...this._dateMap };
  }

  isFormComponentInvalid(componentId: string): boolean {
    let component = this.form[componentId];
    if (component !== undefined && component.touched) {
      return !component.valid;
    } else {
      return false;
    }
  }

  getComponentErrors(componentId: string): string[] {
    let component = this.form[componentId];
    return this._formValidations.doErrors(component.errors, componentId);
  }

  isAuthenticated(): boolean {
    return this._keyCloak?.authSuccess === undefined
      ? false
      : this._keyCloak.authSuccess;
  }

  resetForm(): void {
    this._form = this._formBuilder.group({});
    this._dateMap = {};
    this.formData = undefined;
    this._formStateType;
  }

  gotoNextField(nextElement: any) {
    nextElement.setFocus();
  }

  getCodeList(groupId:string): Observable<LPCode[]>{
    return this._systemService.getCodeGroup(groupId).pipe(map((data : LPCodeGroup) => {return data.codeItem}));
  }
}
