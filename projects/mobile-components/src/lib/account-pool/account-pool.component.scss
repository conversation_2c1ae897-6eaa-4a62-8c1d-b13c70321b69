/* Modern Account Pool Component Styles */

:host {
  display: block;
}

/* Form Container */
form {
  margin-bottom: 1.5rem;
}

/* Section Title */
h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #212121;
  text-align: center;
  margin-bottom: 1.5rem;
  position: relative;
  
}

h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #212121;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
  
}

/* Buttons Container */
.flex {
  display: flex;
  
  &.justify-center {
    justify-content: center;
  }
  
  &.space-x-4 > * + * {
    margin-left: 1rem;
  }
}

/* Modern Form Groups */
.form-group,
div[class*="mb-"] {
  margin-bottom: 1.25rem;
}

/* Labels */
label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  color: #666;
  transition: color 0.3s ease;
}

/* Modern Input Styling */
input, select {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: white;
  color: #212121;
  
  &:focus {
    border-color: var(--ion-color-primary, #FF6B35);
    background-color: white;
    box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.1);
    outline: none;
  }
  
  &:hover:not(:disabled) {
    border-color: #ccc;
  }
  
  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 0.7;
  }

  &::placeholder {
    color: #999;
  }
}

/* Modern Button Styling */
button {
  padding: 0.875rem 1.5rem;
  background: var(--ion-color-primary, #FF6B35);
  color: white;
  font-weight: 600;
  font-size: 0.9375rem;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.25);
  min-width: 140px;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    background: var(--ion-color-primary-shade, #e55a2b);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.35);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    box-shadow: none;
  }
  
  // Secondary button style (for cancel)
  &[type="button"] {
    background: #e0e0e0;
    color: #666;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    &:hover:not(:disabled) {
      background: #d0d0d0;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
  
  // Exit pool button
  &.bg-red-600 {
    background: #F44336;
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.25);
    
    &:hover:not(:disabled) {
      background: #D32F2F;
      box-shadow: 0 6px 20px rgba(244, 67, 54, 0.35);
    }
  }
}

/* Error Messages */
.error-message,
p[class*="text-red"],
.text-sm.text-red-600 {
  color: #F44336;
  font-size: 0.8125rem;
  margin-top: 0.375rem;
  display: flex;
  align-items: center;
  gap: 4px;
  animation: fadeIn 0.3s ease-out;
  
  &::before {
    content: '⚠';
    font-size: 1rem;
  }
}

/* Success Messages */
.text-sm.text-green-600 {
  color: #4CAF50;
  font-size: 0.8125rem;
  margin-top: 0.375rem;
  display: flex;
  align-items: center;
  gap: 4px;
  animation: fadeIn 0.3s ease-out;
  
  &::before {
    content: '✓';
    font-size: 1rem;
  }
}

/* Loading State */
.text-center.text-gray-500 {
  color: #666;
  font-size: 0.9375rem;
  padding: 2rem;
  
  &::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #e0e0e0;
    border-top-color: var(--ion-color-primary, #FF6B35);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
    vertical-align: middle;
  }
}

/* Pool Details Section */
.mb-6 {
  margin-bottom: 1.5rem;
  background: white;
  padding: 1.25rem;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  
  p {
    margin: 0.5rem 0;
    font-size: 0.9375rem;
    color: #424242;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    &.font-medium {
      font-weight: 500;
      color: #666;
      
      span.font-normal {
        font-weight: 400;
        color: #212121;
        font-family: inherit;
      }
    }
  }
}

/* Modern Table Styling */
table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 1.5rem;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  
  th {
    background: white;
    text-align: left;
    padding: 1rem;
    font-weight: 600;
    font-size: 0.8125rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #e0e0e0;
    
    &:nth-child(2) {
      text-align: center;
    }
    
    &:nth-child(3) {
      text-align: right;
    }
  }
  
  td {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
    font-size: 0.9375rem;
    color: #424242;
    
    &:nth-child(2) {
      text-align: center;
    }
    
    &:nth-child(3) {
      text-align: right;
      font-weight: 600;
      color: #4CAF50;
    }
  }
  
  tbody tr {
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: #fafafa;
    }
    
    &:last-child td {
      border-bottom: none;
    }
  }
}

/* Member Info Styling */
.member-name-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  .member-name {
    font-weight: 500;
    color: #212121;
    font-size: 0.9375rem;
  }
  
  .member-id {
    font-size: 0.75rem;
    color: #666;
    background-color: #f0f0f0;
    padding: 3px 8px;
    border-radius: 12px;
    display: inline-block;
    width: fit-content;
    font-family: monospace;
  }
}

/* Type Badge Styling */
.type-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  transition: all 0.2s ease;
  
  &.admin {
    background: var(--ion-color-primary, #FF6B35);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
  }
  
  &.member {
    background: #e9ecef;
    color: #666;
  }
}

/* Exit Pool Section */
.mt-6.pt-4.border-t {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e0e0e0;
  
  p.text-sm {
    font-size: 0.8125rem;
    color: #999;
    margin-top: 0.75rem;
    font-style: italic;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Members Table Styling - matching pools.component.scss */
.members-table {
  width: 100%;
  margin-top: 1rem;
  
  .table-header {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1.5fr;
    gap: 1px;
    background-color: white;
    padding: 0;
    border-radius: 8px 8px 0 0;
    border: 1px solid var(--ion-color-medium, #989aa2);
    overflow: hidden;
    
    .table-cell.header {
      font-weight: 600;
      font-size: 0.875rem;
      color: var(--ion-color-dark, #222428);
      padding: 0.75rem;
      background-color: white;
      
      &:first-child {
        border-top-left-radius: 8px;
      }
      
      &:last-child {
        border-right: none;
        border-top-right-radius: 8px;
      }
    }
  }
  
  .table-row {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1.5fr;
    gap: 1px;
    padding: 0;
    border-bottom: 1px solid var(--ion-color-medium, #989aa2);
    border-left: 1px solid var(--ion-color-medium, #989aa2);
    border-right: 1px solid var(--ion-color-medium, #989aa2);
    align-items: stretch;
    
    &:last-child {
      border-bottom: 1px solid var(--ion-color-medium, #989aa2);
      border-radius: 0 0 8px 8px;
      overflow: hidden;
      
      .table-cell:first-child {
        border-bottom-left-radius: 8px;
      }
      
      .table-cell:last-child {
        border-bottom-right-radius: 8px;
      }
    }
    
    .table-cell {
      font-size: 0.875rem;
      padding: 0.75rem;
      background-color: white;
      display: flex;
      align-items: center;
      
      &:last-child {
        border-right: none;
      }
      
      &.member-info {
        flex-direction: column;
        align-items: flex-start;
        
        .member-name {
          font-weight: 500;
          color: var(--ion-color-dark, #222428);
          margin-bottom: 0.25rem;
        }
        
        .member-id {
          font-size: 0.75rem;
          color: var(--ion-color-medium-shade, #86888c);
        }
      }
      
      &.badges-column {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        
        ion-badge {
          font-size: 0.75rem;
          padding: 0.25rem 0.5rem;
        }
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .members-table {
    .table-header,
    .table-row {
      grid-template-columns: 2fr 1.5fr 1.5fr;
    }
    
    .table-cell {
      padding: 0.5rem !important;
      font-size: 0.75rem !important;
    }
    
    .table-header .table-cell.header {
      font-size: 0.75rem;
    }
    
    .table-row .table-cell {
      font-size: 0.75rem;
      
      &.member-info {
        .member-name {
          font-size: 0.8rem;
        }
        
        .member-id {
          font-size: 0.7rem;
        }
      }
    }
  }
  h2 {
    font-size: 1.25rem;
  }
  
  button {
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    min-width: 120px;
  }
  
  table {
    font-size: 0.8125rem;
    
    th, td {
      padding: 0.75rem 0.5rem;
    }
    
    .member-name-container {
      .member-name {
        font-size: 0.875rem;
      }
      
      .member-id {
        font-size: 0.6875rem;
        padding: 2px 6px;
      }
    }
    
    .type-badge {
      font-size: 0.6875rem;
      padding: 3px 8px;
    }
  }
  
  input, select {
    padding: 0.75rem 0.875rem;
    font-size: 0.9375rem;
  }
}