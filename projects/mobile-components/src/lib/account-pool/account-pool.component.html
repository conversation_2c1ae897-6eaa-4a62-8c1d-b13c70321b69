<div >
  <!-- Loading and error states -->
  <p *ngIf="isLoading" class="text-center text-gray-500 my-4">Loading...</p>
  <p *ngIf="errorMessage" [ngClass]="errorClass">{{ errorMessage }}</p>
  <p *ngIf="successMessage" class="text-sm text-green-600 mt-1">{{ successMessage }}</p>

  <!-- Find Pool View - Default state -->
  <ng-container *ngIf="view === 'find'">
    <h2 [ngClass]="titleClass">{{ terminology.singular }}</h2>
    
    <!-- Show pending invitation notification -->
    <div *ngIf="hasPendingInvite" class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <p class="text-yellow-800 font-semibold mb-2">You have a pending {{ terminology.singular.toLowerCase() }} invitation!</p>
      <button [ngClass]="buttonClass" (click)="changeView('pending-invite')">
        View Invitation
      </button>
    </div>
    
    <div class="flex justify-center mb-6" style="gap: 1rem;">
      <button [ngClass]="buttonClass" (click)="changeView('create')">{{ terminology.createNew }}</button>
      <button [ngClass]="buttonClass" (click)="changeView('join')">{{ terminology.joinExisting }}</button>
    </div>
  </ng-container>

  <!-- Create Pool View -->
  <ng-container *ngIf="view === 'create'">
    <h2 [ngClass]="titleClass">{{ terminology.createNew }}</h2>
    
    <form [formGroup]="createPoolForm" (ngSubmit)="createPool()">
      <div [ngClass]="formGroupClass">
        <label [ngClass]="labelClass" for="poolName">{{ terminology.singular }} Name</label>
        <input 
          [ngClass]="inputClass" 
          id="poolName"
          type="text" 
          formControlName="name" 
          placeholder="Enter {{ terminology.singular.toLowerCase() }} name"
        >
        <p *ngIf="hasError(createPoolForm, 'name', 'required')" [ngClass]="errorClass">
          {{ terminology.singular }} name is required
        </p>
      </div>

      <div [ngClass]="formGroupClass">
        <label [ngClass]="labelClass" for="email">Email</label>
        <input 
          [ngClass]="inputClass" 
          id="email"
          type="email" 
          formControlName="email" 
          placeholder="Enter email"
        >
        <p *ngIf="hasError(createPoolForm, 'email', 'required')" [ngClass]="errorClass">
          Email is required
        </p>
        <p *ngIf="hasError(createPoolForm, 'email', 'email')" [ngClass]="errorClass">
          Please enter a valid email
        </p>
      </div>

      <div [ngClass]="formGroupClass">
        <label [ngClass]="labelClass" for="countryCode">Country Code</label>
        <select 
          [ngClass]="inputClass" 
          id="countryCode"
          formControlName="countryCode"
        >
          <option value="">Select country code</option>
          <option *ngFor="let country of countryDialCodes" [value]="country.code">
            {{ country.name }} ({{ country.code }})
          </option>
        </select>
        <p *ngIf="hasError(createPoolForm, 'countryCode', 'required')" [ngClass]="errorClass">
          Country code is required
        </p>
      </div>

      <div [ngClass]="formGroupClass">
        <label [ngClass]="labelClass" for="telephone">Telephone</label>
        <input 
          [ngClass]="inputClass" 
          id="telephone"
          type="tel" 
          formControlName="telephone" 
          placeholder="Enter telephone number"
        >
        <p *ngIf="hasError(createPoolForm, 'telephone', 'required')" [ngClass]="errorClass">
          Telephone is required
        </p>
      </div>

      <div [ngClass]="formGroupClass">
        <label [ngClass]="labelClass" for="split">{{ terminology.singular }} Split Type</label>
        <select 
          [ngClass]="inputClass" 
          id="split"
          formControlName="split"
        >
          <option value="">Select split type</option>
          <option *ngFor="let splitType of splitTypes" [value]="splitType.code">
            {{ splitType.description }}
          </option>
        </select>
        <p *ngIf="hasError(createPoolForm, 'split', 'required')" [ngClass]="errorClass">
          Split type is required
        </p>
      </div>

      <div class="flex space-x-4">
        <button type="submit" [ngClass]="buttonClass" [disabled]="isLoading">
          {{ isLoading ? 'Creating...' : 'Create ' + terminology.singular }}
        </button>
        <button type="button" [ngClass]="buttonClass" (click)="changeView('find')">
          Cancel
        </button>
      </div>
    </form>
  </ng-container>

  <!-- Join Pool View -->
  <ng-container *ngIf="view === 'join'">
    <h2 [ngClass]="titleClass">{{ terminology.joinExisting }}</h2>
    
    <form [formGroup]="joinPoolForm" (ngSubmit)="requestToJoinPool()">
      <div [ngClass]="formGroupClass">
        <label [ngClass]="labelClass" for="poolMpacc">{{ terminology.singular }} Account Number</label>
        <input 
          [ngClass]="inputClass" 
          id="poolMpacc"
          type="text" 
          formControlName="poolMpacc" 
          placeholder="Enter {{ terminology.singular.toLowerCase() }} account number (MPACC)"
        >
        <p class="text-xs text-gray-600 mt-1">Ask the {{ terminology.singular.toLowerCase() }} administrator for the account number</p>
        <p *ngIf="hasError(joinPoolForm, 'poolMpacc', 'required')" [ngClass]="errorClass">
          {{ terminology.singular }} account number is required
        </p>
        <p *ngIf="hasError(joinPoolForm, 'poolMpacc', 'minlength')" [ngClass]="errorClass">
          Please enter a valid {{ terminology.singular.toLowerCase() }} account number (at least 8 characters)
        </p>
      </div>

      <div class="flex space-x-4">
        <button type="submit" [ngClass]="buttonClass" [disabled]="isLoading">
          {{ isLoading ? 'Requesting...' : 'Request to Join' }}
        </button>
        <button type="button" [ngClass]="buttonClass" (click)="changeView('find')">
          Cancel
        </button>
      </div>
    </form>
  </ng-container>

  <!-- Pending Invitation View -->
  <ng-container *ngIf="view === 'pending-invite'">
    <h2 [ngClass]="titleClass">{{ terminology.invitation }}</h2>
    
    <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <p class="text-blue-800 mb-4">
        You have been invited to join a {{ terminology.singular.toLowerCase() }}. Would you like to accept this invitation?
      </p>
      
      <div *ngIf="pendingInvitePool" class="mb-4">
        <p class="text-sm text-gray-600">{{ terminology.singular }} Name: <span class="font-semibold">{{ pendingInvitePool.POOLNAME }}</span></p>
        <p class="text-sm text-gray-600">{{ terminology.singular }} ID: <span class="font-semibold">{{ pendingInvitePool.ENTITYID }}</span></p>
      </div>
      
      <div class="flex space-x-4">
        <button [ngClass]="buttonClass" (click)="acceptInvite()" [disabled]="isLoading">
          {{ isLoading ? 'Processing...' : 'Accept Invitation' }}
        </button>
        <button 
          class="px-4 py-2 bg-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50"
          (click)="declineInvite()" 
          [disabled]="isLoading"
        >
          Decline
        </button>
      </div>
    </div>
  </ng-container>

  <!-- Pool Details View -->
  <div *ngIf="view === 'details' && poolInfo">
    <!-- <h2 [ngClass]="titleClass">{{ poolInfo.POOLNAME }}</h2> -->

    <!-- Show accept/reject options if user has pending invitation -->
    <div *ngIf="currentUserHasPendingInvite" class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <h3 class="text-lg font-semibold mb-2">You have been invited to join this {{ terminology.singular.toLowerCase() }}</h3>
      <p class="text-gray-700 mb-4">
        You have been invited to join "{{ poolInfo.POOLNAME }}". Would you like to accept this invitation?
      </p>
      <div class="flex space-x-4">
        <button [ngClass]="buttonClass" (click)="acceptInvite()" [disabled]="isLoading">
          {{ isLoading ? 'Processing...' : 'Accept Invitation' }}
        </button>
        <button 
          class="px-4 py-2 bg-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50"
          (click)="declineInvite()" 
          [disabled]="isLoading"
        >
          Decline
        </button>
      </div>
    </div>


    <h3 class="text-lg font-semibold mb-3">{{ terminology.management }}</h3>
    
    <!-- Members Requiring Action -->
    <div class="mb-6" *ngIf="isAdmin && poolMembersWithActions.length > 0">
      <h4 class="text-md font-semibold mb-2">Members Action</h4>
      <div class="members-table">
        <div class="table-header">
          <div class="table-cell header">Member</div>
          <div class="table-cell header">Type/Status</div>
          <div class="table-cell header">Actions</div>
        </div>
        <div class="table-row" *ngFor="let member of poolMembersWithActions">
          <div class="table-cell member-info">
            <div class="member-name">{{ member.NAME || 'Unknown' }}</div>
            <div class="member-id">{{ member.MPACC.trim() }}</div>
          </div>
          <div class="table-cell badges-column">
            <ion-badge [color]="member.TYPE === 'ADMN' ? 'primary' : 'medium'">
              {{ member.TYPE === 'ADMN' ? 'Admin' : 'Member' }}
            </ion-badge>
            <ion-badge [color]="getStatusColor(member)">
              {{ getStatusText(member) }}
            </ion-badge>
          </div>
          <div class="table-cell">
            <ion-button 
              *ngIf="member.INVITESTATUS === 'REQS'"
              size="small"
              color="success"
              (click)="approveJoinRequest(member.MPACC.trim())"
              [disabled]="isLoading"
            >
              Approve
            </ion-button>
            <ion-button 
              *ngIf="member.INVITESTATUS === 'INVT'"
              size="small"
              color="danger"
              (click)="removeMember(member.MPACC.trim())"
              [disabled]="isLoading"
            >
              Remove
            </ion-button>
            <ion-button 
              *ngIf="member.TYPE !== 'ADMN' && member.MEMBERSTATUS === 'STAA'"
              size="small"
              color="danger"
              (click)="removeMember(member.MPACC.trim())"
              [disabled]="isLoading"
            >
              Remove
            </ion-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Admin only - Invite Member form -->
    <div *ngIf="isAdmin">
      <h3 class="text-lg font-semibold mb-2">Invite Member</h3>
      
      <!-- Success message for invite -->
      <div *ngIf="inviteSuccessMessage" class="mb-3 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
        <p class="text-sm">{{ inviteSuccessMessage }}</p>
      </div>
      
      <!-- Error message for invite -->
      <div *ngIf="inviteErrorMessage" class="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
        <p class="text-sm">{{ inviteErrorMessage }}</p>
      </div>
      
      <form [formGroup]="inviteMemberForm" (ngSubmit)="inviteMember()">
        <div [ngClass]="formGroupClass">
          <label [ngClass]="labelClass" for="membershipNumber">Member's Membership Number</label>
          <input 
            [ngClass]="inputClass" 
            id="membershipNumber"
            type="text" 
            formControlName="membershipNumber" 
            placeholder="Membership Number"
          >
          <p class="text-xs text-gray-600 mt-1">Enter the membership number (e.g., 10000123) of the person you want to invite to this {{ terminology.singular.toLowerCase() }}</p>
          <p *ngIf="hasError(inviteMemberForm, 'membershipNumber', 'required')" [ngClass]="errorClass">
            Membership number is required
          </p>
          <p *ngIf="hasError(inviteMemberForm, 'membershipNumber', 'minlength')" [ngClass]="errorClass">
            Membership number must be at least 5 characters
          </p>
          <p *ngIf="hasError(inviteMemberForm, 'membershipNumber', 'pattern')" [ngClass]="errorClass">
            Please enter a valid membership number (numbers only)
          </p>
        </div>

        <button type="submit" [ngClass]="buttonClass" [disabled]="isLoading">
          {{ isLoading ? 'Sending...' : 'Send Invite' }}
        </button>
      </form>
    </div>

    <!-- Exit Pool Option - Only visible for non-admin active members (not invited users) -->
    <div class="mt-6 pt-4 border-t border-gray-200" *ngIf="!isAdmin && isActiveMember && !hasPoolInvite">
      <button 
        class="px-4 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 transition-colors duration-200"
        [disabled]="isLoading"
        (click)="exitPool()"
      >
        {{ isLoading ? 'Processing...' : 'Exit ' + terminology.singular }}
      </button>
      <p class="text-sm text-gray-500 mt-2">
        {{ terminology.exitWarning }}
      </p>
    </div>
  </div>
</div>
