import { Component, OnInit, OnChang<PERSON>, SimpleChanges, Input, Output, EventEmitter, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AccountPoolService, SystemService } from 'lp-client-api';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';

interface PoolResponse {
  ENTITYID: number;
  CIS: number;
  MPACC: string;
  ACCALIAS: string;
  POOLSPLIT: number;
  STATUS: string;
  BEGINDATE: string;
  ENDDATE: string | null;
  POOLNAME: string;
  LANGUAGE: string;
  EMAIL: string;
  CONTACTNUMBER: string;
  AUDITUSER: string;
  AUDITDATE: string;
  TOTALUNITS: number;
  members: PoolMember[];
}

interface PoolMember {
  MPACC: string;
  ACCALIAS: string;
  TYPE: string;
  PRIVACY: string;
  MEMBERSTATUS: string;
  BALANCE: number;
  ALLOWACTIVITY: string;
  ALLOWAWARD: string;
  INVITESTATUS: string | null;
  NAME: string;
  AUDITUSER: string;
  AUDITDATE: string;
  ACTIONS: string;
}

interface ApiResponse {
  status: string;
  [key: string]: any;
}

interface ApiError {
  error?: {
    message?: string;
  };
  [key: string]: any;
}

@Component({
  selector: 'lib-account-pool',
  templateUrl: './account-pool.component.html',
  styleUrls: ['./account-pool.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AccountPoolComponent implements OnInit, OnChanges {
  @Input() membershipNumber!: any;
  @Input() isActiveMember: boolean = false; // Whether user is an active pool member
  @Input() hasPoolInvite: boolean = false; // Whether user has a pending invitation
  @Input() poolInfo: PoolResponse | null = null; // Pool information if user is already in a pool
  @Input() containerClass = 'w-full max-w-md mx-auto p-4 bg-white rounded-lg shadow';
  @Input() titleClass = 'text-xl font-bold mb-4 text-center';
  @Input() formGroupClass = 'mb-4';
  @Input() labelClass = 'block text-sm font-medium text-gray-700 mb-1';
  @Input() inputClass = 'w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500';
  @Input() buttonClass = 'w-full py-2 px-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50';
  @Input() errorClass = 'text-sm text-red-600 mt-1';
  @Input() tableClass = 'w-full border-collapse';
  @Input() tableHeaderClass = 'bg-gray-100 text-left p-2 border';
  @Input() tableCellClass = 'p-2 border';
  @Input() terminology: any = {
    singular: 'Pool',
    plural: 'Pools',
    management: 'Pool Management',
    member: 'Pool Member',
    notInMessage: 'Member is currently not in a pool',
    createNew: 'Create New Pool',
    joinExisting: 'Join Existing Pool',
    invitation: 'Pool Invitation',
    exitConfirmTitle: 'Exit Pool',
    exitConfirmMessage: 'Are you sure you want to exit the pool',
    exitWarning: 'Warning: This action cannot be undone. You will need to be re-invited to rejoin the pool.',
    createdSuccess: 'Pool created successfully!',
    joinRequestSuccess: 'Join request sent successfully!',
    invitationSentSuccess: 'Invitation sent successfully!',
    exitSuccess: 'You have successfully exited the pool.',
    removeMemberSuccess: 'Member removed successfully!',
    approveJoinSuccess: 'Join request approved! Member has been added to the pool.',
    rejectJoinSuccess: 'Join request rejected and member removed.',
    acceptInviteSuccess: 'Successfully joined the pool!',
    inviteAcceptedToast: 'Pool invitation accepted successfully!',
    inviteDeclinedToast: 'Pool invitation declined',
    errorOccurred: 'An error occurred with the account pool',
    exitToast: 'You have successfully exited the pool',
    inviteToast: 'Successfully invited member {membershipNumber} to the pool!',
    loadingInfo: 'Loading pool information...',
    noInfoAvailable: 'No pool information available.',
    alreadyInAnother: 'This member is already in another pool and cannot join this one.',
    failedToCreate: 'Failed to create pool. Please try again.',
    failedToJoin: 'Failed to request pool join. Please try again.',
    failedToExit: 'Failed to exit pool. Please try again.',
    failedToLoad: 'Failed to load pool information'
  };

  @Output() poolCreated = new EventEmitter<any>();
  @Output() poolJoined = new EventEmitter<any>();
  @Output() inviteProcessed = new EventEmitter<any>();
  @Output() poolInviteAccepted = new EventEmitter<any>();
  @Output() poolExited = new EventEmitter<any>();
  @Output() error = new EventEmitter<any>();
  poolMembers: PoolMember[] = [];
  poolMembersWithActions: PoolMember[] = [];
  isAdmin = false;
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  inviteSuccessMessage = '';
  inviteErrorMessage = '';
  view: 'find' | 'create' | 'join' | 'details' | 'pending-invite' = 'find';
  currentUserHasPendingInvite = false;
  
  // Pending invitation data
  hasPendingInvite = false;
  pendingInvitePool: PoolResponse | null = null;
  
  // Pending join requests (for admins)
  pendingJoinRequests: PoolMember[] = [];
  
  createPoolForm: FormGroup = {} as FormGroup;
  joinPoolForm: FormGroup = {} as FormGroup;
  inviteMemberForm: FormGroup = {} as FormGroup;

  // Add property for split types
  splitTypes: Array<{code: string, description: string}> = [];
  
  // Add property for country dial codes
  countryDialCodes: Array<{code: string, name: string}> = [
    { code: '+27', name: 'South Africa' },
    { code: '+266', name: 'Lesotho' },
    { code: '+267', name: 'Botswana' },
    { code: '+264', name: 'Namibia' },
    { code: '+268', name: 'Eswatini' },
    { code: '+258', name: 'Mozambique' },
    { code: '+1', name: 'United States' },
    { code: '+44', name: 'United Kingdom' },
    { code: '+33', name: 'France' },
    { code: '+49', name: 'Germany' },
    { code: '+61', name: 'Australia' },
    { code: '+81', name: 'Japan' }
  ];
  constructor(
    private fb: FormBuilder,
    private accountPoolService: AccountPoolService,
    private systemService: SystemService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.initForms();
    this.loadSplitTypes();
    if (this.membershipNumber) {
      // If poolInfo is already provided, use it
      if (this.poolInfo) {
        this.handlePoolInfoFromParent();
      } else {
        // Only check for invites, don't call findPool here
        // The parent component should provide poolInfo if user is in a pool
        this.checkForPendingInvites();
      }
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Input properties changed - component will re-render automatically
    if (changes['isActiveMember'] || changes['hasPoolInvite'] || changes['poolInfo']) {
      console.log('Account-pool component inputs changed:', {
        isActiveMember: this.isActiveMember,
        hasPoolInvite: this.hasPoolInvite,
        poolInfo: this.poolInfo
      });
      
      // If poolInfo is provided, use it instead of fetching
      if (changes['poolInfo'] && this.poolInfo) {
        this.handlePoolInfoFromParent();
      }
    }
  }

  private handlePoolInfoFromParent(): void {
    console.log('=== USING POOL INFO FROM PARENT ===');
    console.log('Pool info:', this.poolInfo);
    
    if (this.poolInfo && this.poolInfo.members) {
      this.poolMembers = this.poolInfo.members;
      
      // Check if current user is an admin
      const adminMember = this.poolMembers.find(
        member => member.MPACC.trim() === this.membershipNumber && member.TYPE === 'ADMN'
      );
      
      this.isAdmin = !!adminMember;
      console.log('Current user is admin:', this.isAdmin);
      
      // Check if current user has a pending invitation
      const currentUserInPool = this.poolMembers.find(
        member => member.MPACC.trim() === this.membershipNumber
      );
      
      if (currentUserInPool && currentUserInPool.INVITESTATUS === 'INVT') {
        this.currentUserHasPendingInvite = true;
      } else {
        this.currentUserHasPendingInvite = false;
      }
      
      // Filter members who have actions available for admins
      if (this.isAdmin) {
        this.poolMembersWithActions = this.poolMembers.filter(
          member => {
            // Show members with join requests
            if (member.INVITESTATUS === 'REQS') return true;
            // Show invited members that can be removed
            if (member.INVITESTATUS === 'INVT') return true;
            // Show non-admin active members that can be removed
            if (member.TYPE !== 'ADMN' && member.MEMBERSTATUS === 'STAA') return true;
            return false;
          }
        );
      } else {
        this.poolMembersWithActions = [];
      }
      
      // Set view to details since we have pool info
      this.view = 'details';
    }
  }

  private loadSplitTypes(): void {
    this.systemService.getCodeGroup('PSLT').subscribe({
      next: (response: any) => {
        if (response && response.codeItem && Array.isArray(response.codeItem)) {
          this.splitTypes = response.codeItem.map((item: any) => ({
            code: item.code || item.CODE,
            description: item.description || item.DESCRIPTION
          }));
        }
      },
      error: (error: any) => {
        console.error('Failed to load split types:', error);
        // Fallback to hardcoded values if API fails
        this.splitTypes = [
          { code: '1', description: 'Equal Split' },
          { code: '2', description: 'Percentage Split' },
          { code: '3', description: 'Admin Control' }
        ];
      }
    });
  }

  private initForms(): void {
    this.createPoolForm = this.fb.group({
      name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      countryCode: ['', [Validators.required]],
      telephone: ['', [Validators.required]],
      split: ['', [Validators.required]] // Changed from 0 to empty string
    });

    this.joinPoolForm = this.fb.group({
      poolMpacc: ['', [Validators.required, Validators.minLength(8)]]
    });

    this.inviteMemberForm = this.fb.group({
      membershipNumber: ['', [Validators.required, Validators.minLength(5), Validators.pattern(/^\d+$/)]]
    });
  }

  findPool(): void {
    console.log('=== FIND POOL DEBUG ===');
    console.log('Searching for pool with membership number:', this.membershipNumber);
    
    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.accountPoolService.findPool(this.membershipNumber).subscribe({
      next: (response: PoolResponse) => {
        console.log('Pool found successfully:', response);
        console.log('Pool ENTITYID:', response?.ENTITYID);
        console.log('Pool MPACC:', response?.MPACC);
        console.log('Pool Name:', response?.POOLNAME);
        console.log('Number of members:', response?.members?.length);
        
        this.poolInfo = response;
        
        if (response && response.members) {
          this.poolMembers = response.members;
          
          console.log('Pool members:', this.poolMembers);
          
          // Check if current user is an admin
          const adminMember = this.poolMembers.find(
            member => member.MPACC.trim() === this.membershipNumber && member.TYPE === 'ADMN'
          );
          
          this.isAdmin = !!adminMember;
          console.log('Current user is admin:', this.isAdmin);
          console.log('Admin member found:', adminMember);
          
          // Also check if current user is in the pool at all
          const currentUserInPool = this.poolMembers.find(
            member => member.MPACC.trim() === this.membershipNumber
          );
          console.log('Current user in pool:', currentUserInPool);
          
          // Check if current user has a pending invitation
          if (currentUserInPool && currentUserInPool.INVITESTATUS === 'INVT') {
            this.currentUserHasPendingInvite = true;
            console.log('Current user has pending invitation');
          } else {
            this.currentUserHasPendingInvite = false;
          }
          
          // Filter pending join requests for admins
          if (this.isAdmin) {
            this.pendingJoinRequests = this.poolMembers.filter(
              member => member.INVITESTATUS === 'REQS' || member.MEMBERSTATUS === 'PEND'
            );
            console.log('Pending join requests:', this.pendingJoinRequests);
            
            // Filter members who have actions available (can be removed or have pending requests)
            this.poolMembersWithActions = this.poolMembers.filter(
              member => {
                // Show members with join requests
                if (member.INVITESTATUS === 'REQS') return true;
                // Show invited members that can be removed
                if (member.INVITESTATUS === 'INVT') return true;
                // Show non-admin active members that can be removed
                if (member.TYPE !== 'ADMN' && member.MEMBERSTATUS === 'STAA') return true;
                return false;
              }
            );
          } else {
            this.poolMembersWithActions = [];
          }
          
          this.view = 'details';
        } else {
          console.log('No pool found or no members in response');
          this.view = 'find';
        }
        
        this.isLoading = false;
      },
      error: (error: ApiError) => {
        console.error('=== FIND POOL ERROR ===');
        console.error('Error finding pool:', error);
        console.error('Error status:', error?.['status']);
        console.error('Error message:', error?.['error']?.['message']);

        this.isLoading = false;

        // Don't emit error or show error message for 404 - user simply not in a pool
        if (error?.['status'] === 404) {
          console.log('404 error - user not in pool (normal state)');
          this.errorMessage = ''; // Clear any error message
        } else {
          // Only emit error and show message for actual errors
          this.errorMessage = this.handleApiError(error, 'Failed to find pool information.');
          this.error.emit(error);
        }

        // If error, probably no pool exists, so stay on find view
        this.view = 'find';
      }
    });
  }

  createPool(): void {
    if (this.createPoolForm.invalid) {
      this.markFormGroupTouched(this.createPoolForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const { name, email, countryCode, telephone, split } = this.createPoolForm.value;

    this.accountPoolService.createPool(
      this.membershipNumber,
      'en', // Default language, could be made configurable
      name,
      email,
      countryCode,
      telephone,
      split
    ).subscribe({
      next: (response: ApiResponse) => {
        this.isLoading = false;
        this.successMessage = this.terminology.createdSuccess;
        this.poolCreated.emit(response);
        this.createPoolForm.reset();
        this.findPool(); // Refresh pool info
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, this.terminology.failedToCreate);
        this.error.emit(error);
      }
    });
  }

  requestToJoinPool(): void {
    console.log('=== REQUEST TO JOIN POOL DEBUG ===');
    console.log('Form value:', this.joinPoolForm.value);
    console.log('Form valid:', this.joinPoolForm.valid);
    
    if (this.joinPoolForm.invalid) {
      console.error('Join pool form is invalid');
      this.markFormGroupTouched(this.joinPoolForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const { poolMpacc } = this.joinPoolForm.value;
    
    console.log('Looking up pool by MPACC:', poolMpacc);

    // First, find the pool by MPACC to get its ENTITYID
    this.accountPoolService.findPoolByMpacc(poolMpacc).subscribe({
      next: (poolResponse: PoolResponse) => {
        console.log('Pool found by MPACC:', poolResponse);
        
        if (!poolResponse || !poolResponse.ENTITYID) {
          this.isLoading = false;
          this.errorMessage = 'Pool not found. Please check the account number and try again.';
          return;
        }
        
        console.log('Pool ENTITYID:', poolResponse.ENTITYID);
        console.log('Pool Name:', poolResponse.POOLNAME);
        
        // Now join the pool using the ENTITYID
        console.log('Requesting to join pool with:', {
          poolId: poolResponse.ENTITYID,
          membershipNumber: this.membershipNumber,
          action: 'REQS',
          auditUser: this.membershipNumber
        });

        this.accountPoolService.joinPool(
          poolResponse.ENTITYID,
          this.membershipNumber,
          'REQS', // Request to join
          this.membershipNumber
        ).subscribe({
          next: (response: ApiResponse) => {
            console.log('Join pool request successful:', response);
            this.isLoading = false;
            this.successMessage = this.terminology.joinRequestSuccess;
            this.poolJoined.emit(response);
            this.joinPoolForm.reset();
          },
          error: (error: ApiError) => {
            console.error('=== JOIN POOL ERROR ===');
            console.error('Full error object:', error);
            console.error('Error status:', error?.['status']);
            console.error('Error message:', error?.['error']?.['message']);
            console.error('Error details:', error?.['error']);
            
            this.isLoading = false;
            this.errorMessage = this.handleApiError(error, this.terminology.failedToJoin);
            this.error.emit(error);
          }
        });
      },
      error: (error: ApiError) => {
        console.error('=== FIND POOL BY MPACC ERROR ===');
        console.error('Error finding pool by MPACC:', error);

        this.isLoading = false;

        // Provide specific error messages but don't emit error for 404s
        if (error?.['status'] === 404) {
          this.errorMessage = 'Pool not found. Please check the account number and try again.';
          // Don't emit error for 404 - this is user input validation, not a system error
        } else {
          this.errorMessage = this.handleApiError(error, 'Failed to find pool. Please check the account number and try again.');
          this.error.emit(error);
        }
      }
    });
  }

  inviteMember(): void {
    if (this.inviteMemberForm.invalid || !this.poolInfo?.ENTITYID) {
      this.markFormGroupTouched(this.inviteMemberForm);
      return;
    }

    this.isLoading = true;
    this.inviteErrorMessage = '';
    this.inviteSuccessMessage = '';

    const { membershipNumber } = this.inviteMemberForm.value;

    // Log the values being sent for debugging
    console.log('Inviting member with the following details:');
    console.log('Pool ID (ENTITYID):', this.poolInfo!.ENTITYID);
    console.log('Pool Account (MPACC):', this.poolInfo!.MPACC);
    console.log('Invitee Membership Number:', membershipNumber);
    console.log('Current User (Inviter):', this.membershipNumber);

    this.accountPoolService.joinPool(
      this.poolInfo!.ENTITYID,
      membershipNumber,
      'INVT', // Invite
      this.membershipNumber
    ).subscribe({
      next: (response: ApiResponse) => {
        this.isLoading = false;
        this.inviteSuccessMessage = this.terminology.invitationSentSuccess;
        this.inviteErrorMessage = '';
        this.inviteMemberForm.reset();
        
        // Emit success event
        this.inviteProcessed.emit({
          type: 'invite',
          membershipNumber: membershipNumber,
          response: response
        });
        
        // Clear success message after 5 seconds
        setTimeout(() => {
          this.inviteSuccessMessage = '';
        }, 5000);
        
        this.findPool(); // Refresh pool info
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        console.error('Invite member error:', error);
        
        // Provide more specific error messages
        let errorMsg = 'Failed to send invitation. ';
        if (error?.['status'] === 400) {
          errorMsg += 'The membership number may be invalid or the member may already be in a pool.';
        } else if (error?.['status'] === 404) {
          errorMsg += 'The membership number was not found.';
        } else if (error?.['status'] === 409) {
          errorMsg += 'This member may already have a pending invitation or is already in the pool.';
        } else {
          errorMsg += 'Please check the membership number and try again.';
        }
        
        // Don't use handleApiError for invite errors as it returns generic pool messages
        this.inviteErrorMessage = errorMsg;
        this.inviteSuccessMessage = '';
        this.error.emit(error);
      }
    });
  }

  removeMember(membershipNumber: string): void {
    console.log('=== REMOVE MEMBER ===');
    console.log('Admin status:', this.isAdmin);
    console.log('Pool ENTITYID:', this.poolInfo?.ENTITYID);
    console.log('Member to remove:', membershipNumber);
    console.log('Admin performing action:', this.membershipNumber);
    
    if (!this.isAdmin || !this.poolInfo?.ENTITYID) {
      this.errorMessage = 'You do not have permission to perform this action.';
      return;
    }

    // Find the member to get their name for confirmation
    const memberToRemove = this.poolMembers.find(m => m.MPACC.trim() === membershipNumber);
    const memberName = memberToRemove?.NAME || membershipNumber;

    // Show confirmation dialog
    const confirmed = confirm(
      `Are you sure you want to remove "${memberName}" from the ${this.terminology.singular.toLowerCase()}?\n\n` +
      `This action cannot be undone. The member will lose access to all ${this.terminology.singular.toLowerCase()} benefits and ` +
      `will need to be re-invited to join again.`
    );

    if (!confirmed) {
      console.log('Remove member cancelled by user');
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    console.log('Calling processPoolInvite with:');
    console.log('poolId (ENTITYID):', this.poolInfo.ENTITYID);
    console.log('poolId type:', typeof this.poolInfo.ENTITYID);
    console.log('Full poolInfo:', this.poolInfo);
    
    this.accountPoolService.processPoolInvite(
      this.poolInfo.ENTITYID, // Use ENTITYID as poolId
      membershipNumber,
      'REMV', // Remove member
      this.membershipNumber
    ).subscribe({
      next: (response: ApiResponse) => {
        console.log('Remove member successful:', response);
        this.isLoading = false;
        this.successMessage = this.terminology.removeMemberSuccess;
        
        // Add a small delay before refreshing to allow backend processing
        setTimeout(() => {
          this.findPool(); // Refresh pool info
        }, 1000);
      },
      error: (error: any) => {
        console.error('=== REMOVE MEMBER ERROR ===');
        console.error('Full error:', error);
        console.error('Error status:', error?.status);
        console.error('Error message:', error?.error?.message || error?.message);
        console.error('Error detail:', error?.error?.detail);
        
        this.isLoading = false;
        
        // Provide specific error messages
        let errorMsg = 'Failed to remove member. ';
        if (error?.error?.message) {
          errorMsg = error.error.message;
        } else if (error?.error?.detail) {
          errorMsg = error.error.detail;
        }
        
        this.errorMessage = errorMsg;
        this.error.emit(error);
      }
    });
  }

  exitPool(): void {
    console.log('=== EXIT POOL DEBUG ===');
    console.log('Pool Info:', this.poolInfo);
    console.log('Pool ENTITYID:', this.poolInfo?.ENTITYID);
    console.log('Pool Name:', this.poolInfo?.POOLNAME);
    console.log('Membership Number:', this.membershipNumber);
    console.log('Is Admin:', this.isAdmin);
    
    if (!this.poolInfo?.ENTITYID) {
      console.error('Exit pool failed: No ENTITYID available');
      this.errorMessage = this.terminology.noInfoAvailable;
      return;
    }

    // Show confirmation dialog
    const confirmed = confirm(
      `Are you sure you want to exit the ${this.terminology.singular.toLowerCase()} "${this.poolInfo.POOLNAME}"?\n\n` +
      `This action cannot be undone. You will lose access to all ${this.terminology.singular.toLowerCase()} benefits and ` +
      `will need to be re-invited to join again.`
    );

    if (!confirmed) {
      console.log('Exit pool cancelled by user');
      return;
    }

    console.log('User confirmed exit - proceeding with API call');
    console.log('API Parameters:', {
      poolId: this.poolInfo.ENTITYID,
      membershipNumber: this.membershipNumber,
      type: 'EXIT',
      auditUser: this.membershipNumber
    });

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.accountPoolService.processPoolInvite(
      this.poolInfo.ENTITYID, // Use ENTITYID as poolId
      this.membershipNumber,
      'EXIT', // Exit pool
      this.membershipNumber
    ).subscribe({
      next: (response: ApiResponse) => {
        console.log('Exit pool successful:', response);
        this.isLoading = false;
        this.successMessage = 'You have successfully exited the pool.';
        
        // Emit event to parent component
        this.poolExited.emit({
          response: response,
          poolId: this.poolInfo?.ENTITYID,
          poolName: this.poolInfo?.POOLNAME,
          membershipNumber: this.membershipNumber
        });
        
        // Clear pool data
        this.poolInfo = null;
        this.poolMembers = [];
        this.isAdmin = false;
        
        // Small delay to show success message before cleaning up
        setTimeout(() => {
          this.successMessage = '';
        }, 2000);
      },
      error: (error: ApiError) => {
        console.error('=== EXIT POOL ERROR ===');
        console.error('Full error object:', error);
        console.error('Error status:', error?.['status']);
        console.error('Error message:', error?.['error']?.['message']);
        console.error('Error details:', error?.['error']);
        
        this.isLoading = false;
        const errorMsg = this.handleApiError(error, 'Failed to exit pool. Please try again.');
        this.errorMessage = errorMsg;
        this.error.emit({
          type: 'exitPoolError',
          message: errorMsg,
          originalError: error
        });
      }
    });
  }

  changeView(view: 'find' | 'create' | 'join' | 'details' | 'pending-invite'): void {
    this.view = view;
  }
  
  /**
   * Check if the member has any pending pool invitations
   */
  private checkForPendingInvites(): void {
    this.accountPoolService.checkInviteStatus(this.membershipNumber).subscribe({
      next: (response: any) => {
        console.log('Invite status response:', response);
        if (response && response.status === 'Y') {
          this.hasPendingInvite = true;
          // We'll need to get the pool details for the pending invite
          // For now, we'll show a notification
          this.successMessage = 'You have a pending pool invitation!';
        }
      },
      error: (error: any) => {
        console.error('Error checking invite status:', error);
      }
    });
  }
  
  /**
   * Accept a pending pool invitation
   */
  acceptInvite(): void {
    // Use poolInfo if available (when viewing pool details with pending invite)
    const pool = this.pendingInvitePool || this.poolInfo;
    
    if (!pool?.ENTITYID) {
      this.errorMessage = 'No pending invitation found.';
      return;
    }
    
    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';
    
    this.accountPoolService.processPoolInvite(
      pool.ENTITYID, // Use ENTITYID as poolId
      this.membershipNumber,
      'ACCP', // Accept invitation
      this.membershipNumber
    ).subscribe({
      next: (response: ApiResponse) => {
        console.log('Accept invite successful:', response);
        this.isLoading = false;
        this.successMessage = 'Successfully joined the pool!';
        this.hasPendingInvite = false;
        this.pendingInvitePool = null;
        setTimeout(() => {
          console.log('Navigating back to home after accepting invite');
          this.router.navigate(['/app/home']);
        }, 2000);
        this.poolInviteAccepted.emit(response);
        // Refresh pool info
        this.findPool();
      },
      error: (error: ApiError) => {
        console.error('Accept invite error:', error);
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, 'Failed to accept invitation. Please try again.');
        this.error.emit(error);
      }
    });
  }
  
  /**
   * Decline a pending pool invitation
   */
  declineInvite(): void {
    if (!this.pendingInvitePool?.ENTITYID) {
      this.errorMessage = 'No pending invitation found.';
      return;
    }
    
    // For declining, we might need a different approach since there's no specific DECLINE type
    // We could use EXIT or simply not accept the invitation
    this.hasPendingInvite = false;
    this.pendingInvitePool = null;
    this.successMessage = 'Invitation declined.';
    this.view = 'find';
  }
  
  /**
   * Admin approves a join request
   */
  approveJoinRequest(memberMpacc: string): void {
    if (!this.isAdmin || !this.poolInfo?.ENTITYID) {
      this.errorMessage = 'You do not have permission to perform this action.';
      return;
    }
    
    console.log('=== APPROVE JOIN REQUEST ===');
    console.log('Pool Info:', this.poolInfo);
    console.log('Pool ENTITYID:', this.poolInfo.ENTITYID);
    console.log('Pool MPACC:', this.poolInfo.MPACC);
    console.log('Member to approve:', memberMpacc);
    console.log('Admin performing action:', this.membershipNumber);
    
    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';
    
    // Use process-invite with ACCP type to approve the join request
    console.log('Using pool ENTITYID as poolId:', this.poolInfo.ENTITYID);
    
    this.accountPoolService.processPoolInvite(
      this.poolInfo.ENTITYID, // Use ENTITYID as poolId
      memberMpacc,
      'ACCP', // ACCP type to accept/approve the request
      this.membershipNumber
    ).subscribe({
      next: (response: ApiResponse) => {
        console.log('=== APPROVE JOIN REQUEST RESPONSE ===');
        console.log('Full response:', response);
        console.log('Response status:', response?.status);
        console.log('Response data:', JSON.stringify(response, null, 2));
        
        this.isLoading = false;
        this.successMessage = 'Join request approved! Member has been added to the pool.';
        
        // Add a small delay before refreshing to allow backend processing
        console.log('Waiting 1 second before refreshing pool data...');
        setTimeout(() => {
          console.log('Refreshing pool data now...');
          // Refresh pool info to update member list
          this.findPool();
        }, 1000);
      },
      error: (error: any) => {
        console.error('Approve join request error:', error);
        console.error('Error details:', {
          status: error?.status,
          message: error?.error?.detail || error?.error?.message,
          errorCode: error?.error?.errorCode
        });
        this.isLoading = false;
        
        // Provide specific error messages
        let errorMsg = 'Failed to approve join request. ';
        if (error?.error?.errorCode === '100070') {
          errorMsg = this.terminology.alreadyInAnother;
        } else if (error?.error?.detail) {
          errorMsg = error.error.detail;
        }
        
        this.errorMessage = errorMsg;
        this.error.emit(error);
      }
    });
  }
  
  /**
   * Admin rejects a join request
   */
  rejectJoinRequest(memberMpacc: string): void {
    if (!this.isAdmin || !this.poolInfo?.ENTITYID) {
      this.errorMessage = 'You do not have permission to perform this action.';
      return;
    }
    
    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';
    
    // Use REMV to reject/remove the pending join request
    this.accountPoolService.processPoolInvite(
      this.poolInfo.ENTITYID, // Use ENTITYID as poolId
      memberMpacc,
      'REMV', // Remove the member with pending request
      this.membershipNumber
    ).subscribe({
      next: (response: ApiResponse) => {
        console.log('Reject join request successful:', response);
        this.isLoading = false;
        this.successMessage = this.terminology.rejectJoinSuccess;
        // Refresh pool info to update member list
        this.findPool();
      },
      error: (error: ApiError) => {
        console.error('Reject join request error:', error);
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, 'Failed to reject join request. Please try again.');
        this.error.emit(error);
      }
    });
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  hasError(form: FormGroup, controlName: string, errorType: string): boolean {
    const control = form.get(controlName);
    return !!control && control.touched && control.hasError(errorType);
  }

  /**
   * Get member status text for display
   * @param member The member object
   * @returns Status text
   */
  getMemberStatusText(member: PoolMember): string {
    if (member.INVITESTATUS === 'INVT') {
      return 'Invited';
    } else if (member.INVITESTATUS === 'REQS') {
      return 'Join Request';
    } else if (member.MEMBERSTATUS === 'PEND') {
      return 'Pending';
    } else if (member.MEMBERSTATUS === 'STAA') {
      return 'Active';
    }
    return this.formatStatus(member.MEMBERSTATUS || 'STAA');
  }

  /**
   * Alias for getMemberStatusText for template compatibility
   */
  getStatusText(member: PoolMember): string {
    return this.getMemberStatusText(member);
  }

  /**
   * Get status color for ion-badge
   * @param member The member object
   * @returns Color string for ion-badge
   */
  getStatusColor(member: PoolMember): string {
    if (member.INVITESTATUS === 'INVT') {
      return 'warning'; // Yellow for invited
    } else if (member.INVITESTATUS === 'REQS') {
      return 'tertiary'; // Purple for join request
    } else if (member.MEMBERSTATUS === 'PEND') {
      return 'warning'; // Yellow for pending
    } else if (member.MEMBERSTATUS === 'STAA') {
      return 'success'; // Green for active
    }
    return 'medium'; // Default gray
  }

  /**
   * Get CSS class for member status
   * @param member The member object
   * @returns CSS class string
   */
  getMemberStatusClass(member: PoolMember): string {
    if (member.INVITESTATUS === 'INVT') {
      return 'text-yellow-600 font-medium'; // Invited
    } else if (member.INVITESTATUS === 'REQS') {
      return 'text-purple-600 font-medium'; // Join request
    } else if (member.MEMBERSTATUS === 'PEND') {
      return 'text-yellow-600 font-medium'; // Pending
    } else if (member.MEMBERSTATUS === 'STAA') {
      return 'text-green-600 font-medium'; // Active
    }
    return 'text-gray-600'; // Default
  }

  private handleApiError(error: any, fallback: string): string {
    // Check for specific 404 error indicating member not in pool
    if (error?.['status'] === 404) {
      return this.terminology.notInMessage;
    }
    
    // Check if error.error is a string containing '404'
    if (error?.error && typeof error.error === 'string' && error.error.includes('404')) {
      return this.terminology.notInMessage;
    }
    
    // Check for HTTP failure response patterns
    if (error?.message && typeof error.message === 'string' && error.message.includes('Http failure response')) {
      return this.terminology.notInMessage;
    }
    
    // Check for error message in various formats
    if (error?.error?.message) {
      return error.error.message;
    }
    if (error?.message) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    
    // If error.error is an object with useful information, try to extract it
    if (error?.error && typeof error.error === 'object') {
      // Try to create a meaningful message from the error object
      if (error.error.poolId || error.error.mpacc) {
        return `Operation failed for pool ${error.error.poolId || 'unknown'} and member ${error.error.mpacc || 'unknown'}`;
      }
    }
    
    return fallback;
  }

  /**
   * Parses a date string that may contain timezone indicator like [UTC]
   * @param dateString The date string to parse
   * @returns A valid JavaScript Date object or null if parsing fails
   */
  /**
   * Formats status code to human readable text
   * @param status The status code to format
   * @returns Formatted status text
   */
  formatStatus(status: string): string {
    const statusMap: { [key: string]: string } = {
      'STAA': 'Active',
      'INAC': 'Inactive',
      'PEND': 'Pending',
      'SUSP': 'Suspended'
    };
    return statusMap[status] || status;
  }

  parseDate(dateString: string | null | undefined): Date | null {
    if (!dateString) {
      return null;
    }
    
    try {
      // Remove timezone indicator if present
      const cleanDateString = dateString.replace(/\[.*\]$/, '');
      
      // Create a date object from the cleaned string
      const date = new Date(cleanDateString);
      
      // Check if the date is valid
      return isNaN(date.getTime()) ? null : date;
    } catch (error) {
      console.error('Error parsing date:', error);
      return null;
    }
  }
}
