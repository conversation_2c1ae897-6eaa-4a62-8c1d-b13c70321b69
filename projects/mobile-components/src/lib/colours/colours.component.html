
        <ion-accordion-group>
            <ion-accordion :value="color" *ngFor="let color of colors">
              <ion-item slot="header">
                <ion-label>{{color}}</ion-label>
              </ion-item>
              <div slot="content">
                    <div>
                        <ion-row>
                            <ion-col>
                                <ion-input 
                                class="inputs" 
                                label-placement="floating" fill="outline"
                                [colorPicker]="color"
                                [label]="color"
                                (colorPickerChange)="changeThemeColor($event, color, '')"
                                />
                            </ion-col>
                            <ion-col>
                                <ion-input 
                                label-placement="floating" fill="outline"
                                class="inputs" 
                                [colorPicker]="color"
                                [label]="color +' contrast'"
                                (colorPickerChange)="changeThemeColorContrast($event, color, '')"
                                />
                            </ion-col>
                        </ion-row>
                      
                        <ion-button [class]="color" >Main</ion-button>
                        <ion-button [class]="color + 'Tint'" >Tint</ion-button>
                        <ion-button [class]="color + 'Shade'" >Shade</ion-button>
                        <ion-button [class]="color+ 'Contrast'" >Contrast</ion-button>
                    </div>
              </div>
            </ion-accordion>
          </ion-accordion-group>



 