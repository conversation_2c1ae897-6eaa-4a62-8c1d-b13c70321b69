# Page Wrapper Component

A reusable layout template component that provides consistent padding, spacing, and structure across all pages in the application.

## Features

- Consistent padding and spacing across all pages
- Responsive design with mobile, tablet, and desktop breakpoints
- Support for different layouts (single column, grid, custom)
- Optional hero section with overlap effect
- Multiple container sizes
- Automatic mobile/desktop detection

## Usage

### Basic Single Column Layout

```html
<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo [names]="userName" type="dashboard"></lib-head-logo>
  </div>
  
  <div class="section-card">
    <h2>Your Content Here</h2>
    <p>This will have consistent padding and spacing.</p>
  </div>
</lib-page-wrapper>
```

### Two Column Grid Layout (like the home page)

```html
<lib-page-wrapper [containerSize]="'xl'" [layout]="'grid'" [hasBackground]="true">
  <div header>
    <lib-head-logo [names]="userName" type="home"></lib-head-logo>
  </div>
  
  <!-- Main content (2/3 width) -->
  <div main>
    <div class="section-card">
      <h2>Transaction History</h2>
      <!-- Chart content -->
    </div>
  </div>
  
  <!-- Sidebar (1/3 width) -->
  <div sidebar>
    <div class="section-card">
      <h2>Quick Actions</h2>
      <!-- Quick action buttons -->
    </div>
  </div>
</lib-page-wrapper>
```

### With Hero Section

```html
<lib-page-wrapper [hasHero]="true" [containerSize]="'lg'">
  <div header>
    <lib-head-logo [names]="userName" type="profile"></lib-head-logo>
  </div>
  
  <div hero>
    <div class="hero-card">
      <!-- Hero content that overlaps with main content -->
      <h1>Welcome Back!</h1>
    </div>
  </div>
  
  <div class="section-card">
    <!-- Main content -->
  </div>
</lib-page-wrapper>
```

## Input Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `showHeader` | boolean | false | Whether to add padding for a fixed header |
| `hasHero` | boolean | false | Enable hero section with overlap effect |
| `hasBackground` | boolean | true | Apply theme background color |
| `containerSize` | 'sm' \| 'md' \| 'lg' \| 'xl' \| 'full' | 'lg' | Maximum width of content container |
| `layout` | 'single' \| 'grid' \| 'custom' | 'single' | Layout type for content |

## Container Sizes

- **sm**: 640px max width
- **md**: 768px max width  
- **lg**: 1024px max width
- **xl**: 1200px max width
- **full**: 100% width

## Content Slots

| Slot | Description |
|------|-------------|
| `[header]` | Header content (typically lib-head-logo) |
| `[hero]` | Hero section that overlaps main content |
| `[main]` | Main content area (for grid layout) |
| `[sidebar]` | Sidebar content (for grid layout) |
| `[custom]` | Custom layout content |
| (default) | Main content for single column layout |

## CSS Classes Available

The component provides these CSS classes that child components can use:

- `.section-card` - Standard white card with shadow and padding
- `.hero-card` - Card that overlaps from hero section

## Responsive Behavior

The component automatically adjusts padding and spacing:

**Mobile (< 768px)**
- Padding: 16px horizontal
- Cards stack vertically in grid layout

**Tablet (768px - 1199px)**
- Padding: 32px horizontal
- Grid layout becomes 2 columns

**Desktop (≥ 1200px)**
- Padding: 40px horizontal (or centered with max-width)
- Optimal spacing and hover effects

## Migration Example

### Before (Custom padding in each component)
```scss
.desktop-hero {
  margin: 0 32px 32px 32px;
  padding: 32px;
  // ... custom styles
}
```

### After (Using page-wrapper)
```html
<lib-page-wrapper [layout]="'grid'" [containerSize]="'xl'">
  <!-- Content automatically gets consistent padding -->
</lib-page-wrapper>
```

## Benefits

1. **Consistency** - All pages have the same padding and spacing
2. **Maintainability** - Change spacing in one place affects all pages
3. **Responsive** - Built-in responsive breakpoints
4. **Flexibility** - Multiple layout options and container sizes
5. **Clean Code** - Remove repetitive padding/margin CSS from components