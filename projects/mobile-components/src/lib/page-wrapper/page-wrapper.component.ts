import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-page-wrapper',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <ion-content [scrollY]="true" class="page-wrapper" 
                 [class.with-header]="showHeader"
                 [class.desktop-layout]="!isMobile"
                 [class.with-background]="hasBackground">
      
      <!-- Header Section (optional) -->
      <ng-content select="[header]"></ng-content>
      
      <!-- Hero Section (optional) -->
      <div class="hero-section" *ngIf="hasHero">
        <ng-content select="[hero]"></ng-content>
      </div>
      
      <!-- Main Content -->
      <div class="page-content">
        <div class="page-container" [class]="'container-' + containerSize">
          <!-- Single Column Layout -->
          <div class="content-single" *ngIf="layout === 'single'">
            <ng-content></ng-content>
          </div>
          
          <!-- Two Column Layout -->
          <div class="content-grid" *ngIf="layout === 'grid'">
            <div class="content-main">
              <ng-content select="[main]"></ng-content>
            </div>
            <div class="content-sidebar">
              <ng-content select="[sidebar]"></ng-content>
            </div>
          </div>
          
          <!-- Custom Layout -->
          <ng-content select="[custom]" *ngIf="layout === 'custom'"></ng-content>
        </div>
      </div>
    </ion-content>
  `,
  styleUrls: ['./page-wrapper.component.scss']
})
export class PageWrapperComponent {
  @Input() showHeader: boolean = false;
  @Input() hasHero: boolean = false;
  @Input() hasBackground: boolean = true;
  @Input() containerSize: 'sm' | 'md' | 'lg' | 'xl' | 'full' = 'lg';
  @Input() layout: 'single' | 'grid' | 'custom' = 'single';
  
  get isMobile(): boolean {
    return typeof window !== 'undefined' && window.innerWidth < 768;
  }
}