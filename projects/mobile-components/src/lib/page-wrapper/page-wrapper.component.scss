// Consistent page layout styles
.page-wrapper {
  height: 100%;
  width: 100%;
  position: relative;
  
  &.with-background {
    background: var(--ion-color-base, #0072bc);
  }
  
  &.desktop-layout {
    background: var(--ion-color-base, #0072bc);
  }
}

// Hero Section (appears after header, overlaps with content)
.hero-section {
  position: relative;
  z-index: 1;
  
  // Mobile
  padding: 0 16px;
  margin-bottom: -30px;
  
  @media (min-width: 768px) {
    padding: 0 32px;
  }
  
  @media (min-width: 1200px) {
    padding: 0;
    max-width: 1200px;
    margin: 0 auto -30px auto;
  }
}

// Main Content Area
.page-content {
  position: relative;
  z-index: 2;
  width: 100%;
  
  // Mobile padding
  padding: 0 16px 24px;
  
  @media (min-width: 768px) {
    padding: 0 32px 40px;
  }
  
  @media (min-width: 1200px) {
    padding: 0 40px 48px;
  }
}

// Container sizes
.page-container {
  width: 100%;
  margin: 0 auto;
  
  &.container-sm {
    max-width: 640px;
  }
  
  &.container-md {
    max-width: 768px;
  }
  
  &.container-lg {
    max-width: 1024px;
  }
  
  &.container-xl {
    max-width: 1200px;
  }
  
  &.container-full {
    max-width: 100%;
  }
  
  @media (min-width: 1200px) {
    // Remove extra padding on large screens when using container
    .page-wrapper.desktop-layout & {
      padding: 0;
    }
  }
}

// Content Layouts
.content-single {
  width: 100%;
  // Add overlap effect like home page to move content into header area
  margin-top: -30px;
  position: relative;
  z-index: 2;
  // Ensure content has enough bottom padding to be fully scrollable
  padding-bottom: 40px;
  
  @media (min-width: 768px) {
    padding-bottom: 60px;
  }
}

.content-grid {
  display: grid;
  gap: 20px;
  
  @media (min-width: 768px) {
    grid-template-columns: 2fr 1fr;
    gap: 24px;
  }
  
  @media (min-width: 1200px) {
    gap: 28px;
  }
  
  .content-main {
    width: 100%;
  }
  
  .content-sidebar {
    width: 100%;
  }
}

// Card styles that can be used within the template
:host ::ng-deep {
  .section-card {
    background: white;
    border-radius: 20px;
    padding: 24px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    @media (min-width: 768px) {
      padding: 28px;
      margin-bottom: 24px;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }
    }
    
    @media (min-width: 1200px) {
      padding: 32px;
      margin-bottom: 28px;
    }
  }
  
  // Hero card that overlaps
  .hero-card {
    background: white;
    border-radius: 20px;
    padding: 24px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    margin-top: -30px;
    position: relative;
    z-index: 2;
    
    @media (min-width: 768px) {
      padding: 28px;
    }
    
    @media (min-width: 1200px) {
      padding: 32px;
    }
  }
}