<ion-item lines="inset" class="w-full ion-selectable-item">
  <ion-icon slot="start" name="star-outline"></ion-icon>
  <div class="div-ion-item">
    <ion-label class="label-floating" *ngIf="required_field"
      >* Favourite Store</ion-label
    >
    <ion-label class="label-floating" *ngIf="!required_field"
      >Favourite Store</ion-label
    >
    <ionic-selectable
      [(ngModel)]="selectedStores"
      [items]="allStores"
      closeButtonText="{{ modalCloseText }}"
      closeButtonSlot="{{ modalCloseButtonSlot }}"
      itemTextField="partnerName"
      itemValueField="partnerId"
      [canSearch]="true"
      (onChange)="setSelectedStore()"
      (onSelect)="setSelectedStore()"
      labelPlacement="floating"
      class="w-full"
    >
    </ionic-selectable>
  </div>
</ion-item>
