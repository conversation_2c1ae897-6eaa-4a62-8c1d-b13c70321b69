import {
  Component,
  Inject,
  Input,
  OnInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import {
  Form<PERSON>uilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  Address,
  CodeItem,
  CountryItem,
  LssConfig,
  SystemService,
  ValidationService,
} from 'lp-client-api';
import { BehaviorSubject, forkJoin, Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';

@Component({
  selector: 'lp-pos-address',
  templateUrl: './address.component.html',
  styleUrls: ['./address.component.scss'],
})
export class AddressComponent implements OnInit, OnChanges {
  @Input()
  type: any;
  _data!: Address;
  @Input()
  required_field = false;
  @Input()
  mainForm!: FormGroup;
  @Input()
  mainAddress!: Address;
  countries: Observable<CountryItem[]>;
  provinces!: Observable<CodeItem[]>;
  districts!: Observable<CodeItem[]>;
  cities!: CodeItem[];
  places!: Observable<CodeItem[]>;
  @Input()
  modalCloseText = 'Close';
  @Input()
  modalCloseButtonSlot: 'start' | 'end' | 'primary' | 'secondary' = 'end';
  currentSelection = {
    country: '',
    province: '',
    district: '',
    city: '',
    places: '',
    line1: '',
    line2: '',
    postalCode: '',
  };
  selectedCity?: CodeItem;
  useDistrict = false;
  countryList: unknown;
  _myForm: FormGroup;
  loading = false;

  constructor(
    private _formBuilder: FormBuilder,
    private _systemService: SystemService,
    public _formValidations: ValidationService,
    public lssConfig: LssConfig
  ) {
    this._myForm = this._formBuilder.group({
      country: ['', Validators.required],
      province: ['', Validators.required],
      city: ['', Validators.required],
      place: ['', Validators.required],
      line1: ['', Validators.required],
      line2: [''],
      postalCode: [''],
    });
    this.countries = this._systemService.listCountries('').pipe(
      tap((countryData) => {
        this.countryList = countryData;
      })
    );
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['mainAddress']) {
      if (this.mainAddress && this.mainAddress.country) {
        this.patchFormSeq();
      }
    }
  }

  ngOnInit(): void {
    if (this.mainAddress) {
      this._myForm.controls['country'].patchValue(this.mainAddress.country);
    }
    if (this.mainForm != null) {
      this.mainForm.addControl(`address_${this.type}`, this._myForm);
    }
  }
  patchFormSeq() {
    if (this.mainAddress == undefined) return;
    if (this.mainAddress.country) {
      this._myForm.controls['country'].patchValue(this.mainAddress.country);
      this.countryChange(this.mainAddress.country);
      this.provinceChange(this.mainAddress.province);
      if (this.useDistrict) {
        this.districtChange(
          this.mainAddress.district,
          this.mainAddress.province
        );
      }
      this.cityChange(
        this.mainAddress.city,
        this.mainAddress.province,
        this.mainAddress.district
      );
      this._myForm.controls['province'].patchValue(this.mainAddress.province);
      // this._myForm.controls['district'].patchValue(this.mainAddress.district);
      if (
        this.selectedCity &&
        this.selectedCity.value === this.mainAddress.city
      ) {
        this._myForm.controls['city'].patchValue(this.selectedCity);
      }
      this._myForm.controls['place'].patchValue(this.mainAddress.suburb);
      this._myForm.controls['line1'].patchValue(this.mainAddress.line1);
      this._myForm.controls['line2'].patchValue(this.mainAddress.line2);
      this._myForm.controls['postalCode'].patchValue(
        this.mainAddress.postalCode
      );
    }
  }
  get form(): any {
    return this._myForm.controls;
  }

  async countryChange(country?: string) {
    if (country) {
      if (this.currentSelection.country === country) {
        return;
      }

      this.currentSelection.country = country;
      if (country != null && country !== '') {
        this.provinces = this._systemService.listProvice('', country);
      } else {
        this.provinces = of();
      }
      this.form.province.patchValue(null);
    }
  }
  provinceChange(province?: string) {
    if (province) {
      if (this.currentSelection.province === province) {
        return;
      }
      this.currentSelection.province = province;
      if (this.useDistrict) {
        if (province != null && province !== '') {
          this.districts = this._systemService.listDistrict('', province);
        } else {
          this.districts = of();
        }
        if (!this.loading) {
          this.form.district.patchValue(null);
        }
      } else {
        this.populateCity('USECITY', province);
      }
    }
  }

  districtChange(district?: string, province?: string) {
    if (district) {
      this.populateCity(district, province);
    }
  }
  populateCity(district?: string, province?: string) {
    province =
      province || this._myForm.value.province || this.currentSelection.province;
    if (
      province === '' ||
      this.currentSelection.district === `${district}|${province}`
    ) {
      return;
    }
    this.currentSelection.district = `${district}|${province}`;
    if (district != null && district !== '') {
      this._systemService
        .listCity('', province, district)
        .subscribe((data: CodeItem[]) => {
          this.cities = data;
          if (this.mainAddress.city && !this.selectedCity) {
            const adrFilter = this.cities.filter((cityItem) => {
              return cityItem.value === this.mainAddress.city;
            });
            if (adrFilter && adrFilter.length > 0) {
              this.selectedCity = adrFilter[0];
              this._myForm.controls['city'].patchValue(this.selectedCity);
            }
          }
        });
    } else {
      this.cities = [];
    }
    if (!this.loading) {
      this.form.city.patchValue(null);
    }
  }
  cityChange(city?: string, province?: string, district?: string) {
    if (city) {
      province =
        province ||
        this._myForm.value.province ||
        this.currentSelection.province;
      if (this.useDistrict) {
        district =
          district ||
          this._myForm.value.district ||
          this.currentSelection.district.split('|')[0];
      } else {
        district = 'USECITY';
      }
      const currentValue = `${city}|${province}|${district}`;
      if (
        province === '' ||
        district === '' ||
        this.currentSelection.places === currentValue
      ) {
        return;
      }
      this.currentSelection.places = currentValue;
      if (city != null && city !== '') {
        this.places = this._systemService.listPlaces(
          '',
          province,
          district,
          city
        );
      } else {
        this.places = of();
      }
      if (!this.loading) {
        this.form.place.patchValue(null);
      }
    }
  }

  getEventValue(event: any): any {
    return event.detail.value;
  }
  getEventValueFilter(event: any): any {
    this.selectedCity = event.item;
    if (event.item) {
      return event.item.value;
    }
    return null;
  }
}
