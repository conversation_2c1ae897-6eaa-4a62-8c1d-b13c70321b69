<form [formGroup]="_myForm">
  <ion-item>
    <ion-icon slot="start" name="home-outline"></ion-icon>

    <ion-select
      [label]="required_field ? '* Country' : 'Country'"
      labelPlacement="floating"
      placeholder="Select One"
      (ionChange)="countryChange(getEventValue($event))"
      formControlName="country"
    >
      <ion-select-option
        *ngFor="let country of countries | async"
        [value]="lssConfig.useISO ? country.isoCode : country.code"
      >
        {{ country.country }}
      </ion-select-option>
    </ion-select>
  </ion-item>
  <ion-item *ngIf="!form.country.valid && form.country.touched">
    <div
      *ngFor="
        let error of _formValidations.doErrors(form.country.errors, 'country')
      "
      class="validator-error"
    >
      {{ error }}
    </div>
  </ion-item>
  <ion-item *ngIf="form.country.valid">
    <ion-icon slot="start" name="home-outline"></ion-icon>

    <ion-select
      [label]="required_field ? '* Province' : 'Province'"
      labelPlacement="floating"
      placeholder="Select One"
      (ionChange)="provinceChange(getEventValue($event))"
      formControlName="province"
    >
      <ion-select-option
        *ngFor="let province of provinces | async"
        [value]="province.value"
        >{{ province.label }}</ion-select-option
      >
    </ion-select>
  </ion-item>
  <ion-item
    *ngIf="!form.province.valid && form.country.valid && form.province.touched"
  >
    <div
      *ngFor="
        let error of _formValidations.doErrors(form.province.errors, 'province')
      "
      class="validator-error"
    >
      {{ error }}
    </div>
  </ion-item>
  <!-- <ion-item *ngIf="form.province.valid && useDistrict">
    <ion-icon slot="start" name="home-outline"></ion-icon>

    <ion-select
      label="District"
      labelPlacement="floating"
      placeholder="Select One"
      interface="popover"
      (ionChange)="districtChange(getEventValue($event))"
      formControlName="district"
    >
      <ion-select-option
        *ngFor="let district of districts | async"
        [value]="district.value"
        >{{ district.label }}</ion-select-option
      >
    </ion-select>
  </ion-item>
  <ion-item
    *ngIf="
      !form.district.valid &&
      form.province.valid &&
      useDistrict &&
      form.district.touched
    "
  >
    <div
      *ngFor="
        let error of _formValidations.doErrors(form.district.errors, 'district')
      "
      class="validator-error"
    >
      {{ error }}
    </div>
  </ion-item> -->
  <ion-item
    *ngIf="form.province.valid"
    lines="inset"
    class="w-full ion-selectable-item"
  >
    <ion-icon slot="start" name="home-outline"></ion-icon>
    <div class="div-ion-item">
      <ion-label class="label-floating">{{
        required_field ? "* City" : "City"
      }}</ion-label>
      <ionic-selectable
        [items]="cities"
        closeButtonText="{{ modalCloseText }}"
        closeButtonSlot="{{ modalCloseButtonSlot }}"
        itemTextField="label"
        itemValueField="value"
        [canSearch]="true"
        (onChange)="cityChange(getEventValueFilter($event))"
        (onSelect)="cityChange(getEventValueFilter($event))"
        labelPlacement="floating"
        class="w-full"
        formControlName="city"
      >
      </ionic-selectable>
    </div>
  </ion-item>
  <ion-item
    *ngIf="!form.city.valid && form.province.valid && form.city.touched"
  >
    <div
      *ngFor="let error of _formValidations.doErrors(form.city.errors, 'city')"
      class="validator-error"
    >
      {{ error }}
    </div>
  </ion-item>
  <ion-item *ngIf="form.city.valid">
    <ion-icon slot="start" name="home-outline"></ion-icon>

    <ion-select
      [label]="required_field ? '* Suburb' : 'Suburb'"
      labelPlacement="floating"
      placeholder="Select One"
      formControlName="place"
    >
      <ion-select-option
        *ngFor="let place of places | async"
        [value]="place.value"
        >{{ place.label }}</ion-select-option
      >
    </ion-select>
  </ion-item>
  <ion-item *ngIf="!form.place.valid && form.city.valid && form.place.touched">
    <div
      *ngFor="
        let error of _formValidations.doErrors(form.place.errors, 'place')
      "
      class="validator-error"
    >
      {{ error }}
    </div>
  </ion-item>
  <ion-item *ngIf="form.city.valid">
    <ion-icon slot="start" name="home-outline"></ion-icon>

    <ion-input
      formControlName="line1"
      [label]="required_field ? '* Line1' : 'Line1'"
      labelPlacement="floating"
    ></ion-input>
  </ion-item>
  <ion-item *ngIf="!form.line1.valid && form.city.valid && form.line1.touched">
    <div
      *ngFor="
        let error of _formValidations.doErrors(form.line1.errors, 'line1')
      "
      class="validator-error"
    >
      {{ error }}
    </div>
  </ion-item>
  <ion-item *ngIf="form.city.valid">
    <ion-icon name="home-outline" slot="start"></ion-icon>

    <ion-input
      formControlName="line2"
      label="Line 2"
      labelPlacement="floating"
    ></ion-input>
  </ion-item>
  <ion-item *ngIf="!form.line2.valid && form.city.valid && form.line2.touched">
    <div
      *ngFor="
        let error of _formValidations.doErrors(form.line2.errors, 'line2')
      "
      class="validator-error"
    >
      {{ error }}
    </div>
  </ion-item>
  <ion-item *ngIf="form.city.valid">
    <ion-icon slot="start" name="home-outline"></ion-icon>

    <ion-input
      formControlName="postalCode"
      label="Postal/Zip Code"
      labelPlacement="floating"
    ></ion-input>
  </ion-item>
  <ion-item *ngIf="!form.postalCode.valid && form.postalCode.touched">
    <div
      *ngFor="
        let error of _formValidations.doErrors(
          form.postalCode.errors,
          'postalCode'
        )
      "
      class="validator-error"
    >
      {{ error }}
    </div>
  </ion-item>
</form>
