import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { AddressComponent } from './address/address.component';
import { LoadingComponent } from './loading/loading.component';
import { PinComponent } from './pin/pin.component';
import { HeadLogoComponent } from './head-logo/head-logo.component';
import { StoresComponent } from './stores/stores.component';
import { CustomizerComponent } from './customizer/customizer.component';
import { ColorPickerModule } from 'ngx-color-picker';
import { ColoursComponent } from './colours/colours.component';
import { LayoutComponent } from './layout/layout.component';
import { InfoComponent } from './info/info.component';
import { ContactComponent } from './contact/contact.component';
import { IonicSelectableModule } from 'third-party-fix';
import { SocialsPlainComponent } from './socials/plain/socials-plain.component';
import { SocialsCustomizerComponent } from './socials/customizer/socials-customizer.component';
import { PagesCustomizerComponent } from './pages/customizer/pages-customizer.component';
import { PagesLandingCustomizerComponent } from './pages/landing/customizer/pages-landing-customizer.component';
import { PagesLandingTheme1Component } from './pages/landing/themes/theme1/pages-landing-theme1.component';
import { PagesLoginCustomizerComponent } from './pages/login/customizer/pages-login-customizer.component';
import { PagesLoginTheme1Component } from './pages/login/themes/theme1/pages-login-theme1.component';
import { AccountPoolComponent } from './account-pool/account-pool.component';
import { AccountPoolInviteComponent } from './account-pool-invite/account-pool-invite.component';
import { PointsTransferComponent } from './points-transfer/points-transfer.component';
import { PointsTransferHistoryComponent } from './points-transfer-history/points-transfer-history.component';
import { PageWrapperComponent } from './page-wrapper/page-wrapper.component';
import { UiCardComponent } from './ui-card/ui-card.component';
import { UiButtonDirective } from './ui-button/ui-button.directive';
import { NotificationListComponent } from './notifications/notification-list/notification-list.component';

@NgModule({
  declarations: [
    AddressComponent,
    LoadingComponent,
    PinComponent,
    HeadLogoComponent,
    StoresComponent,
    CustomizerComponent,
    ColoursComponent,
    LayoutComponent,
    InfoComponent,
    ContactComponent,
    SocialsPlainComponent,
    SocialsCustomizerComponent,
    PagesCustomizerComponent,
    PagesLandingCustomizerComponent,
    PagesLandingTheme1Component,
    PagesLoginCustomizerComponent,
    PagesLoginTheme1Component,
  ],
  imports: [
    CommonModule,
    ColorPickerModule,
    IonicModule,
    FormsModule,
    ReactiveFormsModule,
    // IonicSelectableModule,
    RouterModule,
    PointsTransferComponent,
    PointsTransferHistoryComponent,
    AccountPoolComponent,
    AccountPoolInviteComponent,
    // Import standalone design system components
    PageWrapperComponent,
    UiCardComponent,
    UiButtonDirective,
    NotificationListComponent,
  ],
  exports: [
    AddressComponent,
    LoadingComponent,
    PinComponent,
    HeadLogoComponent,
    StoresComponent,
    CustomizerComponent,
    ColoursComponent,
    LayoutComponent,
    InfoComponent,
    ContactComponent,
    PagesLandingTheme1Component,
    PagesLoginTheme1Component,
    PointsTransferComponent,
    PointsTransferHistoryComponent,
    AccountPoolComponent,
    AccountPoolInviteComponent,
    // Export design system components
    PageWrapperComponent,
    UiCardComponent,
    UiButtonDirective,
    NotificationListComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ComponentsModule {}
