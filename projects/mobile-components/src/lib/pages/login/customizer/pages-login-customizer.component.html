<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Page Class"
    [(ngModel)]="lssConfig.pages.login.class"
  />
</ion-item>

<ion-item>
  <input type="file" (change)="onFileChange($event)" />
</ion-item>

<ion-item class="w-full primary">
  <img class="icon w-full" [src]="lssConfig.pages.login.icon.src" />
</ion-item>

<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Icon Class"
    [(ngModel)]="lssConfig.pages.login.icon.class"
  />
</ion-item>
<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Logged in Icon"
    [(ngModel)]="lssConfig.pages.login.loggedinIcon"
  />
</ion-item>
<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Title"
    [(ngModel)]="lssConfig.pages.login.title.text"
  />
  <ion-input
    labelPlacement="floating"
    label="Title"
    [(ngModel)]="lssConfig.pages.login.title.class"
  />
</ion-item>
<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Subtitle"
    [(ngModel)]="lssConfig.pages.login.subtitle.text"
  />
  <ion-input
    labelPlacement="floating"
    label="Subtitle Class"
    [(ngModel)]="lssConfig.pages.login.subtitle.class"
  />
</ion-item>

<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Auth Buttons"
    [(ngModel)]="lssConfig.pages.login.auth_buttons.class"
  />
</ion-item>

<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Login"
    [(ngModel)]="lssConfig.pages.login.auth_buttons.login.text"
  />
  <ion-input
    labelPlacement="floating"
    label="Login Class"
    [(ngModel)]="lssConfig.pages.login.auth_buttons.login.class"
  />
</ion-item>

<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Signup"
    [(ngModel)]="lssConfig.pages.login.auth_buttons.signup.text"
  />
  <ion-input
    labelPlacement="floating"
    label="Signup Class"
    [(ngModel)]="lssConfig.pages.login.auth_buttons.signup.class"
  />
</ion-item>

<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Forgot Password"
    [(ngModel)]="lssConfig.pages.login.auth_buttons.password.text"
  />
  <ion-input
    labelPlacement="floating"
    label="Forgot Password Class"
    [(ngModel)]="lssConfig.pages.login.auth_buttons.password.class"
  />
</ion-item>

<ion-item>
  <ion-select
    class="routes"
    [(ngModel)]="lssConfig.pages.login.theme"
    label="Theme"
    label-placement="floating"
  >
    <ion-select-option
      *ngFor="let option of lssConfig.pages.login.themes"
      :value="option"
      >{{ option }}</ion-select-option
    >
  </ion-select>
</ion-item>
