import { Component } from '@angular/core';
import { LssConfig } from 'lp-client-api';

@Component({
  selector: 'pages-login-customizer',
  templateUrl: './pages-login-customizer.component.html',
  styleUrls: [
    './pages-login-customizer.component.css',
    '../../../../styles.scss',
  ],
})
export class PagesLoginCustomizerComponent {
  constructor(public lssConfig: LssConfig) {}

  onFileChange(event: any) {
    const reader = new FileReader();
    if (event.target.files && event.target.files.length) {
      const [file] = event.target.files;
      console.log('file', file);
      reader.readAsDataURL(file);

      reader.onload = () => {
        console.log('reader.result', reader.result);

        this.lssConfig.pages.login.icon.src = reader.result;
      };
    }
  }
}
