<div class="login-container" [ngClass]="lssConfig.pages.login.class">
  <div class="logo-section">
    <div class="logo-wrapper animated slideDown">
      <img
        [src]="lssConfig.pages.login.icon.src"
        alt="Logo"
      />
    </div>
  </div>

  <div class="content">
    <div class="content-title animated fadeIn">
      <h1 class="title-text" [ngClass]="lssConfig.pages.login.title.class">
        {{ lssConfig.pages.login.title.text }}
      </h1>
      <p class="subtitle-text" [ngClass]="lssConfig.pages.login.subtitle.class">
        {{ lssConfig.pages.login.subtitle.text }}
      </p>
    </div>

    <div class="auth-buttons animated slideUp" [ngClass]="lssConfig.pages.login.auth_buttons.class">
      <button
        class="login-button"
        [ngClass]="lssConfig.pages.login.auth_buttons.login.class"
        (click)="login()"
      >
        {{ lssConfig.pages.login.auth_buttons.login.text }}
      </button>
      <button
        class="signup-button"
        [ngClass]="lssConfig.pages.login.auth_buttons.signup.class"
        routerLink="/public/validate"
      >
        {{ lssConfig.pages.login.auth_buttons.signup.text }}
      </button>
      <button
        class="password-button"
        [ngClass]="lssConfig.pages.login.auth_buttons.password.class"
        (click)="lostPassword()"
      >
        {{ lssConfig.pages.login.auth_buttons.password.text }}
      </button>
    </div>
  </div>
  <div class="social-buttons animated fadeIn" [ngClass]="lssConfig.pages.login.social_buttons.class">
    <button class="social-button" [ngClass]="lssConfig.pages.login.social_buttons.facebook.class">
      <a [href]="lssConfig.socials.facebook" target="_blank">
        <ion-icon
          [name]="lssConfig.pages.login.social_buttons.facebook.icon || 'logo-facebook'"
          [size]="lssConfig.pages.login.social_buttons.facebook.icon_size"
        ></ion-icon>
      </a>
    </button>
    <button class="social-button" [ngClass]="lssConfig.pages.login.social_buttons.twitter.class">
      <a [href]="lssConfig.socials.twitter" target="_blank">
        <ion-icon
          [name]="lssConfig.pages.login.social_buttons.twitter.icon || 'logo-twitter'"
          [size]="lssConfig.pages.login.social_buttons.twitter.icon_size"
        ></ion-icon>
      </a>
    </button>
    <button class="social-button" [ngClass]="lssConfig.pages.login.social_buttons.linkedin.class">
      <a [href]="lssConfig.socials.linkedin" target="_blank">
        <ion-icon
          [name]="lssConfig.pages.login.social_buttons.linkedin.icon || 'logo-linkedin'"
          [size]="lssConfig.pages.login.social_buttons.linkedin.icon_size"
        ></ion-icon>
      </a>
    </button>
    <button class="social-button" [ngClass]="lssConfig.pages.login.social_buttons.youtube?.class" *ngIf="lssConfig.pages.login.social_buttons.youtube">
      <a [href]="lssConfig.socials.youtube" target="_blank">
        <ion-icon
          [name]="lssConfig.pages.login.social_buttons.youtube.icon || 'logo-youtube'"
          [size]="lssConfig.pages.login.social_buttons.youtube.icon_size"
        ></ion-icon>
      </a>
    </button>
    <button class="social-button" [ngClass]="lssConfig.pages.login.social_buttons.instagram.class">
      <a [href]="lssConfig.socials.instagram" target="_blank">
        <ion-icon
          [name]="lssConfig.pages.login.social_buttons.instagram.icon || 'logo-instagram'"
          [size]="lssConfig.pages.login.social_buttons.instagram.icon_size"
        ></ion-icon>
      </a>
    </button>
    <button class="social-button" [ngClass]="lssConfig.pages.login.social_buttons.pinterest.class">
      <a [href]="lssConfig.socials.pinterest" target="_blank">
        <ion-icon
          [name]="lssConfig.pages.login.social_buttons.pinterest.icon || 'logo-pinterest'"
          [size]="lssConfig.pages.login.social_buttons.pinterest.icon_size"
        ></ion-icon>
      </a>
    </button>
  </div>
</div>
