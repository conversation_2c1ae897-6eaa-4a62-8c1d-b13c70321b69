import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { LssConfig } from 'lp-client-api';
import { Preferences } from '@capacitor/preferences';

@Component({
  selector: 'pages-login-theme1',
  templateUrl: './pages-login-theme1.component.html',
  styleUrls: [
    './pages-login-theme1.component.css',
    '../../../../../styles.scss',
  ],
})
export class PagesLoginTheme1Component {
  @Input() public profile: any;
  @Input() public kc: any;

  constructor(protected readonly router: Router, public lssConfig: LssConfig) {}

  login() {
    console.log('login');
    console.log('login', this.kc);

    if (!this.kc.authSuccess) {
      this.kc.login().then((data: any) => {
        console.log(data);
      });
    }
    Preferences.remove({ key: 'login' }).then(() => {
      console.log('Token Removed');
    });
  }

  lostPassword() {
    if (!this.kc.authSuccess) {
      this.kc.lostPassword().then((data: any) => {
        console.log(data);
      });
    }
  }
}
