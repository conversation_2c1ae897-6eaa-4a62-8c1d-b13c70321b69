/* Modern Login Theme Styles */

:host {
  display: block;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);
}

.login-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}


/* Logo Section */
.logo-section {
  text-align: center;
  padding-top: 10px;
  z-index: 1;
}

.logo-wrapper {
  width: 320px;
  height: 320px;
  margin: 0 auto;
  background: transparent;
  border-radius: 30px;
  padding: 10px;
  animation: slideDown 0.6s ease-out;
}

.logo-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Content Section */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 0;
  margin-top: -40px;
  z-index: 1;
}

.content-title {
  text-align: center;
  margin-bottom: 20px;
  animation: fadeIn 0.8s ease-out 0.3s both;
}

.title-text {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 10px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.subtitle-text {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}

/* Auth Buttons */
.auth-buttons {
  max-width: 320px;
  margin: 0 auto;
  width: 100%;
  animation: slideUp 0.8s ease-out 0.5s both;
}

.auth-buttons button {
  width: 100%;
  padding: 16px;
  margin-bottom: 12px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-button {
  background: white;
  color: #667eea;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.login-button:active {
  transform: translateY(0);
}

.signup-button {
  background: transparent;
  color: white;
  border: none;
}

.signup-button:hover {
  background: white;
  color: #667eea;
}

.password-button {
  background: transparent;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  text-decoration: underline;
}

.password-button:hover {
  color: white;
}

/* Social Buttons */
.social-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding-bottom: 30px;
  z-index: 1;
  animation: fadeIn 1s ease-out 0.7s both;
}

.social-buttons button {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-buttons button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-3px);
}

.social-buttons a {
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.social-buttons ion-icon {
  font-size: 22px;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 380px) {
  .logo-wrapper {
    width: 260px;
    height: 260px;
  }
  
  .title-text {
    font-size: 28px;
  }
  
  .subtitle-text {
    font-size: 16px;
  }
  
  .auth-buttons button {
    padding: 14px;
    font-size: 15px;
  }
}

/* Modern Alternative Theme */
.modern-theme {
  background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
}

.modern-theme .logo-wrapper {
  background: transparent;
}

.modern-theme .login-button {
  background: #1a1a1a;
  color: white;
}

.modern-theme .login-button:hover {
  background: #000;
}

.modern-theme .signup-button:hover {
  background: white;
  color: #FF6B35;
}