<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Icon"
    [(ngModel)]="lssConfig.pages.landing.icon"
  />
</ion-item>
<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Logged in Icon"
    [(ngModel)]="lssConfig.pages.landing.loggedinIcon"
  />
</ion-item>
<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Title"
    [(ngModel)]="lssConfig.pages.landing.title.text"
  />
  <ion-input
    labelPlacement="floating"
    label="Title"
    [(ngModel)]="lssConfig.pages.landing.title.class"
  />
</ion-item>
<ion-item>
  <ion-input
    labelPlacement="floating"
    label="Subtitle"
    [(ngModel)]="lssConfig.pages.landing.subtitle.text"
  />
  <ion-input
    labelPlacement="floating"
    label="Subtitle Class"
    [(ngModel)]="lssConfig.pages.landing.subtitle.class"
  />
</ion-item>

<ion-item>
  <ion-select
    class="routes"
    [(ngModel)]="lssConfig.pages.landing.theme"
    label="Theme"
    label-placement="floating"
  >
    <ion-select-option
      *ngFor="let option of lssConfig.pages.landing.themes"
      :value="option"
      >{{ option }}</ion-select-option
    >
  </ion-select>
</ion-item>
