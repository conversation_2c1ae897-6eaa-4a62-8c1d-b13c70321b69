// Improved Home Screen Styles
// Based on UX/UI Specification v1.0

// Import the design system variables
:root {
  // Brand Colors
  --ion-color-base: #0072bc;
  --ion-color-base-shade: #00569a;
  --ion-color-base-tint: #1a82c6;
  
  --ion-color-primary: #FF6B35;
  --ion-color-primary-shade: #E55A2B;
  --ion-color-primary-tint: #FF7D4D;
  
  --ion-color-action: #FFA726;
  --ion-color-action-shade: #F57C00;
  --ion-color-action-contrast: #212121;
  
  // Typography
  --h1-font-size: 28px;
  --h2-font-size: 24px;
  --h3-font-size: 20px;
  --body-large: 18px;
  --body-regular: 16px;
  --body-small: 14px;
  --label-size: 12px;
  
  // Spacing
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  
  // Components
  --card-radius: 12px;
  --action-size: 80px;
  --transition-base: 300ms ease-out;
}

// Main container
.app-background {
  --background: var(--ion-color-base);
  
  // Add subtle gradient for depth
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: linear-gradient(180deg, var(--ion-color-base) 0%, var(--ion-color-base-shade) 100%);
    z-index: -1;
  }
}

// Header improvements
lib-head-logo {
  .welcome-section {
    padding: var(--space-md);
    
    .welcome-text {
      font-size: var(--body-small);
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: var(--space-xs);
    }
    
    .user-name {
      font-size: var(--h3-font-size);
      font-weight: 600;
      color: white;
    }
  }
}

// Balance Cards - Enhanced
.balance-card {
  &.primary {
    background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);
    margin: var(--space-sm) var(--space-md);
    border-radius: var(--card-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: transform var(--transition-base), box-shadow var(--transition-base);
    
    &:active {
      transform: translateY(1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
    
    ion-card-content {
      padding: var(--space-md) var(--space-lg);
    }
    
    ion-card-title {
      &:first-child {
        font-size: var(--body-small);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: rgba(255, 255, 255, 0.9);
      }
      
      &:last-child {
        font-size: var(--h2-font-size);
        font-weight: 600;
        color: white;
      }
    }
  }
}

// Action Grid - Enhanced
.content-grid {
  padding: var(--space-xl) var(--space-md) var(--space-md);
  
  ion-row {
    margin-bottom: var(--space-lg);
    
    ion-col {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
}

// Action Cards - Circular buttons
.action-card {
  width: var(--action-size);
  height: var(--action-size);
  border-radius: 50%;
  background: linear-gradient(135deg, var(--ion-color-action) 0%, var(--ion-color-action-shade) 100%);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-base);
  cursor: pointer;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  }
  
  &:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
  }
  
  // Icon styling
  ion-icon {
    font-size: 32px;
    color: var(--ion-color-action-contrast);
  }
}

// Action labels
.action-name {
  font-size: var(--body-small);
  color: white;
  margin-top: var(--space-sm);
  text-align: center;
  font-weight: 500;
}

// Responsive adjustments
@media (max-width: 320px) {
  :root {
    --action-size: 70px;
    --h2-font-size: 20px;
  }
  
  .content-grid {
    padding: var(--space-lg) var(--space-sm);
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .balance-card.primary {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
  }
  
  .action-card {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  }
}

// Animation classes
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Utility classes
.text-white { color: white !important; }
.text-white-80 { color: rgba(255, 255, 255, 0.8) !important; }
.mb-xs { margin-bottom: var(--space-xs) !important; }
.mb-sm { margin-bottom: var(--space-sm) !important; }
.mb-md { margin-bottom: var(--space-md) !important; }