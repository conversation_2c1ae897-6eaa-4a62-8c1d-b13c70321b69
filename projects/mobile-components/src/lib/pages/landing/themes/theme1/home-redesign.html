<!-- Enhanced Home Screen Design -->
<ion-content class="app-background">
  
  <!-- OPTION 1: <PERSON>han<PERSON> <PERSON><PERSON> with Gradient -->
  <div class="head-logo-enhanced">
    <div class="logo-section">
      <img [src]="lssConfig.pages.landing.loggedinIcon" alt="Logo" />
    </div>
    <div class="welcome-section">
      <p class="welcome-label">Welcome back</p>
      <h1 class="user-name">{{ profile?.givenNames }} {{ profile?.surname }}</h1>
    </div>
  </div>

  <!-- Enhanced Balance Cards -->
  <div class="balance-cards-container">
    <div class="balance-card-enhanced rand-value">
      <ion-icon name="cash-outline" class="card-icon"></ion-icon>
      <p class="label">Rand Value</p>
      <p class="value">R {{ profile?.availRands?.toFixed(2) || '0.00' }}</p>
    </div>
    
    <div class="balance-card-enhanced points">
      <ion-icon name="star-outline" class="card-icon"></ion-icon>
      <p class="label">Points</p>
      <p class="value">{{ profile?.currentBalance || 0 }}</p>
    </div>
  </div>

  <!-- OPTION 1: Colorful Grid Buttons -->
  <div class="action-buttons-grid">
    <div class="action-grid">
      <div class="action-item">
        <a [routerLink]="['/secure/profile']" class="action-button-enhanced profile">
          <div class="icon-wrapper">
            <ion-icon name="person-outline"></ion-icon>
          </div>
          <p class="button-label">Profile</p>
        </a>
      </div>
      
      <div class="action-item">
        <a [routerLink]="['/secure/card']" class="action-button-enhanced card">
          <div class="icon-wrapper">
            <ion-icon name="card-outline"></ion-icon>
          </div>
          <p class="button-label">Card</p>
        </a>
      </div>
      
      <div class="action-item">
        <a [routerLink]="['/secure/transactions']" class="action-button-enhanced transactions">
          <div class="icon-wrapper">
            <ion-icon name="receipt-outline"></ion-icon>
          </div>
          <p class="button-label">Transactions</p>
        </a>
      </div>
      
      <div class="action-item">
        <a [routerLink]="['/secure/stores']" class="action-button-enhanced stores">
          <div class="icon-wrapper">
            <ion-icon name="storefront-outline"></ion-icon>
          </div>
          <p class="button-label">Stores</p>
        </a>
      </div>
    </div>
  </div>

  <!-- OPTION 2: Floating Card Style Buttons (Alternative Design) -->
  <!-- Uncomment this section and comment out the grid above to use this design -->
  <!--
  <div class="action-buttons-floating">
    <div class="floating-grid">
      <div class="floating-action profile" [routerLink]="['/secure/profile']">
        <div class="action-content">
          <div class="icon-bubble profile">
            <ion-icon name="person-outline" class="profile"></ion-icon>
          </div>
          <div class="action-text">
            <h3 class="action-title">Profile</h3>
            <p class="action-subtitle">Manage account</p>
          </div>
          <ion-icon name="chevron-forward-outline" class="arrow-icon"></ion-icon>
        </div>
      </div>
      
      <div class="floating-action card" [routerLink]="['/secure/card']">
        <div class="action-content">
          <div class="icon-bubble card">
            <ion-icon name="card-outline" class="card"></ion-icon>
          </div>
          <div class="action-text">
            <h3 class="action-title">Card</h3>
            <p class="action-subtitle">Virtual card</p>
          </div>
          <ion-icon name="chevron-forward-outline" class="arrow-icon"></ion-icon>
        </div>
      </div>
      
      <div class="floating-action transactions" [routerLink]="['/secure/transactions']">
        <div class="action-content">
          <div class="icon-bubble transactions">
            <ion-icon name="receipt-outline" class="transactions"></ion-icon>
          </div>
          <div class="action-text">
            <h3 class="action-title">Transactions</h3>
            <p class="action-subtitle">View history</p>
          </div>
          <ion-icon name="chevron-forward-outline" class="arrow-icon"></ion-icon>
        </div>
      </div>
      
      <div class="floating-action stores" [routerLink]="['/secure/stores']">
        <div class="action-content">
          <div class="icon-bubble stores">
            <ion-icon name="storefront-outline" class="stores"></ion-icon>
          </div>
          <div class="action-text">
            <h3 class="action-title">Stores</h3>
            <p class="action-subtitle">Find locations</p>
          </div>
          <ion-icon name="chevron-forward-outline" class="arrow-icon"></ion-icon>
        </div>
      </div>
    </div>
  </div>
  -->

</ion-content>

<!-- Alternative Header Design with Centered Layout -->
<!-- 
<div class="header-centered">
  <div class="header-background">
    <svg viewBox="0 0 375 200" preserveAspectRatio="none">
      <path d="M0,0 L375,0 L375,160 Q187.5,200 0,160 Z" fill="url(#headerGradient)"/>
      <defs>
        <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#0072bc;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#00487a;stop-opacity:1" />
        </linearGradient>
      </defs>
    </svg>
  </div>
  
  <div class="header-content">
    <img [src]="lssConfig.pages.landing.loggedinIcon" alt="Logo" class="logo-centered" />
    <h2 class="greeting">Good {{ getTimeOfDay() }},</h2>
    <h1 class="user-name-large">{{ profile?.givenNames }}</h1>
  </div>
</div>
-->