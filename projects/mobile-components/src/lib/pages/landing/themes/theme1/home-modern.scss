// Modern Home Screen Design with Visual Impact
// Bold changes while maintaining structure

// Modern Color Palette
:root {
  // Base colors remain the same
  --ion-color-base: #0072bc;
  --ion-color-primary: #FF6B35;
  
  // Modern gradients
  --gradient-blue: linear-gradient(135deg, #0072bc 0%, #003d6b 100%);
  --gradient-orange: linear-gradient(135deg, #FF6B35 0%, #FF4500 100%);
  --gradient-purple: linear-gradient(135deg, #7C4DFF 0%, #536DFE 100%);
  --gradient-teal: linear-gradient(135deg, #00BCD4 0%, #00ACC1 100%);
  
  // Glassmorphism
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

// MODERN HEADER WITH WAVE
.modern-header {
  position: relative;
  height: 280px;
  background: var(--gradient-blue);
  overflow: hidden;
  
  // Animated wave background
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: white;
    border-radius: 50% 50% 0 0 / 100% 100% 0 0;
    transform: translateY(50px);
  }
  
  // Floating circles decoration
  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    
    &.circle-1 {
      width: 120px;
      height: 120px;
      top: -60px;
      right: -60px;
    }
    
    &.circle-2 {
      width: 80px;
      height: 80px;
      top: 80px;
      left: -40px;
    }
    
    &.circle-3 {
      width: 60px;
      height: 60px;
      bottom: 40px;
      right: 20px;
    }
  }
  
  // Content
  .header-content {
    position: relative;
    z-index: 1;
    padding: 40px 24px;
    text-align: center;
    
    .time-greeting {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 8px;
      
      ion-icon {
        font-size: 20px;
        vertical-align: middle;
        margin-right: 8px;
      }
    }
    
    .user-name {
      font-size: 32px;
      font-weight: 700;
      color: white;
      margin-bottom: 24px;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .logo-badge {
      width: 80px;
      height: 80px;
      margin: 0 auto;
      background: white;
      border-radius: 24px;
      padding: 16px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
      
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
}

// GLASSMORPHIC BALANCE CARDS
.glass-balance-cards {
  margin-top: -40px;
  padding: 0 24px;
  position: relative;
  z-index: 2;
  display: flex;
  gap: 16px;
  
  .glass-card {
    flex: 1;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 24px;
    box-shadow: var(--glass-shadow);
    position: relative;
    overflow: hidden;
    
    // Gradient border effect
    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 20px;
      padding: 2px;
      background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-base));
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      opacity: 0.5;
    }
    
    .card-content {
      position: relative;
      z-index: 1;
      
      .card-label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: var(--ion-color-base);
        font-weight: 600;
        margin-bottom: 12px;
        
        ion-icon {
          font-size: 18px;
        }
      }
      
      .card-value {
        font-size: 28px;
        font-weight: 700;
        color: #212121;
        
        &.orange {
          color: var(--ion-color-primary);
        }
      }
    }
  }
}

// MODERN ACTION BUTTONS
.modern-actions {
  padding: 32px 24px;
  
  .actions-title {
    font-size: 20px;
    font-weight: 600;
    color: #212121;
    margin-bottom: 24px;
  }
  
  .modern-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .modern-action-card {
    background: white;
    border-radius: 24px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
    }
    
    // Gradient backgrounds for each card
    &.profile {
      background: linear-gradient(135deg, #FFE0B2 0%, #FFCC80 100%);
      
      .action-icon { background: #FF6F00; }
    }
    
    &.card {
      background: linear-gradient(135deg, #E1F5FE 0%, #B3E5FC 100%);
      
      .action-icon { background: #0288D1; }
    }
    
    &.transactions {
      background: linear-gradient(135deg, #E8F5E9 0%, #C8E6C9 100%);
      
      .action-icon { background: #388E3C; }
    }
    
    &.stores {
      background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%);
      
      .action-icon { background: #7B1FA2; }
    }
    
    // Icon with modern style
    .action-icon {
      width: 48px;
      height: 48px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      
      ion-icon {
        font-size: 24px;
        color: white;
      }
    }
    
    // Text content
    .action-label {
      font-size: 16px;
      font-weight: 600;
      color: #212121;
      margin: 0;
    }
    
    // Decorative element
    .action-decoration {
      position: absolute;
      top: -20px;
      right: -20px;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// ALTERNATIVE: NEUMORPHIC DESIGN
.neumorphic-actions {
  padding: 32px 24px;
  background: #e0e5ec;
  
  .neu-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }
  
  .neu-button {
    background: #e0e5ec;
    border-radius: 20px;
    padding: 32px;
    box-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
    transition: all 0.3s ease;
    text-align: center;
    
    &:active {
      box-shadow: inset 9px 9px 16px #a3b1c6, inset -9px -9px 16px #ffffff;
    }
    
    ion-icon {
      font-size: 40px;
      color: var(--ion-color-primary);
      margin-bottom: 12px;
    }
    
    .neu-label {
      font-size: 14px;
      font-weight: 600;
      color: #4a5568;
    }
  }
}

// ANIMATED ELEMENTS
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

.pulse-animation {
  animation: pulse 2s ease-in-out infinite;
}

// Responsive adjustments
@media (max-width: 320px) {
  .modern-header {
    height: 240px;
    
    .header-content {
      padding: 24px 16px;
      
      .user-name {
        font-size: 26px;
      }
    }
  }
  
  .glass-balance-cards {
    padding: 0 16px;
    
    .glass-card {
      padding: 20px;
      
      .card-value {
        font-size: 24px;
      }
    }
  }
  
  .modern-actions {
    padding: 24px 16px;
    
    .modern-action-card {
      height: 120px;
      padding: 20px;
    }
  }
}