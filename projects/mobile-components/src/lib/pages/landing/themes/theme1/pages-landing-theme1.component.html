<lib-page-wrapper [containerSize]="'xl'" [hasBackground]="true" [layout]="!isMobile ? 'custom' : 'single'">
  <div header>
    <lib-head-logo
      [names]="profile.givenNames + ' ' + profile.surname"
      [membership]="profile.newMembershipNumber"
      type="welcome"
      [balance]="profile.currentBalance"
      [src]="lssConfig.pages.landing.loggedinIcon"
    />
  </div>

  <!-- Hero Section (Desktop Only) - Place in custom slot -->
  <div custom *ngIf="!isMobile">
    <div class="hero-card">
      <div class="hero-container">
        <!-- Desktop Balance Cards -->
        <div class="desktop-balance-grid">
          <div class="balance-card">
            <div class="balance-info">
              <span class="balance-label">Rand Value</span>
              <span class="balance-amount">R {{ formatCurrency(profile?.availRands) }}</span>
            </div>
            <div class="balance-icon">
              <ion-icon name="wallet-outline"></ion-icon>
            </div>
          </div>
          
          <div class="balance-card">
            <div class="balance-info">
              <span class="balance-label">Available Points</span>
              <span class="balance-amount">{{ formatNumber(profile?.currentBalance) }}</span>
            </div>
            <div class="balance-icon">
              <ion-icon name="diamond-outline"></ion-icon>
            </div>
          </div>
          
          <div class="stats-card">
            <div class="stat-row">
              <span class="stat-label">Member Status</span>
              <span class="stat-value">{{ getMemberTier() }}</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">Member Since</span>
              <span class="stat-value">{{ getDaysAsMember() }} days</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Main Grid Content Container -->
    <div class="content-grid">
      <!-- Transaction Chart (2/3 width) -->
      <div class="main-content">
        <!-- Transaction Chart -->
        <div class="section-card transaction-chart">
      <div class="chart-header">
        <h3 class="section-title">
          <ion-icon name="bar-chart-outline"></ion-icon>
          Transaction History - {{ getPeriodText() }}
        </h3>
        
        <div class="chart-controls">
          <button class="view-toggle-btn" (click)="toggleViewMode()">
            <ion-icon [name]="chartViewMode === 'combined' ? 'layers-outline' : 'analytics-outline'"></ion-icon>
            {{ chartViewMode === 'combined' ? 'Show Details' : 'Show Combined' }}
          </button>
          
          <div class="period-filter">
          <button class="filter-btn" 
                  [class.active]="selectedPeriod === 'week'"
                  (click)="changePeriod('week')">
            Last Week
          </button>
          <button class="filter-btn" 
                  [class.active]="selectedPeriod === 'month'"
                  (click)="changePeriod('month')">
            Last Month
          </button>
          <button class="filter-btn" 
                  [class.active]="selectedPeriod === 'year'"
                  (click)="changePeriod('year')">
            Last Year
          </button>
          <button class="filter-btn" 
                  [class.active]="selectedPeriod === 'all'"
                  (click)="changePeriod('all')">
            All
          </button>
          </div>
        </div>
      </div>
      
      <div class="chart-container" [class.loading]="chartLoading">
        <div class="loading-overlay" *ngIf="chartLoading">
          <ion-spinner name="crescent"></ion-spinner>
        </div>
        <div class="chart-bars">
          <div class="chart-bar-group" *ngFor="let day of getTransactionData(); let i = index" 
               [style.--bar-index]="i">
            <!-- Combined View -->
            <ng-container *ngIf="chartViewMode === 'combined'">
              <div class="bar-wrapper">
                <div class="bar-earned" 
                     [style.height.%]="getChartHeight(day.earned)"
                     [attr.data-value]="day.earned"></div>
              </div>
              <div class="bar-wrapper">
                <div class="bar-spent" 
                     [style.height.%]="getChartHeight(day.spent)"
                     [attr.data-value]="day.spent"></div>
              </div>
            </ng-container>
            
            <!-- Detailed View -->
            <ng-container *ngIf="chartViewMode === 'detailed'">
              <div class="bar-wrapper">
                <div class="bar-accrual" 
                     [style.height.%]="getChartHeight(day.accrual)"
                     [attr.data-value]="day.accrual"
                     title="Accrual"></div>
              </div>
              <div class="bar-wrapper">
                <div class="bar-redemption" 
                     [style.height.%]="getChartHeight(day.redemption)"
                     [attr.data-value]="day.redemption"
                     title="Redemption"></div>
              </div>
              <div class="bar-wrapper">
                <div class="bar-bonus" 
                     [style.height.%]="getChartHeight(day.bonus)"
                     [attr.data-value]="day.bonus"
                     title="Bonus"></div>
              </div>
              <div class="bar-wrapper">
                <div class="bar-refund" 
                     [style.height.%]="getChartHeight(day.refund)"
                     [attr.data-value]="day.refund"
                     title="Refund"></div>
              </div>
              <div class="bar-wrapper">
                <div class="bar-reversal" 
                     [style.height.%]="getChartHeight(day.reversal)"
                     [attr.data-value]="day.reversal"
                     title="Reversal"></div>
              </div>
            </ng-container>
            
            <span class="bar-label">{{ day.label }}</span>
          </div>
        </div>
        <div class="chart-legend">
          <!-- Combined View Legend -->
          <ng-container *ngIf="chartViewMode === 'combined'">
            <span class="legend-item earned">
              <span class="legend-dot"></span>
              Points Earned
            </span>
            <span class="legend-item spent">
              <span class="legend-dot"></span>
              Points Spent
            </span>
          </ng-container>
          
          <!-- Detailed View Legend -->
          <ng-container *ngIf="chartViewMode === 'detailed'">
            <span class="legend-item accrual">
              <span class="legend-dot"></span>
              Accrual
            </span>
            <span class="legend-item redemption">
              <span class="legend-dot"></span>
              Redemption
            </span>
            <span class="legend-item bonus">
              <span class="legend-dot"></span>
              Bonus
            </span>
            <span class="legend-item refund">
              <span class="legend-dot"></span>
              Refund
            </span>
            <span class="legend-item reversal">
              <span class="legend-dot"></span>
              Reversal
            </span>
          </ng-container>
        </div>
        <div class="chart-summary">
          <div class="summary-item">
            <span class="summary-label">Total Earned:</span>
            <span class="summary-value earned">+{{ getTotalEarned() }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Total Spent:</span>
            <span class="summary-value spent">-{{ getTotalSpent() }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Net Change:</span>
            <span class="summary-value" [class.positive]="getNetChange() > 0" [class.negative]="getNetChange() < 0">
              {{ getNetChange() > 0 ? '+' : '' }}{{ getNetChange() }}
            </span>
          </div>
        </div>
      </div>
    </div>
      </div>
      
      <!-- Sidebar Content (1/3 width) -->
      <div class="sidebar-content">
    <!-- Quick Actions Card -->
    <div class="section-card quick-actions">
      <h3 class="section-title">
        <ion-icon name="flash-outline"></ion-icon>
        Quick Actions
      </h3>
      
      <div class="quick-action-list">
        <a class="quick-action-item" (click)="navigateWithAnimation('/app/account', 'profile')">
          <ion-icon name="person-outline"></ion-icon>
          <span>My Profile</span>
          <ion-icon name="chevron-forward-outline" class="arrow"></ion-icon>
        </a>
        
        <a class="quick-action-item" (click)="navigateWithAnimation('/app/virtualcard', 'card')">
          <ion-icon name="card-outline"></ion-icon>
          <span>Virtual Card</span>
          <ion-icon name="chevron-forward-outline" class="arrow"></ion-icon>
        </a>
        
        <a class="quick-action-item" (click)="navigateWithAnimation('/app/transactions', 'transactions')">
          <ion-icon name="list-outline"></ion-icon>
          <span>All Transactions</span>
          <ion-icon name="chevron-forward-outline" class="arrow"></ion-icon>
        </a>
        
        <a class="quick-action-item" (click)="navigateWithAnimation('/app/stores', 'stores')">
          <ion-icon name="storefront-outline"></ion-icon>
          <span>Find Stores</span>
          <ion-icon name="chevron-forward-outline" class="arrow"></ion-icon>
        </a>
      </div>
      
      <!-- Points Top-up Card -->
      <div class="topup-card">
        <div class="topup-header">
          <ion-icon name="add-circle-outline"></ion-icon>
          <h4>Quick Top-up</h4>
        </div>
        
        <p class="topup-description">Need more points? Top up instantly!</p>
        
        <div class="topup-options">
          <button class="topup-btn" (click)="navigateWithAnimation('/app/points-topup', 'topup')">
            <span class="points">500</span>
            <span class="price">R50</span>
          </button>
          <button class="topup-btn" (click)="navigateWithAnimation('/app/points-topup', 'topup')">
            <span class="points">1000</span>
            <span class="price">R100</span>
          </button>
          <button class="topup-btn" (click)="navigateWithAnimation('/app/points-topup', 'topup')">
            <span class="points">2000</span>
            <span class="price">R200</span>
          </button>
        </div>
        
        <a class="topup-link" (click)="navigateWithAnimation('/app/points-topup', 'topup')">
          More options
          <ion-icon name="arrow-forward-outline"></ion-icon>
        </a>
      </div>
    </div>
      </div>
    </div>
  </div>

  <!-- Mobile/Tablet Cards - Show these on mobile, hide on desktop -->
  <div *ngIf="isMobile">
    <ion-card class="primary card">
      <ion-card-content class="quick-action">
        <ion-row class="ion-justify-content-between ion-align-items-center">
          <ion-card-title class="primary">Rand Value</ion-card-title>
          <ion-card-title *ngIf="profile?.availRands" class="primary" [style.font-size]="calculateFontSize('R ' + formatCurrency(profile?.availRands))">
            R {{ formatCurrency(profile?.availRands) }}
          </ion-card-title>
          <ion-card-title *ngIf="!profile?.availRands" class="primary" [style.font-size]="calculateFontSize('R 0.00')">
            R 0.00
          </ion-card-title>
        </ion-row>
      </ion-card-content>
    </ion-card>
    <ion-card class="card primary">
      <ion-card-content class="quick-action">
        <ion-row class="ion-justify-content-between ion-align-items-center">
          <ion-card-title class="primary">Points</ion-card-title>
          <ion-card-title class="primary" [style.font-size]="calculateFontSize(formatNumber(profile?.currentBalance))">
            {{ formatNumber(profile?.currentBalance) }}
          </ion-card-title>
        </ion-row>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Action Cards Grid - Mobile Only -->
  <ion-grid [class]="lssConfig.pages.landing.action_card.class" class="action-grid" *ngIf="isMobile">
    <ion-row>
      <ion-col [routerLink]="['/app/account']">
        <div [class]="lssConfig.pages.landing.action_card.profile.class" class="action-card">
          <div>
            <div [class]="lssConfig.pages.landing.action_card.profile.class_icon_outer">
              <ion-icon
                [class]="lssConfig.pages.landing.action_card.profile.class_icon"
                [name]="lssConfig.pages.landing.action_card.profile.icon"
              ></ion-icon>
            </div>
            <p [class]="lssConfig.pages.landing.action_card.profile.class_text">
              {{ lssConfig.pages.landing.action_card.profile.text }}
            </p>
          </div>
        </div>
      </ion-col>
      <ion-col [routerLink]="['/app/virtualcard']">
        <div [class]="lssConfig.pages.landing.action_card.card.class" class="action-card">
          <div>
            <div [class]="lssConfig.pages.landing.action_card.card.class_icon_outer">
              <ion-icon
                [class]="lssConfig.pages.landing.action_card.card.class_icon"
                [name]="lssConfig.pages.landing.action_card.card.icon"
              ></ion-icon>
            </div>
            <p [class]="lssConfig.pages.landing.action_card.card.class_text">
              {{ lssConfig.pages.landing.action_card.card.text }}
            </p>
          </div>
        </div>
      </ion-col>
    </ion-row>
    <!-- Mobile only - keep second row separate -->
    <br />
    <ion-row>
      <ion-col [routerLink]="['/app/transactions']">
        <div [class]="lssConfig.pages.landing.action_card.history.class" class="action-card">
          <div>
            <div [class]="lssConfig.pages.landing.action_card.history.class_icon_outer">
              <ion-icon
                [class]="lssConfig.pages.landing.action_card.history.class_icon"
                [name]="lssConfig.pages.landing.action_card.history.icon"
              ></ion-icon>
            </div>
            <p [class]="lssConfig.pages.landing.action_card.history.class_text">
              {{ lssConfig.pages.landing.action_card.history.text }}
            </p>
          </div>
        </div>
      </ion-col>
      <ion-col [routerLink]="['/app/stores']">
        <div [class]="lssConfig.pages.landing.action_card.stores.class" class="action-card">
          <div>
            <div [class]="lssConfig.pages.landing.action_card.stores.class_icon_outer">
              <ion-icon
                [class]="lssConfig.pages.landing.action_card.stores.class_icon"
                [name]="lssConfig.pages.landing.action_card.stores.icon"
              ></ion-icon>
            </div>
            <p [class]="lssConfig.pages.landing.action_card.stores.class_text">
              {{ lssConfig.pages.landing.action_card.stores.text }}
            </p>
          </div>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>
</lib-page-wrapper>