/* Modern App Background */
.app-background {
  --background: #f5f7fa;
  position: relative;
}

/* Split Balance Cards */
.balance-section {
  margin-top: -30px;
  padding: 0 16px 16px;
  position: relative;
  z-index: 2;
  animation: slideUp 0.6s ease-out;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.balance-card-full {
  background: var(--ion-color-primary, #f5821f);
  border-radius: 16px;
  padding: 16px 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 65px;
  color: white;
  
  &.rand-value {
    background: var(--ion-color-primary, #f5821f);
  }
  
  &.points {
    background: var(--ion-color-primary, #f5821f);
  }
  
  .balance-label {
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 0;
    margin: 0;
    color: white;
  }
  
  .balance-value {
    font-size: 20px;
    font-weight: 700;
    line-height: 1;
    margin: 0;
    color: white;
    text-align: right;
  }
}

/* Modern Action Grid */
.modern-actions {
  padding: 16px 16px 38px;
}

.actions-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.modern-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  grid-template-rows: repeat(2, 100px); // Explicit row heights for perfect alignment
  align-items: start; // Align all cards to the top of their grid cells
}

.modern-action-card {
  background: #1e3a5f; /* Dark blue background */
  border-radius: 20px;
  padding: 20px;
  height: 100px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  cursor: pointer;
  gap: 4px;
  margin: 0; // Ensure no margin differences
  vertical-align: top; // Ensure consistent baseline alignment
}

.modern-action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
}

.modern-action-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
}

/* All action cards have dark blue background */
.modern-action-card.profile,
.modern-action-card.card,
.modern-action-card.transactions,
.modern-action-card.stores {
  background: #1e3a5f; /* Dark blue */
}

/* Action icons - yellow circular backgrounds with dark blue icons */
.modern-action-card .orange-action-icon {
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  position: relative !important;
  top: -25px !important;
  margin: auto !important;
  margin-bottom: 0 !important;
  flex-shrink: 0 !important;
  background: #FFD700 !important; /* Yellow background */
}

.modern-action-card .orange-action-icon ion-icon {
  font-size: 28px !important;
  color: #1e3a5f !important; /* Dark blue icon color - same as card background */
}

.action-label {
  font-size: 14px;
  font-weight: 600;
  color: white; /* White text for dark blue background */
  margin: 0;
  text-align: center;
  line-height: 1.2;
}

/* Diagonal animation for card exit */
.modern-action-card.animating-diagonal {
  animation: diagonalExit 0.6s ease-out forwards;
  z-index: 100;
}

.modern-action-card.animating-diagonal .orange-action-icon {
  animation: iconSpin 0.6s ease-out;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes diagonalExit {
  0% {
    transform: scale(1) translate(0, 0);
    opacity: 1;
  }
  100% {
    transform: scale(1.2) translate(150px, -150px) rotate(15deg);
    opacity: 0;
  }
}

@keyframes iconSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 375px) {
  .balance-section {
    padding: 0 12px 12px;
    gap: 10px;
  }
  
  .balance-card-full {
    padding: 14px 16px;
    min-height: 60px;
  }
  
  .balance-value {
    font-size: 18px;
  }
  
  .balance-label {
    font-size: 18px;
  }
  
  .modern-actions {
    padding: 12px 16px 24px;
  }
  
  .actions-title {
    font-size: 18px;
    margin-bottom: 16px;
  }
  
  .modern-action-card {
    padding: 16px;
    gap: 2px;
  }

  .modern-action-card .orange-action-icon {
    width: 50px !important;
    height: 50px !important;
    margin-bottom: 0 !important;
  }

  .modern-action-card .orange-action-icon ion-icon {
    font-size: 24px !important;
  }

  .action-label {
    font-size: 12px;
  }
}

.landing_old {
  // background-image: url("~/src/assets/images/landing.jfif");
  background-size: cover;
  background-position: center;
  --background: transparent;
  .header {
    text-align: center;

    .logo {
      position: relative;
      padding-bottom: 30px;
    }

    img {
      margin: auto;
      display: block;
    }
  }

  ion-card {
    box-shadow: none;
    border-radius: 20px;

    h1 {
      font-weight: bold;
      font-size: 20px;
      text-align: center;
      margin-bottom: 5px;
    }

    p {
      text-align: center;
      font-size: 16px;
      margin-bottom: 45px;
    }

    .line-text {
      padding: 2px;
      margin-top: 20px;
      position: relative;

      .line {
        height: 1px;
        background: #858585;
        display: block;
      }

      .text {
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        background: #fff;
        padding: 0 15px;
        font-size: 10px;
        font-weight: 500;
      }

      //@media (prefers-color-scheme: dark) {
      //    .text {
      //        background: #1e1e1e;
      //    }
      //}
    }
  }
}

ion-toolbar {
  display: none;
}
.landing2 {
  --background: var(--ion-color-base);
  padding: 40px;
}
.landing {
  --background: var(--ion-color-base);
  padding: 20px;
  position: relative;
  height: 100vh;

  .logo {
    position: absolute;
    width: 100vw;
    top: 4%;
    -ms-transform: translateY(-4%);
    transform: translateY(-4%);
    display: flex;
    justify-content: center;

    img {
      width: 50vw;
      max-width: 300px;
    }
  }

  .content {
    position: absolute;
    width: 100vw;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;

    ion-button {
      width: 80vw;
    }
  }
  .content-title {
    text-align: center;
  }

  ion-button {
    --background: var(--ion-color-primary);
    --color: var(--ion-color-primaryContrast);
  }
  .login-title {
    color: var(--ion-color-baseContrast);
    margin-bottom: 6px;
  }

  .login-subtitle {
    color: var(--ion-color-baseContrast);
    margin-top: 0;
    margin-bottom: 14px;
    font-weight: 300;
  }

  .socials {
    position: absolute;
    width: 100vw;
    bottom: 3%;
    -ms-transform: translateY(3%);
    transform: translateY(3%);
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    ion-button {
      --background: transparent;
      --box-shadow: none;
    }

    ion-icon {
      color: #fff;
      font-size: 2.4em;
    }
  }
}
