/* Modern App Background */
.app-background {
  --background: #f5f7fa;
  position: relative;
  height: 100%;
}

/* Split Balance Cards */
.balance-section {
  margin-top: -30px;
  padding: 0 16px 16px;
  position: relative;
  z-index: 2;
  animation: slideUp 0.6s ease-out;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.balance-card-full {
  background: var(--ion-color-primary, #f5821f);
  border-radius: 16px;
  padding: 16px 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 65px;
  color: white;
  
  &.rand-value {
    background: var(--ion-color-primary, #f5821f);
  }
  
  &.points {
    background: var(--ion-color-primary, #f5821f);
  }
  
  .balance-label {
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 0;
    margin: 0;
    color: white;
  }
  
  .balance-value {
    font-size: 20px;
    font-weight: 700;
    line-height: 1;
    margin: 0;
    color: white;
    text-align: right;
  }
}

/* Modern Action Grid */
.modern-actions {
  padding: 16px 16px 38px;
}

.actions-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.modern-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  grid-template-rows: repeat(2, 100px); // Explicit row heights for perfect alignment
  align-items: start; // Align all cards to the top of their grid cells
}

.modern-action-card {
  background: #1e3a5f; /* Dark blue background */
  border-radius: 20px;
  padding: 20px;
  height: 100px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  cursor: pointer;
  gap: 4px;
  margin: 0; // Ensure no margin differences
  vertical-align: top; // Ensure consistent baseline alignment
}

.modern-action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.2);
}

.modern-action-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
}

/* All action cards have dark blue background */
.modern-action-card.profile,
.modern-action-card.card,
.modern-action-card.transactions,
.modern-action-card.stores {
  background: #1e3a5f; /* Dark blue */
}

/* Action icons - yellow circular backgrounds with dark blue icons */
.modern-action-card .orange-action-icon {
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  position: relative !important;
  top: -25px !important;
  margin: auto !important;
  margin-bottom: 0 !important;
  flex-shrink: 0 !important;
  background: #FFD700 !important; /* Yellow background */
}

.modern-action-card .orange-action-icon ion-icon {
  font-size: 28px !important;
  color: #1e3a5f !important; /* Dark blue icon color - same as card background */
}

.action-label {
  font-size: 14px;
  font-weight: 600;
  color: white; /* White text for dark blue background */
  margin: 0;
  text-align: center;
  line-height: 1.2;
}

/* Diagonal animation for card exit */
.modern-action-card.animating-diagonal {
  animation: diagonalExit 0.6s ease-out forwards;
  z-index: 100;
}

.modern-action-card.animating-diagonal .orange-action-icon {
  animation: iconSpin 0.6s ease-out;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes diagonalExit {
  0% {
    transform: scale(1) translate(0, 0);
    opacity: 1;
  }
  100% {
    transform: scale(1.2) translate(150px, -150px) rotate(15deg);
    opacity: 0;
  }
}

@keyframes iconSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 375px) {
  .balance-section {
    padding: 0 12px 12px;
    gap: 10px;
  }
  
  .balance-card-full {
    padding: 14px 16px;
    min-height: 60px;
  }
  
  .balance-value {
    font-size: 18px;
  }
  
  .balance-label {
    font-size: 18px;
  }
  
  .modern-actions {
    padding: 12px 16px 24px;
  }
  
  .actions-title {
    font-size: 18px;
    margin-bottom: 16px;
  }
  
  .modern-action-card {
    padding: 16px;
    gap: 2px;
  }

  .modern-action-card .orange-action-icon {
    width: 50px !important;
    height: 50px !important;
    margin-bottom: 0 !important;
  }

  .modern-action-card .orange-action-icon ion-icon {
    font-size: 24px !important;
  }

  .action-label {
    font-size: 12px;
  }
}

.landing_old {
  // background-image: url("~/src/assets/images/landing.jfif");
  background-size: cover;
  background-position: center;
  --background: transparent;
  .header {
    text-align: center;

    .logo {
      position: relative;
      padding-bottom: 30px;
    }

    img {
      margin: auto;
      display: block;
    }
  }

  ion-card {
    box-shadow: none;
    border-radius: 20px;

    h1 {
      font-weight: bold;
      font-size: 20px;
      text-align: center;
      margin-bottom: 5px;
    }

    p {
      text-align: center;
      font-size: 16px;
      margin-bottom: 45px;
    }

    .line-text {
      padding: 2px;
      margin-top: 20px;
      position: relative;

      .line {
        height: 1px;
        background: #858585;
        display: block;
      }

      .text {
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        background: #fff;
        padding: 0 15px;
        font-size: 10px;
        font-weight: 500;
      }

      //@media (prefers-color-scheme: dark) {
      //    .text {
      //        background: #1e1e1e;
      //    }
      //}
    }
  }
}

ion-toolbar {
  display: none;
}
.landing2 {
  --background: var(--ion-color-base);
  padding: 40px;
}
.landing {
  --background: var(--ion-color-base);
  padding: 20px;
  position: relative;
  display: flex;
  flex-direction: column;

  .logo {
    position: absolute;
    width: 100vw;
    top: 4%;
    -ms-transform: translateY(-4%);
    transform: translateY(-4%);
    display: flex;
    justify-content: center;

    img {
      width: 50vw;
      max-width: 300px;
    }
  }

  .content {
    position: absolute;
    width: 100vw;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;

    ion-button {
      width: 80vw;
    }
  }
  .content-title {
    text-align: center;
  }

  ion-button {
    --background: var(--ion-color-primary);
    --color: var(--ion-color-primaryContrast);
  }
  .login-title {
    color: var(--ion-color-baseContrast);
    margin-bottom: 6px;
  }

  .login-subtitle {
    color: var(--ion-color-baseContrast);
    margin-top: 0;
    margin-bottom: 14px;
    font-weight: 300;
  }

  .socials {
    position: absolute;
    width: 100vw;
    bottom: 3%;
    -ms-transform: translateY(3%);
    transform: translateY(3%);
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    ion-button {
      --background: transparent;
      --box-shadow: none;
    }

    ion-icon {
      color: #fff;
      font-size: 2.4em;
    }
  }
}

/* Component-specific styles only - layout handled by page-wrapper */

/* Hero Section - inherits styles from page-wrapper .hero-card class */

  .hero-container {
    position: relative;
  }

  .desktop-balance-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    
    .balance-card {
      background: white;
      border: 1px solid rgba(0, 0, 0, 0.06);
      border-radius: 16px;
      padding: 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      animation: scaleIn 0.5s ease-out backwards;
      
      &:nth-child(1) {
        animation-delay: 0.1s;
      }
      
      &:nth-child(2) {
        animation-delay: 0.2s;
      }
      
      &:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
        border-color: var(--ion-color-primary, #f5821f);
        
        .balance-icon ion-icon {
          transform: rotate(10deg) scale(1.1);
        }
      }
      
      .balance-info {
        flex: 1;
        
        .balance-label {
          display: block;
          font-size: 0.875rem;
          color: #64748b;
          margin-bottom: 8px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          font-weight: 500;
        }
        
        .balance-amount {
          display: block;
          font-size: 1.75rem;
          font-weight: 700;
          line-height: 1;
          color: #2d3748;
        }
      }
      
      .balance-icon {
        ion-icon {
          font-size: 2rem;
          color: var(--ion-color-primary, #f5821f);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }
    }
    
    .stats-card {
      background: white;
      border: 1px solid rgba(0, 0, 0, 0.06);
      border-radius: 16px;
      padding: 24px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        border-color: var(--ion-color-primary, #f5821f);
      }
      
      .stat-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        &:not(:last-child) {
          margin-bottom: 16px;
          padding-bottom: 16px;
          border-bottom: 1px solid #e9ecef;
        }
        
        .stat-label {
          font-size: 0.875rem;
          color: #64748b;
          font-weight: 500;
        }
        
        .stat-value {
          font-size: 1rem;
          font-weight: 600;
          color: #2d3748;
        }
      }
    }
  }

/* Content grid for custom layout */
.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-top: 24px;
  
  @media (max-width: 1199px) {
    grid-template-columns: 1fr;
  }
  
  .main-content {
    min-width: 0; // Prevent grid blowout
  }
  
  .sidebar-content {
    min-width: 0; // Prevent grid blowout
  }
}

/* Transaction Chart - uses section-card from page-wrapper */
.transaction-chart {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
  }

  .chart-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
    
    .view-toggle-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: white;
      border: 2px solid var(--ion-color-primary, #f5821f);
      color: var(--ion-color-primary, #f5821f);
      border-radius: 8px;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      
      ion-icon {
        font-size: 1.125rem;
      }
      
      &:hover {
        background: var(--ion-color-primary, #f5821f);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(245, 130, 31, 0.3);
      }
    }
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;

    ion-icon {
      font-size: 1.5rem;
      color: var(--ion-color-primary, #f5821f);
    }
  }

  .period-filter {
    display: flex;
    gap: 8px;
    
    .filter-btn {
      padding: 8px 16px;
      border: 1px solid #e9ecef;
      background: white;
      color: #64748b;
      border-radius: 8px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      
      &:hover {
        background: #f8f9fa;
        border-color: var(--ion-color-primary, #f5821f);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
      }
      
      &.active {
        background: var(--ion-color-primary, #f5821f);
        color: white;
        border-color: var(--ion-color-primary, #f5821f);
        transform: scale(1.05);
        
        &:hover {
          transform: scale(1.08) translateY(-2px);
          box-shadow: 0 6px 12px rgba(245, 130, 31, 0.3);
        }
      }
    }
  }

  .chart-container {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #e9ecef;
    position: relative;
    transition: all 0.3s ease;
    
    &.loading {
      opacity: 0.5;
    }
    
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 12px;
      z-index: 10;
      
      ion-spinner {
        --color: var(--ion-color-primary, #f5821f);
      }
    }

    .chart-bars {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      height: 200px;
      margin-bottom: 20px;
      padding-bottom: 30px;
      position: relative;

      .chart-bar-group {
        flex: 1;
        display: flex;
        gap: 4px;
        align-items: flex-end;
        justify-content: center;
        position: relative;
        margin: 0 4px;

        .bar-wrapper {
          display: flex;
          align-items: flex-end;
          height: 200px;
        }

        .bar-earned {
          width: 20px;
          background: linear-gradient(to top, #22c55e, #34d399);
          border-radius: 4px 4px 0 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          min-height: 10px;
          animation: growUp 0.6s ease-out backwards;
          animation-delay: calc(var(--bar-index) * 0.05s);
          position: relative;
          
          &::after {
            content: attr(data-value);
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.7rem;
            font-weight: 600;
            color: #22c55e;
            opacity: 0;
            transition: opacity 0.3s ease;
            white-space: nowrap;
          }
        }

        .bar-spent {
          width: 20px;
          background: linear-gradient(to top, #ef4444, #f87171);
          border-radius: 4px 4px 0 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          min-height: 10px;
          animation: growUp 0.6s ease-out backwards;
          animation-delay: calc(var(--bar-index) * 0.05s + 0.1s);
          position: relative;
          
          &::after {
            content: attr(data-value);
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.7rem;
            font-weight: 600;
            color: #ef4444;
            opacity: 0;
            transition: opacity 0.3s ease;
            white-space: nowrap;
          }
        }

        .bar-label {
          position: absolute;
          bottom: -25px;
          left: 50%;
          transform: translateX(-50%);
          font-size: 0.75rem;
          color: #64748b;
          font-weight: 500;
          transition: all 0.3s ease;
          white-space: nowrap;
        }

        &:hover {
          .bar-earned,
          .bar-spent {
            transform: scaleY(1.1);
            filter: brightness(1.1);
            
            &::after {
              opacity: 1;
            }
          }
          
          .bar-label {
            color: var(--ion-color-primary, #f5821f);
            font-weight: 600;
          }
        }
        
        // Detailed view bars
        .bar-accrual {
          width: 12px;
          background: linear-gradient(to top, #22c55e, #34d399);
          border-radius: 4px 4px 0 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          min-height: 10px;
          animation: growUp 0.6s ease-out backwards;
          animation-delay: calc(var(--bar-index) * 0.05s);
          position: relative;
          
          &::after {
            content: attr(data-value);
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.65rem;
            font-weight: 600;
            color: #22c55e;
            opacity: 0;
            transition: opacity 0.3s ease;
            white-space: nowrap;
          }
        }
        
        .bar-redemption {
          width: 12px;
          background: linear-gradient(to top, #ef4444, #f87171);
          border-radius: 4px 4px 0 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          min-height: 10px;
          animation: growUp 0.6s ease-out backwards;
          animation-delay: calc(var(--bar-index) * 0.05s + 0.05s);
          position: relative;
          
          &::after {
            content: attr(data-value);
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.65rem;
            font-weight: 600;
            color: #ef4444;
            opacity: 0;
            transition: opacity 0.3s ease;
            white-space: nowrap;
          }
        }
        
        .bar-bonus {
          width: 12px;
          background: linear-gradient(to top, #f59e0b, #fbbf24);
          border-radius: 4px 4px 0 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          min-height: 10px;
          animation: growUp 0.6s ease-out backwards;
          animation-delay: calc(var(--bar-index) * 0.05s + 0.1s);
          position: relative;
          
          &::after {
            content: attr(data-value);
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.65rem;
            font-weight: 600;
            color: #f59e0b;
            opacity: 0;
            transition: opacity 0.3s ease;
            white-space: nowrap;
          }
        }
        
        .bar-refund {
          width: 12px;
          background: linear-gradient(to top, #3b82f6, #60a5fa);
          border-radius: 4px 4px 0 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          min-height: 10px;
          animation: growUp 0.6s ease-out backwards;
          animation-delay: calc(var(--bar-index) * 0.05s + 0.15s);
          position: relative;
          
          &::after {
            content: attr(data-value);
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.65rem;
            font-weight: 600;
            color: #3b82f6;
            opacity: 0;
            transition: opacity 0.3s ease;
            white-space: nowrap;
          }
        }
        
        .bar-reversal {
          width: 12px;
          background: linear-gradient(to top, #8b5cf6, #a78bfa);
          border-radius: 4px 4px 0 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          min-height: 10px;
          animation: growUp 0.6s ease-out backwards;
          animation-delay: calc(var(--bar-index) * 0.05s + 0.2s);
          position: relative;
          
          &::after {
            content: attr(data-value);
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.65rem;
            font-weight: 600;
            color: #8b5cf6;
            opacity: 0;
            transition: opacity 0.3s ease;
            white-space: nowrap;
          }
        }
        
        // Hover effects for detailed bars
        &:hover {
          .bar-accrual,
          .bar-redemption,
          .bar-bonus,
          .bar-refund,
          .bar-reversal {
            transform: scaleY(1.1);
            filter: brightness(1.1);
            
            &::after {
              opacity: 1;
            }
          }
        }
      }
    }

    .chart-legend {
      display: flex;
      justify-content: center;
      gap: 24px;
      margin-bottom: 20px;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.875rem;
        color: #64748b;

        .legend-dot {
          width: 12px;
          height: 12px;
          border-radius: 2px;
          display: inline-block;
        }

        &.earned .legend-dot {
          background: linear-gradient(to right, #22c55e, #34d399);
        }

        &.spent .legend-dot {
          background: linear-gradient(to right, #ef4444, #f87171);
        }
        
        // Detailed view legend colors
        &.accrual .legend-dot {
          background: linear-gradient(to right, #22c55e, #34d399);
        }
        
        &.redemption .legend-dot {
          background: linear-gradient(to right, #ef4444, #f87171);
        }
        
        &.bonus .legend-dot {
          background: linear-gradient(to right, #f59e0b, #fbbf24);
        }
        
        &.refund .legend-dot {
          background: linear-gradient(to right, #3b82f6, #60a5fa);
        }
        
        &.reversal .legend-dot {
          background: linear-gradient(to right, #8b5cf6, #a78bfa);
        }
      }
    }

    .chart-summary {
      display: flex;
      justify-content: space-around;
      padding-top: 20px;
      border-top: 1px solid #e9ecef;

      .summary-item {
        text-align: center;

        .summary-label {
          display: block;
          font-size: 0.875rem;
          color: #64748b;
          margin-bottom: 4px;
        }

        .summary-value {
          display: block;
          font-size: 1.25rem;
          font-weight: 700;

          &.earned {
            color: #22c55e;
          }

          &.spent {
            color: #ef4444;
          }

          &.positive {
            color: #22c55e;
          }

          &.negative {
            color: #ef4444;
          }
        }
      }
    }
  }
}

/* Quick Actions - uses section-card from page-wrapper */
.quick-actions {
  height: fit-content;
  display: flex;
  flex-direction: column;
  gap: 20px;

  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 24px 0;

    ion-icon {
      font-size: 1.5rem;
      color: var(--ion-color-primary, #f5821f);
    }
  }

  .quick-action-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;

    .quick-action-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 12px;
      border: 1px solid #e9ecef;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      color: #2d3748;

      &:hover {
        background: #f1f3f5;
        border-color: var(--ion-color-primary, #f5821f);
        transform: translateX(4px);

        .arrow {
          transform: translateX(4px);
          color: var(--ion-color-primary, #f5821f);
        }
      }

      ion-icon {
        font-size: 1.25rem;
        color: var(--ion-color-primary, #f5821f);
        flex-shrink: 0;
      }

      span {
        flex: 1;
        font-size: 0.9375rem;
        font-weight: 500;
      }

      .arrow {
        font-size: 1rem;
        color: #cbd5e1;
        transition: all 0.3s ease;
      }
    }
  }

  // Points Top-up Card
  .topup-card {
    background: linear-gradient(135deg, #f5821f 0%, #ff9a44 100%);
    border-radius: 16px;
    padding: 20px;
    color: white;
    margin-top: auto;
    animation: slideUp 0.6s ease-out backwards;
    animation-delay: 0.3s;
    
    .topup-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      
      ion-icon {
        font-size: 1.5rem;
      }
      
      h4 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
      }
    }
    
    .topup-description {
      font-size: 0.875rem;
      margin-bottom: 16px;
      opacity: 0.95;
    }
    
    .topup-options {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
      margin-bottom: 16px;
      
      .topup-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        padding: 12px 8px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        
        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        &:active {
          transform: scale(0.98);
        }
        
        .points {
          display: block;
          font-size: 1rem;
          font-weight: 700;
          color: white;
          margin-bottom: 4px;
        }
        
        .price {
          display: block;
          font-size: 0.75rem;
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
    
    .topup-link {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      color: white;
      text-decoration: none;
      font-size: 0.875rem;
      font-weight: 500;
      padding: 8px;
      border-radius: 6px;
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
        
        ion-icon {
          transform: translateX(3px);
        }
      }
      
      ion-icon {
        font-size: 1rem;
        transition: transform 0.3s ease;
      }
    }
  }
}

/* Activity Overview */
.activity-overview {
  background: white;
  border-radius: 20px;
  padding: 32px;
  margin: 0 32px 32px 32px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);

  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 24px 0;

    ion-icon {
      font-size: 1.5rem;
      color: var(--ion-color-primary, #f5821f);
    }
  }

  .activity-grid {
    .quick-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      
      .stat-item {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 12px;
        border-left: 4px solid transparent;
        transition: all 0.3s ease;
        
        &:hover {
          background: #f1f3f5;
          transform: translateX(4px);
        }
        
        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          
          ion-icon {
            font-size: 1.5rem;
          }
          
          &.earned {
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border-left-color: #22c55e;
          }
          
          &.spent {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            border-left-color: #ef4444;
          }
          
          &.transactions {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border-left-color: #3b82f6;
          }
        }
        
        .stat-details {
          flex: 1;
          
          .stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 4px;
            line-height: 1;
          }
          
          .stat-description {
            display: block;
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 500;
          }
        }
      }
    }
  }
}

/* Quick Actions Summary */
.quick-actions-summary {
  background: white;
  border-radius: 20px;
  padding: 32px;
  margin: 0 32px 32px 32px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);

  .section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 24px 0;

    ion-icon {
      font-size: 1.5rem;
      color: var(--ion-color-primary, #f5821f);
    }
  }

  .actions-preview {
    display: flex;
    flex-direction: column;
    gap: 16px;
    
    .action-preview {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 12px;
      border: 1px solid #e9ecef;
      text-decoration: none;
      color: inherit;
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        background: #f1f3f5;
        border-color: var(--ion-color-primary, #f5821f);
        transform: translateX(4px);
        
        .preview-arrow {
          transform: translateX(4px);
          color: var(--ion-color-primary, #f5821f);
        }
      }
      
      .preview-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: rgba(245, 130, 31, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        
        ion-icon {
          font-size: 1.5rem;
          color: var(--ion-color-primary, #f5821f);
        }
      }
      
      .preview-content {
        flex: 1;
        
        .preview-title {
          display: block;
          font-size: 1rem;
          font-weight: 600;
          color: #2d3748;
          margin-bottom: 4px;
        }
        
        .preview-subtitle {
          display: block;
          font-size: 0.875rem;
          color: #64748b;
        }
      }
      
      .preview-arrow {
        font-size: 1.25rem;
        color: #cbd5e1;
        transition: all 0.3s ease;
      }
    }
  }
}

/* Desktop Grid Layout */
.action-grid {
  margin: 0 32px;
  
  &.desktop-grid {
    ion-row {
      ion-col {
        flex: 0 0 25%;
        max-width: 25%;
        padding: 0 8px;
      }
    }
    
    .action-card {
      background: white;
      border-radius: 16px;
      padding: 24px 16px;
      text-align: center;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      height: 140px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 12px;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        border-color: var(--ion-color-primary, #f5821f);
      }
      
      .orange-action-icon,
      [class*="class_icon_outer"] {
        width: 56px !important;
        height: 56px !important;
        border-radius: 14px !important;
        background: rgba(245, 130, 31, 0.1) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin: 0 !important;
        position: static !important;
        top: auto !important;
        box-shadow: none !important;
        
        ion-icon {
          font-size: 1.75rem !important;
          color: var(--ion-color-primary, #f5821f) !important;
        }
      }
      
      p,
      [class*="class_text"] {
        font-size: 0.875rem !important;
        font-weight: 600 !important;
        color: #2d3748 !important;
        margin: 0 !important;
        text-align: center !important;
      }
    }
  }
}

/* Mobile-specific overrides to ensure mobile stays unchanged */
@media (max-width: 767px) {
  .landing-container {
    &.desktop-layout {
      padding: 20px;
      background: transparent;
    }
  }
  
  // Hide all desktop elements on mobile
  .desktop-hero,
  .desktop-content-grid,
  .activity-overview,
  .quick-actions-summary {
    display: none !important;
  }
  
  // Ensure mobile action cards remain unchanged
  .action-grid {
    margin: 0;
    
    .action-card {
      // Keep original mobile styling
      background: transparent;
      border-radius: 0;
      box-shadow: none;
      border: none;
      padding: 16px;
    }
  }
}

/* Desktop Responsive Adjustments */
@media (min-width: 768px) {
  // Hide mobile balance cards on desktop
  ion-card.primary.card {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .desktop-hero {
    margin: 0 24px 24px 24px;
    padding: 24px;
    
    .desktop-balance-grid {
      grid-template-columns: 1fr 1fr;
      
      .stats-card {
        grid-column: 1 / -1;
        
        .stat-row {
          justify-content: space-around;
          text-align: center;
          
          &:not(:last-child) {
            border-bottom: none;
            border-right: 1px solid #e9ecef;
            padding-right: 16px;
            margin-bottom: 0;
          }
        }
      }
    }
  }
  
  .desktop-content-grid {
    margin: 0 24px 24px 24px;
    grid-template-columns: 1fr;
    gap: 24px;
    
    .transaction-chart-section,
    .quick-actions-card {
      padding: 24px;
    }
  }
  
  .activity-overview,
  .quick-actions-summary {
    margin: 0 24px 24px 24px;
    padding: 24px;
  }
}

/* Smooth transitions for desktop elements */
.desktop-hero *,
.activity-overview *,
.quick-actions-summary * {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus states for accessibility */
.action-preview:focus,
.balance-card:focus,
.stats-card:focus {
  outline: 2px solid var(--ion-color-primary, #f5821f);
  outline-offset: 2px;
}

/* Final polish - ensure clean desktop experience */
@media (min-width: 768px) {
  .landing-container.desktop-layout {
    // Add subtle animation when switching to desktop
    animation: fadeIn 0.5s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes growUp {
  from {
    transform: scaleY(0);
    opacity: 0;
  }
  to {
    transform: scaleY(1);
    opacity: 1;
  }
}
