<div class="app-background">
  <!-- Modern Header Component -->
  <lib-head-logo
    [names]="profile.givenNames + ' ' + profile.surname"
    [membership]="profile.newMembershipNumber"
    type="welcome"
    [balance]="profile.currentBalance"
    [src]="lssConfig.pages.landing.loggedinIcon"
  />
  
  <!-- Split Balance Cards -->
  <div class="balance-section">
    <!-- Rand Value Card -->
    <div class="balance-card-full rand-value">
      <p class="balance-label">RAND VALUE</p>
      <p class="balance-value">R {{ formatCurrency(profile?.availRands) }}</p>
    </div>
    
    <!-- Points Card -->
    <div class="balance-card-full points">
      <p class="balance-label">POINTS</p>
      <p class="balance-value">{{ formatNumber(profile?.currentBalance) }}</p>
    </div>
  </div>

  <!-- Modern Action Buttons -->
  <div class="modern-actions">
    <!-- <h2 class="actions-title">Quick Actions</h2> -->
    <div class="modern-grid">
      <!-- Profile -->
      <a (click)="navigateWithAnimation('/app/account', 'profile')" 
         class="modern-action-card profile"
         [class.animating-diagonal]="animatingCard === 'profile'">
        <div class="orange-action-icon">
          <ion-icon name="person-outline"></ion-icon>
        </div>
        <p class="action-label">Profile</p>
      </a>
      
      <!-- Card -->
      <a (click)="navigateWithAnimation('/app/virtualcard', 'card')" 
         class="modern-action-card card"
         [class.animating-diagonal]="animatingCard === 'card'">
        <div class="orange-action-icon">
          <ion-icon name="card-outline"></ion-icon>
        </div>
        <p class="action-label">Card</p>
      </a>
      
      <!-- Transactions -->
      <a (click)="navigateWithAnimation('/app/transactions', 'transactions')" 
         class="modern-action-card transactions"
         [class.animating-diagonal]="animatingCard === 'transactions'">
        <div class="orange-action-icon">
          <ion-icon name="receipt-outline"></ion-icon>
        </div>
        <p class="action-label">Transactions</p>
      </a>
      
      <!-- Stores -->
      <a (click)="navigateWithAnimation('/app/stores', 'stores')" 
         class="modern-action-card stores"
         [class.animating-diagonal]="animatingCard === 'stores'">
        <div class="orange-action-icon">
          <ion-icon name="storefront-outline"></ion-icon>
        </div>
        <p class="action-label">Stores</p>
      </a>
    </div>
  </div>
</div>
