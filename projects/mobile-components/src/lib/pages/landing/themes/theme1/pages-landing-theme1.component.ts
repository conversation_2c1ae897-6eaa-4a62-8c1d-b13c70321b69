import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { LssConfig, MemberService, KeyCloakService, Statement } from 'lp-client-api';

@Component({
  selector: 'pages-landing-theme1',
  templateUrl: './pages-landing-theme1.component.html',
  styleUrls: [
    './pages-landing-theme1.component.scss',
    '../../../../../styles.scss',
  ],
})
export class PagesLandingTheme1Component implements OnInit, OnDestroy {
  @Input() public profile: any;
  animatingCard: string | null = null;
  isMobile: boolean = true;
  private resizeListener: any;
  private transactionData: any[] = [];
  private transactions: Statement[] = [];
  selectedPeriod: string = 'week'; // Default to last week
  chartLoading: boolean = false;
  chartViewMode: 'combined' | 'detailed' = 'combined'; // Toggle between combined and detailed view
  
  constructor(
    protected readonly router: Router, 
    public lssConfig: LssConfig,
    private memberService: MemberService,
    private kc: KeyCloakService
  ) {

  }

  ngOnInit() {
    this.checkMobile();
    this.resizeListener = this.checkMobile.bind(this);
    window.addEventListener('resize', this.resizeListener);
    
    // Load real transaction data
    this.loadTransactionData();
  }

  private loadTransactionData() {
    this.chartLoading = true;
    
    // Determine how many transactions to fetch based on period
    const limit = this.selectedPeriod === 'year' ? 500 : 
                  this.selectedPeriod === 'month' ? 200 : 
                  this.selectedPeriod === 'all' ? 1000 : 50;
    
    this.memberService.getTransactionHistory(
      this.kc.lpUniueReference,
      undefined,
      undefined,
      0,
      limit
    ).subscribe({
      next: (transactions) => {
        if (transactions && transactions.length > 0) {
          this.transactions = transactions;
          
          // Log transaction types for analysis
          const uniqueTypes = [...new Set(transactions.map(t => t.transactionType))];
          console.log('Available transaction types:', uniqueTypes);
          
          // Log sample transactions to understand data structure
          console.log('Sample transactions:', transactions.slice(0, 5));
          
          // Log transaction type distribution
          const typeCount = transactions.reduce((acc, t) => {
            acc[t.transactionType || 'unknown'] = (acc[t.transactionType || 'unknown'] || 0) + 1;
            return acc;
          }, {} as Record<string, number>);
          console.log('Transaction type distribution:', typeCount);
          
          this.processTransactionsForChart();
        } else {
          // If no transactions, show empty chart
          this.initializeEmptyChart();
        }
        this.chartLoading = false;
      },
      error: (error) => {
        console.error('Error loading transactions:', error);
        // Fallback to empty chart on error
        this.initializeEmptyChart();
        this.chartLoading = false;
      }
    });
  }

  // Method to change period filter
  changePeriod(period: string) {
    this.selectedPeriod = period;
    this.loadTransactionData();
  }

  // Method to toggle view mode
  toggleViewMode() {
    this.chartViewMode = this.chartViewMode === 'combined' ? 'detailed' : 'combined';
    this.processTransactionsForChart(); // Reprocess with new mode
  }

  private processTransactionsForChart() {
    const today = new Date();
    let chartData: any[] = [];
    
    // Filter transactions based on selected period
    const filteredTransactions = this.filterTransactionsByPeriod(this.transactions);
    
    if (this.selectedPeriod === 'week') {
      // Show last 7 days
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        const dayName = days[date.getDay()];
        
        if (this.chartViewMode === 'combined') {
          chartData.push({
            date: date,
            label: dayName,
            earned: 0,
            spent: 0
          });
        } else {
          chartData.push({
            date: date,
            label: dayName,
            accrual: 0,
            redemption: 0,
            bonus: 0,
            refund: 0,
            reversal: 0
          });
        }
      }
    } else if (this.selectedPeriod === 'month') {
      // Show last 30 days grouped by week
      for (let i = 3; i >= 0; i--) {
        const weekStart = new Date(today);
        weekStart.setDate(weekStart.getDate() - (i * 7 + 6));
        const weekEnd = new Date(today);
        weekEnd.setDate(weekEnd.getDate() - (i * 7));
        
        if (this.chartViewMode === 'combined') {
          chartData.push({
            dateStart: weekStart,
            dateEnd: weekEnd,
            label: `Week ${4 - i}`,
            earned: 0,
            spent: 0
          });
        } else {
          chartData.push({
            dateStart: weekStart,
            dateEnd: weekEnd,
            label: `Week ${4 - i}`,
            accrual: 0,
            redemption: 0,
            bonus: 0,
            refund: 0,
            reversal: 0
          });
        }
      }
    } else if (this.selectedPeriod === 'year') {
      // Show last 12 months
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      
      for (let i = 11; i >= 0; i--) {
        const date = new Date(today);
        date.setMonth(date.getMonth() - i);
        
        if (this.chartViewMode === 'combined') {
          chartData.push({
            month: date.getMonth(),
            year: date.getFullYear(),
            label: months[date.getMonth()],
            earned: 0,
            spent: 0
          });
        } else {
          chartData.push({
            month: date.getMonth(),
            year: date.getFullYear(),
            label: months[date.getMonth()],
            accrual: 0,
            redemption: 0,
            bonus: 0,
            refund: 0,
            reversal: 0
          });
        }
      }
    } else {
      // 'all' - group by year or last 7 entries
      const sortedTransactions = [...filteredTransactions].sort((a, b) => 
        new Date(b.transactionDate!).getTime() - new Date(a.transactionDate!).getTime()
      );
      
      // Just show last 7 days of activity
      const uniqueDates = [...new Set(sortedTransactions.map(t => 
        new Date(t.transactionDate!).toDateString()
      ))].slice(0, 7);
      
      uniqueDates.reverse().forEach(dateStr => {
        const date = new Date(dateStr);
        if (this.chartViewMode === 'combined') {
          chartData.push({
            date: date,
            label: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
            earned: 0,
            spent: 0
          });
        } else {
          chartData.push({
            date: date,
            label: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
            accrual: 0,
            redemption: 0,
            bonus: 0,
            refund: 0,
            reversal: 0
          });
        }
      });
    }

    // Aggregate transactions into chart data
    filteredTransactions.forEach(transaction => {
      if (transaction.transactionDate) {
        const transDate = new Date(transaction.transactionDate);
        const points = transaction.transactionPoints || 0;
        
        let dataPoint = null;
        
        if (this.selectedPeriod === 'week') {
          dataPoint = chartData.find(d => d.date && d.date.toDateString() === transDate.toDateString());
        } else if (this.selectedPeriod === 'month') {
          dataPoint = chartData.find(d => 
            d.dateStart && d.dateEnd &&
            transDate >= d.dateStart && transDate <= d.dateEnd
          );
        } else if (this.selectedPeriod === 'year') {
          dataPoint = chartData.find(d => 
            d.month === transDate.getMonth() && d.year === transDate.getFullYear()
          );
        } else {
          dataPoint = chartData.find(d => d.date && d.date.toDateString() === transDate.toDateString());
        }
        
        if (dataPoint) {
          const transType = transaction.transactionType;
          const absPoints = Math.abs(points);
          
          if (this.chartViewMode === 'combined') {
            // Combined view: Earned vs Spent
            if (transType === 'Accrual' || transType === 'Bonus' || 
                transType === 'Refund' || transType === 'RefundRedemption' ||
                (transType !== 'Redemption' && points > 0)) {
              dataPoint.earned += absPoints;
            } else if (transType === 'Redemption' || points < 0) {
              dataPoint.spent += absPoints;
            }
          } else {
            // Detailed view: Show each transaction type separately
            switch(transType) {
              case 'Accrual':
                dataPoint.accrual = (dataPoint.accrual || 0) + absPoints;
                break;
              case 'Redemption':
                dataPoint.redemption = (dataPoint.redemption || 0) + absPoints;
                break;
              case 'Bonus':
                dataPoint.bonus = (dataPoint.bonus || 0) + absPoints;
                break;
              case 'Refund':
              case 'RefundRedemption':
                dataPoint.refund = (dataPoint.refund || 0) + absPoints;
                break;
              case 'Reversal':
              case 'Void':
              case 'Cancel':
                dataPoint.reversal = (dataPoint.reversal || 0) + absPoints;
                break;
              default:
                // Handle any other transaction types
                if (points > 0) {
                  dataPoint.accrual = (dataPoint.accrual || 0) + absPoints;
                } else {
                  dataPoint.redemption = (dataPoint.redemption || 0) + absPoints;
                }
            }
          }
        }
      }
    });

    this.transactionData = chartData;
    
    // Log the processed chart data for debugging
    console.log('Processed chart data:', chartData);
    console.log('Chart data summary:', chartData.map(d => ({
      label: d.label,
      earned: d.earned,
      spent: d.spent
    })));
  }

  private filterTransactionsByPeriod(transactions: Statement[]): Statement[] {
    const today = new Date();
    const startDate = new Date();
    
    switch(this.selectedPeriod) {
      case 'week':
        startDate.setDate(today.getDate() - 7);
        break;
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'year':
        startDate.setFullYear(today.getFullYear() - 1);
        break;
      case 'all':
        return transactions; // Return all transactions
    }
    
    return transactions.filter(t => {
      if (!t.transactionDate) return false;
      const transDate = new Date(t.transactionDate);
      return transDate >= startDate && transDate <= today;
    });
  }

  private initializeEmptyChart() {
    // Initialize empty chart data for last 7 days
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const today = new Date();
    const last7Days: any[] = [];
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dayName = days[date.getDay()];
      
      last7Days.push({
        label: dayName,
        earned: 0,
        spent: 0
      });
    }
    
    this.transactionData = last7Days;
  }

  ngOnDestroy() {
    if (this.resizeListener) {
      window.removeEventListener('resize', this.resizeListener);
    }
  }

  private checkMobile() {
    this.isMobile = window.innerWidth < 768;
  }

  // Format currency with thousand separators
  formatCurrency(value: number | undefined | null): string {
    if (!value && value !== 0) return '0.00';
    
    // Format with 2 decimal places and thousand separators
    return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  // Format number with thousand separators
  formatNumber(value: number | undefined | null): string {
    if (!value && value !== 0) return '0';
    
    // Format with thousand separators
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  // Navigate with animation
  navigateWithAnimation(route: string, cardType: string) {
    this.animatingCard = cardType;
    
    // Wait for animation to complete before navigating
    setTimeout(() => {
      this.router.navigate([route]);
      this.animatingCard = null;
    }, 600); // Match animation duration
  }

  // Calculate dynamic font size based on value length
  calculateFontSize(value: string): string {
    const length = value.length;
    let fontSize = 28; // Default size
    
    if (length <= 8) {
      fontSize = 28;
    } else if (length <= 10) {
      fontSize = 26;
    } else if (length <= 12) {
      fontSize = 24;
    } else if (length <= 14) {
      fontSize = 22;
    } else {
      fontSize = 20;
    }
    
    return `${fontSize}px`;
  }

  // Get current date formatted
  getCurrentDate(): string {
    const date = new Date();
    const options: Intl.DateTimeFormatOptions = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    return date.toLocaleDateString('en-US', options);
  }

  // Get member tier based on balance
  getMemberTier(): string {
    const balance = this.profile?.currentBalance || 0;
    if (balance >= 100000) return 'Platinum';
    if (balance >= 50000) return 'Gold';
    if (balance >= 20000) return 'Silver';
    return 'Bronze';
  }

  // Calculate days as member
  getDaysAsMember(): string {
    // Using a placeholder date for now
    const registrationDate = new Date(2024, 0, 1); // Placeholder
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - registrationDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays.toString();
  }

  // Transaction chart methods
  getTransactionData() {
    return this.transactionData;
  }

  getTotalEarned(): string {
    const total = this.transactionData.reduce((sum, day) => sum + day.earned, 0);
    return this.formatNumber(total);
  }

  getTotalSpent(): string {
    const total = this.transactionData.reduce((sum, day) => sum + day.spent, 0);
    return this.formatNumber(total);
  }

  getNetChange(): number {
    const earned = this.transactionData.reduce((sum, day) => sum + day.earned, 0);
    const spent = this.transactionData.reduce((sum, day) => sum + day.spent, 0);
    return earned - spent;
  }

  getChartHeight(value: number): number {
    if (!value || value === 0) return 5; // Minimum height for empty bars
    
    // Find max value for scaling based on view mode
    let maxValue = 100; // Default minimum
    
    if (this.chartViewMode === 'combined') {
      maxValue = Math.max(
        ...this.transactionData.map(d => Math.max(d.earned || 0, d.spent || 0)),
        100
      );
    } else {
      // For detailed view, find max across all transaction types
      maxValue = Math.max(
        ...this.transactionData.map(d => Math.max(
          d.accrual || 0,
          d.redemption || 0,
          d.bonus || 0,
          d.refund || 0,
          d.reversal || 0
        )),
        100
      );
    }
    
    // Scale to percentage (5-70% of container height)
    const height = Math.min(70, Math.max(5, (value / maxValue) * 70));
    
    return height;
  }

  // Get period display text
  getPeriodText(): string {
    switch(this.selectedPeriod) {
      case 'week': return 'Last 7 Days';
      case 'month': return 'Last 30 Days';
      case 'year': return 'Last 12 Months';
      case 'all': return 'All Time';
      default: return 'Last 7 Days';
    }
  }

  // Simplified helper methods for clean desktop display
}
