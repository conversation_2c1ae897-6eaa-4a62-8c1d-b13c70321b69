<ion-content class="mc-notifications">
  <div class="notifications-header" *ngIf="items?.length">
    <h1 class="page-title">Notifications</h1>
    <p class="page-subtitle">Stay updated with your latest activities</p>
  </div>

  <div class="empty-state" *ngIf="!items || items.length === 0">
    <div class="empty-icon-wrapper">
      <ion-icon [name]="emptyIconName" class="empty-icon"></ion-icon>
    </div>
    <h3 class="empty-title">{{ emptyTitle }}</h3>
    <p class="empty-subtitle">{{ emptySubtitle }}</p>
  </div>

  <div *ngIf="items?.length" class="notifications-list">
    <lib-ui-card *ngFor="let noti of items; let i = index"
                 class="notification-card"
                 [class.expanded]="isExpanded(noti.noteSeq)"
                 (click)="toggle(noti.noteSeq)"
                 [style.animation-delay]="(i * 0.07) + 's'">
      <div class="notification-header">
        <div class="notification-icon">
          <ion-icon [name]="iconName"></ion-icon>
        </div>
        <div class="notification-info">
          <h4 class="notification-title">{{ noti.shortDescription }}</h4>
          <p class="notification-time">Just now</p>
        </div>
        <ion-icon name="chevron-down-outline"
                  class="expand-icon"
                  [class.expanded]="isExpanded(noti.noteSeq)"></ion-icon>
      </div>

      <div class="notification-content" [class.show]="isExpanded(noti.noteSeq)">
        <p class="notification-message">{{ getDisplayMessage(noti.message) }}</p>
        <button libUiButton variant="primary" size="sm" class="mark-read-button"
                (click)="onMarkRead(noti.noteSeq, $event)">
          <ion-icon name="checkmark-circle-outline"></ion-icon>
          Mark as read
        </button>
      </div>
    </lib-ui-card>
  </div>
</ion-content>
