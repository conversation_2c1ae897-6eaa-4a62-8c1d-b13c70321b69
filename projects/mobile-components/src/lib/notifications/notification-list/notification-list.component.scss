.mc-notifications {
  --background: #f8f9fa;
}

.notifications-header {
  background: linear-gradient(135deg, var(--ion-color-primary, #FF6B35) 0%, #ff8659 100%);
  padding: 24px 16px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);

  .page-title { font-size: 22px; font-weight: 700; color: white; margin: 0 0 6px 0; }
  .page-subtitle { font-size: 14px; color: rgba(255, 255, 255, 0.9); margin: 0; }
}

.empty-state {
  display: flex; flex-direction: column; align-items: center; justify-content: center;
  padding: 48px 16px; min-height: 40vh;

  .empty-icon-wrapper {
    width: 96px; height: 96px; background: linear-gradient(135deg, #f0f3f6 0%, #e9ecef 100%);
    border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 16px;
    .empty-icon { font-size: 48px; color: #adb5bd; }
  }
  .empty-title { font-size: 20px; font-weight: 600; color: #343a40; margin: 0 0 8px 0; }
  .empty-subtitle { font-size: 14px; color: #6c757d; text-align: center; margin: 0; max-width: 280px; }
}

.notifications-list { padding: 16px; padding-bottom: 64px; }

.notification-card { display: block; background: white; border-radius: 16px; margin-bottom: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.08); overflow: hidden; transition: all .2s ease; }
.notification-card:hover { box-shadow: 0 4px 20px rgba(0,0,0,0.12); transform: translateY(-1px); }

.notification-header { display: flex; align-items: center; padding: 14px; }
.notification-icon { width: 44px; height: 44px; background: rgba(255,107,53,.08); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 12px; flex-shrink: 0; }
.notification-icon ion-icon { font-size: 22px; color: var(--ion-color-primary, #FF6B35); }
.notification-info { flex: 1; }
.notification-title { font-size: 15px; font-weight: 600; color: #1a1a1a; margin: 0 0 2px 0; }
.notification-time { font-size: 12px; color: #6c757d; margin: 0; }
.expand-icon { font-size: 18px; color: #6c757d; transition: transform .2s ease; }
.expand-icon.expanded { transform: rotate(180deg); }

.notification-content { max-height: 0; overflow: hidden; transition: max-height .25s ease; }
.notification-content.show { max-height: 260px; }
.notification-message { padding: 0 14px 12px 70px; color: #495057; font-size: 14px; line-height: 1.5; margin: 0; }
.mark-read-button { margin-left: 56px; margin-bottom: 12px; display: inline-flex; align-items: center; gap: 6px; }

@media (max-width: 380px) {
  .notifications-header { padding: 20px 12px; }
  .notification-message { padding-left: 60px; }
  .mark-read-button { margin-left: 48px; }
}
.mc-notifications { --background: #f8f9fa; }

.notifications-header {  background: linear-gradient(135deg, var(--ion-color-primary, #FF6B35) 0%, #ff8659 100%);  padding: 24px 16px;  text-align: center;  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1); }
.page-title {  font-size: 20px;  font-weight: 700;  color: white;  margin: 0 0 4px 0; }
.page-subtitle {  font-size: 14px;  color: rgba(255,255,255,0.9);  margin: 0; }

.empty-state {  display: flex;  flex-direction: column;  align-items: center;  justify-content: center;  padding: 40px 16px;  min-height: 50vh; }
.empty-icon-wrapper {  width: 96px;  height: 96px;  background: linear-gradient(135deg, #f0f3f6 0%, #e9ecef 100%);  border-radius: 50%;  display: flex;  align-items: center;  justify-content: center;  margin-bottom: 16px; }
.empty-icon {  font-size: 48px;  color: #adb5bd; }
.empty-title {  font-size: 20px;  font-weight: 600;  color: #343a40;  margin: 0 0 8px 0; }
.empty-subtitle {  font-size: 14px;  color: #6c757d;  text-align: center;  margin: 0;  max-width: 280px; }

.notifications-list {  padding: 16px;  padding-bottom: 64px; }
.notification-card {  display: block; }
.notification-header {  display: flex;  align-items: center;  padding: 16px;  cursor: pointer; }
.notification-icon {  width: 40px;  height: 40px;  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 134, 89, 0.1) 100%);  border-radius: 12px;  display: flex;  align-items: center;  justify-content: center;  margin-right: 16px;  flex-shrink: 0; }
.notification-icon ion-icon {  font-size: 22px;  color: var(--ion-color-primary, #FF6B35); }
.notification-info {  flex: 1; }
.notification-title {  font-size: 16px;  font-weight: 600;  color: #1a1a1a;  margin: 0 0 4px 0; }
.notification-time {  font-size: 13px;  color: #6c757d;  margin: 0; }
.expand-icon {  font-size: 20px;  color: #6c757d;  transition: transform 0.2s ease; }
.expand-icon.expanded {  transform: rotate(180deg); }
.notification-content {  max-height: 0;  overflow: hidden;  transition: max-height 0.25s ease; }
.notification-content.show {  max-height: 300px; }
.notification-message {  padding: 0 16px 16px 72px;  color: #495057;  font-size: 15px;  line-height: 1.6;  margin: 0; }
.mark-read-button {  margin-left: 56px;  margin-bottom: 16px;  display: inline-flex;  gap: 6px;  align-items: center; }
