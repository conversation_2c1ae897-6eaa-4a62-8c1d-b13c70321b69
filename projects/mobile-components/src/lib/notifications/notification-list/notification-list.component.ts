import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { UiCardComponent } from '../../ui-card/ui-card.component';
import { UiButtonDirective } from '../../ui-button/ui-button.directive';

export interface NotificationItemModel {
  noteSeq: number | string;
  shortDescription: string;
  message: string;
}

@Component({
  selector: 'lib-notification-list',
  standalone: true,
  imports: [CommonModule, IonicModule, UiCardComponent, UiButtonDirective],
  templateUrl: './notification-list.component.html',
  styleUrls: ['./notification-list.component.scss']
})
export class NotificationListComponent {
  @Input() items: NotificationItemModel[] = [];
  @Input() emptyTitle = 'No Notifications';
  @Input() emptySubtitle = "You're all caught up! Check back later for updates.";
  @Input() iconName = 'notifications-outline';
  @Input() emptyIconName = 'notifications-off-outline';

  @Output() markRead = new EventEmitter<number | string>();
  @Output() toggled = new EventEmitter<number | string>();

  // Track expansion state internally to avoid mutating input
  expanded = new Set<number | string>();

  isExpanded(id: number | string): boolean {
    return this.expanded.has(id);
  }

  toggle(id: number | string) {
    if (this.expanded.has(id)) {
      this.expanded.delete(id);
    } else {
      this.expanded.add(id);
    }
    this.toggled.emit(id);
  }

  onMarkRead(id: number | string, event?: Event) {
    if (event) {
      event.stopPropagation();
    }
    this.markRead.emit(id);
  }

  // Helper to extract message after a backslash as in existing template
  getDisplayMessage(msg: string): string {
    const parts = msg.split('\\\\');
    return parts[1] || msg;
  }
}
