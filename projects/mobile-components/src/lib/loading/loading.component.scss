.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;

  .loader{
    font-size: 2rem;
    color: var(--ion-color-primary);
    display: inline-block;
    font-weight: 400;
    position: relative;
  }
  .loader:after{
    content: '';
    margin-top: 4px;
    height: 6px;
    width:0%;
    display: block;
    background: var(--ion-color-primary);
    animation: 5s lineGrow linear infinite;
  }
  
  @keyframes lineGrow {to{width: 100%;}}
  
}