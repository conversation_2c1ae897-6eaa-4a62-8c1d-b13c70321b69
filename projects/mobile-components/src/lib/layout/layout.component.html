
<ion-item>
    <ion-select class="routes" [(ngModel)]="environment.lssConfig.theme.layout" label="Theme" label-placement="floating">
      <ion-select-option value="sidebar">Sidebar</ion-select-option>
      <ion-select-option value="tabs">Tabs</ion-select-option>
    </ion-select>
  </ion-item>
  <ion-item>
    <ion-input labelPlacement="floating" label="T&C"  [value]="environment.lssConfig.termsConditions" />

  </ion-item>
<ion-item>
    <ion-label class="routes">Sidebar</ion-label>

</ion-item>
<ion-item>
    <ion-input labelPlacement="floating" label="Title"  [value]="environment.lssConfig.navigation.sidebarTitle" />

  </ion-item>
  <ion-item>
    <ion-input labelPlacement="floating" label="Icon"  [value]="environment.lssConfig.navigation.sidebarIcon" />

  </ion-item>
    <ion-row *ngFor="let nv of environment.lssConfig.navigation.routes" class="routes">
        <ion-checkbox class="primary" labelPlacement="end" [(ngModel)]="nv.sidebar"  [value]="nv.active" style="color: black !important" >{{nv.id}}</ion-checkbox>
        <br />
    
    </ion-row>




