import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'lib-ui-card',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="ui-card lib-ui-card-inner" 
         [class.card-sm]="size === 'sm'"
         [class.card-lg]="size === 'lg'"
         [class.card-flat]="variant === 'flat'"
         [class.card-elevated]="variant === 'elevated'"
         [class.card-clickable]="clickable"
         [routerLink]="routerLink"
         [style.cursor]="clickable || routerLink ? 'pointer' : 'default'">
      <ng-content></ng-content>
    </div>
  `,
  styleUrls: ['./ui-card.component.scss'],
  host: {
    'class': 'lib-ui-card-host'
  }
})
export class UiCardComponent {
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() variant: 'default' | 'flat' | 'elevated' = 'default';
  @Input() clickable: boolean = false;
  @Input() routerLink: any[] | string | null = null;
}