@import '../../../../lp-client/src/styles/design-system/variables';
@import '../../../../lp-client/src/styles/design-system/mixins';

// Host element styling - simplified
:host {
  display: block;
  // Don't set width here, let parent control it
}

.ui-card {
  @include card;
  display: block;
  box-sizing: border-box;
  height: 100%; // Fill height of grid cell
  
  // Size variants
  &.card-sm {
    padding: $spacing-sm;
    border-radius: $radius-md;
    
    @include tablet {
      padding: $spacing-md;
    }
    
    @include desktop {
      padding: $spacing-md;
    }
  }
  
  &.card-lg {
    padding: $spacing-lg;
    border-radius: $radius-xl;
    
    @include tablet {
      padding: $spacing-xl;
      border-radius: $radius-2xl;
    }
    
    @include desktop {
      padding: $spacing-2xl;
    }
  }
  
  // Style variants
  &.card-flat {
    box-shadow: none;
    border: 1px solid rgba(0, 0, 0, 0.08);
    
    @include desktop {
      &:hover {
        transform: none;
        box-shadow: none;
        border-color: rgba(0, 0, 0, 0.12);
      }
    }
  }
  
  &.card-elevated {
    box-shadow: $shadow-lg;
    
    @include desktop {
      &:hover {
        box-shadow: $shadow-xl;
        transform: translateY(-4px);
      }
    }
  }
  
  // Clickable variant
  &.card-clickable {
    cursor: pointer;
    
    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }
  }
}