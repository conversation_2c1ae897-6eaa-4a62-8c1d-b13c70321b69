<ion-fab slot="fixed" vertical="bottom" horizontal="end">
  <ion-fab-button size="small" (click)="setOpen(true)">
    <ion-icon name="create-outline"></ion-icon>
  </ion-fab-button>
</ion-fab>

<ion-modal [isOpen]="isModalOpen">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-title>Modal</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="setOpen(false)">Close</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content>
      <ion-accordion-group [value]="'first'">
        <!--  <ion-accordion value="first">
          <ion-item slot="header" color="light">
            <ion-label>Info</ion-label>
          </ion-item>
          <div class="ion-padding" slot="content">
            <lib-info />
          </div>
        </ion-accordion>
      
        <ion-accordion value="second">
          <ion-item slot="header" color="light">
            <ion-label>Layout</ion-label>
          </ion-item>
          <div class="ion-padding" slot="content">
            <lib-layout />
          </div>
        </ion-accordion>

        <ion-accordion value="fourth">
          <ion-item slot="header" color="light">
            <ion-label>Contact/Social Media</ion-label>
          </ion-item>
          <div class="ion-padding" slot="content">
            <lib-contact />
          </div>
        </ion-accordion> -->
        <ion-accordion value="third">
          <ion-item slot="header" color="light">
            <ion-label>Colours</ion-label>
          </ion-item>
          <div class="ion-padding" slot="content">
            <lib-colours />
          </div>
        </ion-accordion>
        <ion-accordion value="fourth">
          <ion-item slot="header" color="light">
            <ion-label>Social Media</ion-label>
          </ion-item>
          <div class="ion-padding" slot="content">
            <socials-customizer />
          </div>
        </ion-accordion>

        <ion-accordion value="fifth">
          <ion-item slot="header" color="light">
            <ion-label>Pages</ion-label>
          </ion-item>
          <div class="ion-padding" slot="content">
            <pages-customizer />
          </div>
        </ion-accordion>
      </ion-accordion-group>
    </ion-content>
  </ng-template>
</ion-modal>
