.customizer-icon {
color: red;
}

    .primary { --background: var(--ion-color-primary); --color: var(--ion-color-primary-contrast)}
    .primaryContrast { --background: var(--ion-color-primary-contrast); --color: var(--ion-color-primary)}
    .primaryShade { --background: var(--ion-color-primary-shade); --color: var(--ion-color-primary-contrast)}
    .primaryTint { --background: var(--ion-color-primary-tint); --color: var(--ion-color-primary-contrast)}
    .secondary { --background: var(--ion-color-secondary); --color: var(--ion-color-secondary-contrast)}
    .secondaryContrast { --background: var(--ion-color-secondary-contrast); --color: var(--ion-color-secondary)}
    .secondaryShade { --background: var(--ion-color-secondary-shade); --color: var(--ion-color-secondary-contrast)}
    .secondaryTint { --background: var(--ion-color-secondary-tint); --color: var(--ion-color-secondary-contrast)}
    .tertiary { --background: var(--ion-color-tertiary); --color: var(--ion-color-tertiary-contrast)}
    .tertiaryContrast { --background: var(--ion-color-tertiary-contrast); --color: var(--ion-color-tertiary)}
    .tertiaryShade { --background: var(--ion-color-tertiary-shade); --color: var(--ion-color-tertiary-contrast)}
    .tertiaryTint { --background: var(--ion-color-tertiary-tint); --color: var(--ion-color-tertiary-contrast)}
    .success { --background: var(--ion-color-success); --color: var(--ion-color-success-contrast)}
    .successContrast { --background: var(--ion-color-success-contrast); --color: var(--ion-color-success)}
    .successShade { --background: var(--ion-color-success-shade); --color: var(--ion-color-success-contrast)}
    .successTint { --background: var(--ion-color-success-tint); --color: var(--ion-color-success-contrast)}
    .warning { --background: var(--ion-color-warning); --color: var(--ion-color-warning-contrast)}
    .warningContrast { --background: var(--ion-color-warning-contrast); --color: var(--ion-color-warning)}
    .warningShade { --background: var(--ion-color-warning-shade); --color: var(--ion-color-warning-contrast)}
    .warningTint { --background: var(--ion-color-warning-tint); --color: var(--ion-color-warning-contrast)}
    .danger { --background: var(--ion-color-danger); --color: var(--ion-color-danger-contrast)}
    .dangerContrast { --background: var(--ion-color-danger-contrast); --color: var(--ion-color-danger)}
    .dangerShade { --background: var(--ion-color-danger-shade); --color: var(--ion-color-danger-contrast)}
    .dangerTint { --background: var(--ion-color-danger-tint); --color: var(--ion-color-danger-contrast)}
    .medium { --background: var(--ion-color-medium); --color: var(--ion-color-medium-contrast)}
    .mediumContrast { --background: var(--ion-color-medium-contrast); --color: var(--ion-color-medium)}
    .mediumShade { --background: var(--ion-color-medium-shade); --color: var(--ion-color-medium-contrast)}
    .mediumTint { --background: var(--ion-color-medium-tint); --color: var(--ion-color-medium-contrast)}
    .base { --background: var(--ion-color-base); --color: var(--ion-color-base-contrast)}
    .baseContrast { --background: var(--ion-color-base-contrast); --color: var(--ion-color-base)}
    .baseShade { --background: var(--ion-color-base-shade); --color: var(--ion-color-base-contrast)}
    .baseTint { --background: var(--ion-color-base-tint); --color: var(--ion-color-base-contrast)}
    .light { --background: var(--ion-color-light); --color: var(--ion-color-light-contrast)}
    .lightContrast { --background: var(--ion-color-light-contrast); --color: var(--ion-color-light)}
    .lightShade { --background: var(--ion-color-light-shade); --color: var(--ion-color-light-contrast)}
    .lightTint { --background: var(--ion-color-light-tint); --color: var(--ion-color-light-contrast)}