import { Address, Telephone } from './member';
import { MemberCommObject } from './system';

export interface PartnerDetails {
  calcMethod: string;
  name: string;
  partnerId: string;
  partnerType: string;
  productDescription: string;
  productId: string;
}

export class Partner {
  constructor(data : {partnerId: string, partnerName: string, address?: string, telephone?: string, operatingHours?: string, partnerMore?: string,
  }){
    this.partnerId = data.partnerId;
    this.partnerName= data.partnerName;
    this.address = data.address?JSON.parse(data.address):undefined;
    this.telephone = data.telephone?JSON.parse(data.telephone):undefined;
    this.operatingHours = data.operatingHours?JSON.parse(data.operatingHours):undefined;
    this.partnerMore = data.partnerMore?JSON.parse(data.partnerMore):undefined;
  }
  partnerId?: string;
  partnerName?: string;
  address?: Address[];
  telephone?: Telephone[];
  operatingHours?: OperatingHours[];
  partnerMore?: PartnerMore[];
  getAddressByType(adrType: string): Address | undefined{
    const ret = this.address?.filter(item => item.addressType === adrType);
    if (ret && ret.length > 0){
      return ret[0];
    }
    return undefined;
  }
  
  getTelephoneByType(telType: string): Telephone | undefined{
    const ret = this.telephone?.filter(item => item.telephoneType === telType);
    if (ret && ret.length > 0){
      return ret[0];
    }
    return undefined;
  }
  getPartnerMoreByType(moreType: string): PartnerMore | undefined{
    const ret = this.partnerMore?.filter(item => item.itemKey === moreType);
    if (ret && ret.length > 0){
      return ret[0];
    }
    return undefined;
  }
}

export interface PartnerListRequest {
  memberCommObject: MemberCommObject;
  offset: number;
  limit: number;
}

export interface PartnerProductListRequest {
  memberCommObject: MemberCommObject;
  partnerId: string;
  productClass?: string;
  productCode?: string;
  filterString?: string;
}

export interface PartnerProduct {
  productCode?: string;
  productName?: string;
  productClassCode?: string;
  productClassName: string;
  partnerId?: string;
  partnerName?: string;
}

export interface OperatingHours {
  dayOfWeek: string;
  description: string;
}

export interface PartnerMore {
  itemKey: string;
  value: string;
}
