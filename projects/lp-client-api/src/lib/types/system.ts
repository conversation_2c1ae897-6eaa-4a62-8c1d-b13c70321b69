export interface CodeItem {
  value: string;
  label: string;
}

export interface CountryItem {
  code: string;
  country: string;
  productId: string;
  dialcode: string;
  isoCode: string;
}

export interface PageResponse {
  offset: number;
  limit: number;
  total: number;
}

export interface CodeResponse extends PageResponse {
  data: CodeItem[];
}

export interface CountryResponse extends PageResponse {
  data: CountryItem[];
}

export interface MemberCommObject {
  sessionId?: string;
  membershipNumber?: string;
  uniqueId?: string;
  userId?: string;
  password?: string;
  newPassword?: string;
  memberPreferredLanguage?: string;
  pin?: string;
  productId?: string;
  communityId?: string;
  apiId?: string;
  terminalId?: string;
}

export interface Terminal {
  pin: string;
  apiUrl: string;
  clientId: string;
  productId: string;
  partnerId: string;
  partnerName: string;
  terminalId: string;
  deviceId: string;
  status: string;
  endDate: Date;
  floorLimit: number;
  calculationMethod: string;
  allowAccrual: boolean;
  allowRedemption: boolean;
  allowBalance: boolean;
  allowRegistration: boolean;
  otpRequired: boolean;
  pinRequired: boolean;
  themeColor?: string;
  logo?: string;
  logobase64?: string;
  slipLogo?: string;
  slipLogobase64?: string;
  apiIdRaw: string;
  apiId?: ApiId;
  expiryDate: Date;
  memberAccrualSlipRaw?: string;
  memberAccrualSlip?: WAPSlipConfig[];
  memberRedeemSlipRaw?: string;
  memberRedeemSlip?: WAPSlipConfig[];
  partnerAccrualSlipRaw?: string;
  partnerAccrualSlip?: WAPSlipConfig[];
  partnerRedeemSlipRaw?: string;
  partnerRedeemSlip?: WAPSlipConfig[];
  memberGiftCardSlipRaw?: string;
  memberGiftCardSlip?: WAPSlipConfig[];
  partnerGiftCardSlipRaw?: string;
  partnerGiftCardSlip?: WAPSlipConfig[];
  slips: SlipConfig[];
}

export interface ApiId {
  apiId: string;
  secretStart: string;
  secretEnd: string;
  userId?: string;
}

export interface SlipConfig {
  slipType: string;
  slipFunction: string;
  slipData: string;
}

export interface WAPSlipConfig {
  type: string;
  value: string;
  fontSize?: number;
  align?: string;
  isBold?: boolean;
  isItalic?: boolean;
  height?: number;
  width?: number;
  marginSize?: number;
}

export interface WAPSlipData {
  timeStamp?: string;
  logo?: string;
  partnerName?: string;
  invoiceNumber?: string;
  authCode?: string;
  amount?: number;
  quantity?: number;
  pcuMovement?: number;
  cardNumber?: string;
  firstName?: string;
  lastName?: string;
  originalBalance?: number;
  balance?: number;
  currencyBalance?: number;
  attendant?: string;
  attendantNumber?: string;
  expiry?: string;
}

export interface Slip {
  slipId?: number;
  transactionType?: string;
  invoiceNumber?: string;
  printDate?: Date;
  slipRaw?: string;
  slip?: WAPSlipData;
}

export interface LPCode {
  codeId: string;
  abrId: string;
  description: string;
}

export interface LPCodeGroup {
  groupId: string;
  groupDescription: string;
  codeItem: LPCode[];
}

export interface LPCodeGroupResponse {
  responseCode: string;
  responseMessage: string;
  codeGroupDetails: LPCodeGroup;
}

export interface DeviceDetails {
  appVersion: string;
  appId: string;
  confVersion: string;
  operatingSystem: string;
  osVersion: string;
  platform: string;
  webViewVersion: string;
  deviceId: string;
  languageCode: string;
}
