﻿import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, map, of } from 'rxjs';
import { LogEntry } from './log.service';
import { LogPublisher } from './log-publishers';

export class LogWebApi extends LogPublisher {
  constructor(private http: HttpClient) {
    super();
    this.location = '/api/logging';
  }

  // **************
  // Public Methods
  // **************

  // Add log entry to back end data store
  log(entry: LogEntry): Observable<boolean> {
    // TODO: Call Web API to log entry
    return of(true);
  }

  // Clear all log entries from local storage
  clear(): Observable<boolean> {
    // TODO: Call Web API to clear all values
    return of(true);
  }
}
