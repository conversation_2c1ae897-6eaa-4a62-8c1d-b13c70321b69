﻿import { Observable } from 'rxjs';
import { LogEntry } from './log.service';

// ****************************************************
// Log Publisher Abstract Class
// NOTE: This class must be located BEFORE
//       all those that extend this class
// ****************************************************
export abstract class LogPublisher {
  location: string = '';

  abstract log(record: LogEntry): Observable<boolean>;
  abstract clear(): Observable<boolean>;
}
