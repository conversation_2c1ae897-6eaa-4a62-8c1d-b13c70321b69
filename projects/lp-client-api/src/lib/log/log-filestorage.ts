﻿import { Observable, of } from 'rxjs';
import { LogEntry } from './log.service';
import { LogPublisher } from './log-publishers';
import { formatDate } from '@angular/common';
import {
  Directory,
  Encoding,
  Filesystem,
  ReaddirResult,
} from '@capacitor/filesystem';

export class LogFileStorage extends LogPublisher {
  fileLocation: string = '';

  constructor(fileLocation: string) {
    super();
    this.fileLocation = fileLocation;
  }
  log(entry: LogEntry): Observable<boolean> {
    if (
      this.fileLocation === undefined ||
      this.fileLocation === null ||
      this.fileLocation.length === 0
    ) {
      console.error('File location not set for file logger');
      return of(false);
    }

    // Log to file
    this.writeToFile(entry);

    return of(true);
  }

  clear(): Observable<boolean> {
    console.clear();

    return of(true);
  }

  // Write the given log entry to the file as specified in the publisher config
  private async writeToFile(entry: LogEntry) {
    await this.createLogDirectory();
    let fileDate: string = formatDate(new Date(), 'yyyy-MM-dd', 'en');
    let fileName: string = `${this.fileLocation}/log-${fileDate}.txt`;

    let logData: any = await Filesystem.readFile({
      path: fileName,
      directory: Directory.Documents,
      encoding: Encoding.UTF8,
    })
      .then((result) => {
        return result.data;
      })
      .catch((error) => {
        console.error('Log file does not exist', error);
        return '';
      });

    logData = logData + '\n' + entry.buildLogString();

    await Filesystem.writeFile({
      path: fileName,
      data: logData,
      directory: Directory.Documents,
      encoding: Encoding.UTF8,
    });
  }

  /**
   * Create log directory
   */
  private async createLogDirectory() {
    let path: string = 'aero.loyaltyplus.app.terminal';
    let result: ReaddirResult = await Filesystem.readdir({
      path: path,
      directory: Directory.Documents,
    });

    if (!result.files) {
      await Filesystem.mkdir({
        path: path + '/log',
        recursive: true,
        directory: Directory.Documents,
      }).catch((error) => {
        console.error(error);
      });
    }
  }
}
