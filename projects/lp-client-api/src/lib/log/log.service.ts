﻿import { Inject, Injectable } from '@angular/core';
import { LogPublishersService } from './log-publishers.service';
import { LogPublisher } from './log-publishers';
import { formatDate } from '@angular/common';

// ****************************************************
// Log Level Enumeration
// ****************************************************
export enum LogLevel {
  All = 0,
  Debug = 1,
  Info = 2,
  Warn = 3,
  Error = 4,
  Fatal = 5,
  Off = 6,
}

// ****************************************************
// Log Entry Class
// ****************************************************
export class LogEntry {
  // Public Properties
  method: string = '';
  message: string = '';
  level: LogLevel = LogLevel.Debug;
  extraInfo: any[] = [];

  // **************
  // Public Methods
  // **************
  buildLogString(): string {
    let value: string = '';

    value = formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss', 'en') + ' ';
    value += LogLevel[this.level].toUpperCase() + ' ';
    value += '[' + this.method + '] ';
    value += this.message + ' ';
    if (this.extraInfo.length) {
      value += this.formatParams(this.extraInfo) + ' ';
    }

    return value;
  }

  // ***************
  // Private Methods
  // ***************
  private formatParams(params: any[]): string {
    let ret: string = params.join(',');

    // Is there at least one object in the array?
    if (params.some((p) => typeof p == 'object')) {
      ret = '';
      // Build comma-delimited string
      for (let item of params) {
        ret += JSON.stringify(item) + ',';
      }
    }

    return ret;
  }
}

// ****************************************************
// Log Service Class
// ****************************************************
@Injectable()
export class LogService {
  constructor(
    private publishersService: LogPublishersService,
    @Inject('environment')
    private environment: any
  ) {
    // Set publishers
    this.publishers = this.publishersService.publishers;
  }

  // Public Properties
  publishers: LogPublisher[];
  level: LogLevel = LogLevel.Error;

  // *************************
  // Public methods
  // *************************
  debug(method: string, msg: string, ...optionalParams: any[]) {
    this.writeToLog(method, msg, LogLevel.Debug, optionalParams);
  }

  info(method: string, msg: string, ...optionalParams: any[]) {
    this.writeToLog(method, msg, LogLevel.Info, optionalParams);
  }

  warn(method: string, msg: string, ...optionalParams: any[]) {
    this.writeToLog(method, msg, LogLevel.Warn, optionalParams);
  }

  error(method: string, msg: string, ...optionalParams: any[]) {
    this.writeToLog(method, msg, LogLevel.Error, optionalParams);
  }

  fatal(method: string, msg: string, ...optionalParams: any[]) {
    this.writeToLog(method, msg, LogLevel.Fatal, optionalParams);
  }

  log(method: string, msg: string, ...optionalParams: any[]) {
    this.writeToLog(method, msg, LogLevel.All, optionalParams);
  }

  clear(): void {
    for (let logger of this.publishers) {
      logger.clear().subscribe((response) => console.log(response));
    }
  }

  // *************************
  // Private methods
  // *************************
  private shouldLog(level: LogLevel): boolean {
    this.level =
      this.environment.lssConfig.logConfig.level !== undefined
        ? this.environment.lssConfig.logConfig.level
        : LogLevel.Off;
    let ret: boolean = false;

    if (
      (level >= this.level && level !== LogLevel.Off) ||
      this.level === LogLevel.All
    ) {
      ret = true;
    }

    return ret;
  }

  private writeToLog(
    method: string,
    msg: string,
    level: LogLevel,
    params: any[]
  ) {
    if (this.shouldLog(level)) {
      // Declare variables
      let entry: LogEntry = new LogEntry();

      // Build Log Entry
      entry.method = method;
      entry.message = msg;
      entry.level = level;
      entry.extraInfo = params;

      for (let logger of this.publishers) {
        logger.log(entry).subscribe();
      }
    }
  }
}
