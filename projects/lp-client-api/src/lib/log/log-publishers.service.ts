﻿import { Inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { LogPublisher } from './log-publishers';
import { LogConsole } from './log-console';
import { LogLocalStorage } from './log-localstorage';
import { LogWebApi } from './log-webapi';
import { LogFileStorage } from './log-filestorage';

// ****************************************************
// Log Publisher Config Definition Class
// ****************************************************
export class LogPublisherConfig {
  loggerName?: string;
  loggerLocation?: string;
  isActive?: boolean;
}

// ****************************************************
// Logging Publishers Service Class
// ****************************************************
@Injectable()
export class LogPublishersService {
  constructor(
    private http: HttpClient,
    @Inject('environment')
    private environment: any
  ) {
    // Build publishers arrays
    this.buildPublishers();
  }

  // Public properties
  publishers: LogPublisher[] = [];

  // *************************
  // Public methods
  // *************************
  // Build publishers array
  buildPublishers(): void {
    let logPub: LogPublisher;

    this.getLoggers().subscribe((response) => {
      for (let pub of response.filter((p) => p.isActive)) {
        switch (pub.loggerName!.toLowerCase()) {
          case 'console':
            logPub = new LogConsole();
            break;
          case 'localstorage':
            logPub = new LogLocalStorage();
            break;
          case 'filestorage':
            logPub = new LogFileStorage(pub.loggerLocation!);
            break;
          case 'webapi':
            logPub = new LogWebApi(this.http);
            break;
        }
        // Set location of logging
        logPub.location = pub.loggerLocation!;
        // Add publisher to array
        this.publishers.push(logPub);
      }
    });
  }

  // Get logger configuration info from JSON file
  getLoggers(): Observable<LogPublisherConfig[]> {
    if (
      this.environment.lssConfig.logConfig &&
      this.environment.lssConfig.logConfig.publishers
    ) {
      return of(this.environment.lssConfig.logConfig.publishers);
    } else {
      return of([]);
    }
  }
}
