import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable, of, throwError, timer } from 'rxjs';
import { map, shareReplay, switchMap, catchError, retryWhen, delay, mergeMap, finalize } from 'rxjs/operators';
import { CacheService } from './cache.service';
import { KeyCloakService } from './key-cloak.service';
import { AlertController } from '@ionic/angular';

export enum Verbs {
  GET = 'GET',
  PUT = 'PUT',
  POST = 'POST',
  DELETE = 'DELETE',
}

@Injectable({
  providedIn: 'root',
})
export class HttpClientService {



  
  constructor(private http: HttpClient, private _cacheService: CacheService,
    private kc: KeyCloakService,
    private alertController: AlertController) {}

  get<T>(options: HttpOptions): Observable<T> {
    return this.httpCall(Verbs.GET, options).pipe(
      map((response) => <T>response.body)
    );
  }

  getWithResponse<T>(options: HttpOptions): Observable<HttpResponse<T>> {
    return this.httpCall(Verbs.GET, options);
  }

  delete<T>(options: HttpOptions): Observable<T> {
    return this.httpCall(Verbs.DELETE, options).pipe(
      map((response) => <T>response.body)
    );
  }

  deleteWithResponse<T>(options: HttpOptions): Observable<HttpResponse<T>> {
    return this.httpCall(Verbs.DELETE, options);
  }

  post<T>(options: HttpOptions): Observable<T> {
    return this.httpCall(Verbs.POST, options).pipe(
      map((response) => <T>response.body)
    );
  }

  postWithResponse<T>(options: HttpOptions): Observable<HttpResponse<T>> {
    return this.httpCall(Verbs.POST, options);
  }

  put<T>(options: HttpOptions): Observable<T> {
    return this.httpCall(Verbs.PUT, options).pipe(
      map((response) => <T>response.body)
    );
  }

  putWithResponse<T>(options: HttpOptions): Observable<HttpResponse<T>> {
    return this.httpCall(Verbs.PUT, options);
  }

  private handleError(err:HttpErrorResponse){
      if (err.status === 401){
        console.log('Access error', err);
        if (this.kc.authStatus){
        this.kc.authed(true).then(status => {
          if (!status){
            this.alertController.create({
              header: 'Connection Error',
              subHeader: 'Session expired!',
              message: 'Your session has expired and the system will now log you out.',
              buttons: ['OK'],
            }).then(alertObj => {
              alertObj.onDidDismiss().then(() => {
                this.kc.keycloak?.logout().then(() => {
                  console.log('Auto logout expired session!');
                });
              })
              alertObj.present();
            })
          }
        });
      }
      }else{
        console.log('Handling error locally and rethrowing it...', err);
      }
      return throwError(() => err);
  }

  private httpCall<T>(
    verb: Verbs,
    options: HttpOptions
  ): Observable<HttpResponse<T>> {
    // Setup default values
    options.body = options.body || null;
    options.cacheMins = options.cacheMins || 0;
    options.key = options.key || options.url;
    options.params = options.params || new HttpParams();

    if (options.cacheMins > 0) {
      // Get data from cache
      const data = this._cacheService.load(options.key);
      // Return data from cache
      if (data !== null) {
        //return of<T>(data)
        return data;
      }
    }

    let observeData = this.http.request<T>(verb, options.url, {
      params: options.params,
      body: options.body,
      observe: 'response',
    }).pipe( 
      retryWhen(genericAuthRetryStrategy(this.kc)),
      catchError((err:HttpErrorResponse) => this.handleError(err))
    );
    if (options.cacheMins > 0) {
      observeData = observeData.pipe(shareReplay(1));
      this._cacheService.save({
        key: options.key,
        data: observeData,
        expirationMins: options.cacheMins,
      });
    }
    return observeData;
  }
}

export const genericAuthRetryStrategy = (kc: KeyCloakService,
  {
  maxRetryAttempts = 3,
  scalingDuration = 1000
}: {
  maxRetryAttempts?: number,
  scalingDuration?: number,
  excludedStatusCodes?: number[]
} = {}) => (attempts: Observable<any>) => {
  return attempts.pipe(
    mergeMap((error, i) => {
      const retryAttempt = i + 1;
      // if maximum number of retries have been met
      // or response is a status code we don't wish to retry, throw error
      if (retryAttempt > maxRetryAttempts || (error.status && error.status !== 401)) {
        return throwError(() => error);
      }
      kc.authed(true).then();

      console.log(`Attempt ${retryAttempt}: retrying in ${retryAttempt * scalingDuration}ms`
      );
      // retry after 1s, 2s, etc...
      return timer(retryAttempt * scalingDuration);
    }),
    finalize(() => console.log('We are done!'))
  );
};

export class HttpOptions {
  url!: string;
  key!: string;
  body?: any;
  params?: HttpParams;
  cacheMins?: number;
}
