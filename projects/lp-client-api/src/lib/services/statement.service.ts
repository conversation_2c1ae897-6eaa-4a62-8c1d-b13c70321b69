import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { Statement } from '../types/member';

@Injectable({
  providedIn: 'root'
})
export class StatementService {

  private apiVersion:string='1.0.0';
  private apiUrl:string;

  constructor(private _httpClient: HttpClient) { 
    this.apiUrl = `https://rziaqa.loyaltyplus.aero/extsecure/loyaltyapi/${this.apiVersion}/member/`;
  }

  getTransactionHistory(id: any, beginDate: Date | undefined, endDate: Date | undefined, page: number, pageSize: number) : Observable<Statement[]> {
    return this._httpClient.get<any>(`${this.apiUrl}${id}/statementList?beginDate=${beginDate}&endDate=${endDate}&offset=${page}&limit=${pageSize}`).pipe(
      map((response) => response.statements
    ));
  }

}
