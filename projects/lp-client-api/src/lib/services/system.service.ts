import { HttpParams } from '@angular/common/http';
import { Inject, Injectable, Injector, Type } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { LssConfig } from '../types/lss-config';
import { Md5 } from 'ts-md5';
import {
  CodeItem,
  CodeResponse,
  CountryItem,
  CountryResponse,
  LPCodeGroup,
  LPCodeGroupResponse,
} from '../types/system';
import { HttpClientService } from './http-client.service';

@Injectable({
  providedIn: 'root',
})
export class SystemService {
  private lssConfig: LssConfig;
  private apiVersion: string = '1.0.0';

  constructor(
    private _httpClient: HttpClientService,
    private injector: Injector
  ) {
    this.lssConfig = injector.get<LssConfig>(LssConfig as Type<LssConfig>);
  }

  get systemAPI(): string {
    return `${this.lssConfig.apiBaseUrl}public/loyaltyapi/${this.apiVersion}/system-utils/`;
  }

  /**
   * List all the countries in the system.
   * @param filterString : Filter the data on the remote server based on a like string
   * @param offset : start offset the return
   * @param limit : Total number of records to return.
   * @returns
   */
  listCountries(
    filterString: string,
    offset: number = 0,
    limit: number = 1000
  ): Observable<CountryItem[]> {
    let httpParams = new HttpParams()
      .set('offset', offset + '')
      .set('limit', limit + '')
      .set('filterString', filterString);
    return this._httpClient
      .get<CountryResponse>({
        url: `${this.systemAPI}country`,
        params: httpParams,
        key: `sysapi-country|` + offset + '|' + limit + '|' + filterString,
        cacheMins: 1000,
      })
      .pipe(
        map((mapData: CountryResponse) => {
          console.log('listCountries Done');
          return mapData.data;
        })
      );
  }

  /**
   * Get the details for a specific country code.
   * @param countryCode - Country to Find
   * @returns
   */
  getCountries(countryCode: string): Observable<CountryItem> {
    return this._httpClient.get<CountryItem>({
      url: `${this.systemAPI}country/${countryCode}`,
      cacheMins: 100,
      key: '',
    });
  }

  /**
   *
   * @param filterString
   * @param country - Only return provinces for this country
   * @param offset
   * @param limit
   * @returns
   */
  listProvice(
    filterString: string = '',
    country: string = '',
    offset: number = 0,
    limit: number = 100
  ): Observable<CodeItem[]> {
    let httpParams = new HttpParams()
      .set('offset', offset + '')
      .set('limit', limit + '')
      .set('country', country)
      .set('filterString', filterString);
    return this._httpClient
      .get<CodeResponse>({
        url: `${this.systemAPI}province`,
        key:
          `sysapi-province|` +
          offset +
          '|' +
          limit +
          '|' +
          country +
          '|' +
          filterString,
        params: httpParams,
        cacheMins: 1000,
      })
      .pipe(
        map((mapData: CodeResponse) => {
          console.log('listProvice Done');
          return mapData.data;
        })
      );
  }

  /**
   *
   * @param filterString
   * @param province - Only return provinces for this country
   * @param offset
   * @param limit
   * @returns
   */
  listDistrict(
    filterString: string = '',
    province: string = '',
    offset: number = 0,
    limit: number = 1000
  ): Observable<CodeItem[]> {
    let httpParams = new HttpParams()
      .set('offset', offset + '')
      .set('limit', limit + '')
      .set('province', province)
      .set('filterString', filterString);
    return this._httpClient
      .get<CodeResponse>({
        url: `${this.systemAPI}district`,
        key:
          `sysapi-district|` +
          offset +
          '|' +
          limit +
          '|' +
          province +
          '|' +
          filterString,
        params: httpParams,
        cacheMins: 1000,
      })
      .pipe(
        map((mapData: CodeResponse) => {
          console.log('listDistrict Done');
          return mapData.data;
        })
      );
  }

  /**
   *
   * @param filterString
   * @param province - Only return provinces for this country
   * @param district - Only return provinces for this country
   * @param offset
   * @param limit
   * @returns
   */
  listCity(
    filterString: string = '',
    province: string = '',
    district: string = '',
    offset: number = 0,
    limit: number = 1000
  ): Observable<CodeItem[]> {
    let httpParams = new HttpParams()
      .set('offset', offset + '')
      .set('limit', limit + '')
      .set('province', province)
      .set('district', district === 'USECITY' ? '' : district)
      .set('filterString', filterString);
    return this._httpClient
      .get<CodeResponse>({
        url: `${this.systemAPI}city`,
        key:
          `sysapi-city|` +
          offset +
          '|' +
          limit +
          '|' +
          province +
          '|' +
          district +
          '|' +
          filterString,
        params: httpParams,
        cacheMins: 1000,
      })
      .pipe(
        map((mapData: CodeResponse) => {
          console.log('listCity Done');
          return mapData.data;
        })
      );
  }

  /**
   *
   * @param filterString
   * @param province - Only return provinces for this country
   * @param district - Only return provinces for this country
   * @param offset
   * @param limit
   * @returns
   */
  listPlaces(
    filterString: string = '',
    province: string = '',
    district: string = '',
    town: string = '',
    offset: number = 0,
    limit: number = 1000
  ): Observable<CodeItem[]> {
    let httpParams = new HttpParams()
      .set('offset', offset + '')
      .set('limit', limit + '')
      .set('province', province)
      .set('district', district === 'USECITY' ? '' : district)
      .set('town', town)
      .set('filterString', filterString);
    return this._httpClient
      .get<CodeResponse>({
        url: `${this.systemAPI}place`,
        key:
          `sysapi-places|` +
          offset +
          '|' +
          limit +
          '|' +
          province +
          '|' +
          district +
          '|' +
          town +
          '|' +
          filterString,
        params: httpParams,
        cacheMins: 1000,
      })
      .pipe(
        map((mapData: CodeResponse) => {
          mapData.data.forEach((row) => {
            row.value = row.value.trim();
          });
          return mapData.data;
        })
      );
  }

  getUniqueId(startString: string, endString: string, encKey: string): string {
    console.log('getUniqueId ', startString, endString, encKey);
    let date: Date = new Date();
    let decr =
      '==' +
      startString +
      date.getUTCFullYear() +
      (date.getUTCMonth() + 1) +
      date.getUTCDate() +
      encKey +
      endString +
      '==';
    const md5 = new Md5();
    const ret = md5.appendStr(decr).end();
    if (ret !== undefined) {
      return ret.toString();
    } else {
      return '';
    }
  }

  getCodeGroup(groupId: string): Observable<LPCodeGroup> {
    return this._httpClient
      .get<LPCodeGroupResponse>({
        url: `${this.systemAPI}group/${groupId}`,
        cacheMins: 100,
        key: '',
      })
      .pipe(
        map((data: LPCodeGroupResponse) => {
          return data.codeGroupDetails;
        })
      );
  }
}
