import { Injectable } from '@angular/core';
import { Filesystem, Directory } from '@capacitor/filesystem';

@Injectable({
  providedIn: 'root',
})
export class ImageService {
  constructor() {}

  private async readAsBase64(image: SystemImage) {
    // Fetch the photo, read as a blob, then convert to base64 format
    const response = await fetch(image.webPath!)
      .then((res) => {
        return res;
      })
      .catch((err) => {
        console.log(err);
      });

    if (response) {
      const blob = await response.blob();
      return (await this.convertBlobToBase64(blob)) as string;
    } else {
      return '';
    }
  }

  public convertBlobToBase64 = (blob: Blob) =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onerror = reject;
      reader.onload = () => {
        resolve(reader.result);
      };
      reader.readAsDataURL(blob);
    });

  public async saveImage(image: SystemImage): Promise<string> {
    if (image.base64Data === undefined) {
      image.base64Data = await this.readAsBase64(image);
    }

    await Filesystem.writeFile({
      path: image.fileName!,
      data: image.base64Data,
      directory: Directory.Data,
    });

    return image.base64Data;
  }

  public async loadImage(fileName: string): Promise<any> {
    const image = await Filesystem.readFile({
      path: fileName,
      directory: Directory.Data,
    });

    console.log('loadImage: ', image);

    return image.data;
  }
}

export interface SystemImage {
  fileName: string;
  webPath: string;
  base64Data?: string;
}
