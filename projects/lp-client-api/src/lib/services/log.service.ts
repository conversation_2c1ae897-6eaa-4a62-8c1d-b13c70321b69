import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { LogFields } from '../types/log-fields.interface';
import { Logger } from '../workers/logger';
import { KeyCloakService } from './key-cloak.service';

@Injectable({
  providedIn: 'root',
})
export class LogServiceOld {
  private logger!: Logger;
  private userId!: string;
  constructor(
    @Inject('environment')
    private environment: any,
    private httpClient: HttpClient,
    private kcS: KeyCloakService
  ) {
    this.userId = 'test';
    this.initialize();
  }

  public initialize() {
    this.logger = new Logger(
      this.environment.lssConfig.appName,
      this.environment.lssConfig.logEndpoint,
      this.environment.production,
      this.httpClient,
      this.kcS
    );
  }
  public logHttpInfo(info: any, elapsedTime: number, requestPath: string) {
    // TODO: create and set correlation id
    const url = location.href;
    const logFields: LogFields = {
      environment: this.environment.env,
      userId: this.userId,
      appVersion: this.environment.lssConfig.appVersion,
      requestPath: requestPath,
      elapsedTime: elapsedTime,
      url: url,
    };

    this.logger.log('Information', `${info}`, logFields);
  }

  public logWarning(errorMsg: string) {
    const url = location.href;

    const logFields: LogFields = {
      environment: this.environment.env,
      userId: this.kcS.userId,
      appVersion: this.environment.lssConfig.appVersion,
      requestPath: '',
      elapsedTime: 0,
      url: url,
    };

    this.logger.log('Warning', errorMsg, logFields);
  }

  public logError(errorMsg: string) {
    const url = location.href;

    const logFields: LogFields = {
      environment: this.environment.env,
      userId: this.kcS.userId,
      appVersion: this.environment.lssConfig.appVersion,
      requestPath: '',
      elapsedTime: 0,
      url: url,
    };

    this.logger.log('Error', errorMsg, logFields);
  }

  public logInfo(info: any) {
    const url = location.href;

    const logFields: LogFields = {
      environment: this.environment.env,
      userId: this.kcS.userId,
      appVersion: this.environment.lssConfig.appVersion,
      requestPath: '',
      elapsedTime: 0,
      url,
    };

    this.logger.log('Information', info, logFields);
  }
}
