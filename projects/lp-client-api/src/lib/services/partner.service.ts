import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable, Injector } from '@angular/core';
import { map, Observable } from 'rxjs';
import {
  Partner,
  PartnerListRequest,
  PartnerProduct,
  PartnerProductListRequest,
} from '../types/partner';
import { AbstractService } from './abstract.service';

@Injectable({
  providedIn: 'root',
})
export class PartnerService extends AbstractService {
  constructor(injector: Injector) {
    super(injector);
  }

  getPartners(partnerListRequest: PartnerListRequest): Observable<Partner[]> {
    return this.httpClient
      .postWithResponse<any>({
        url: `${this.apiPublic()}/partner/listPartners`,
        key: '',
        body: partnerListRequest,
      })
      .pipe(
        map((response) => {
          return <Partner[]>(
            response.body.data.map(
              (val: any) =>
                new Partner({
                  partnerId: val.partnerId,
                  partnerName: val.partnerName,
                  address: val.address,
                  telephone: val.telephone,
                  operatingHours: val.operatingHours,
                  partnerMore: val.partnerMore,
                })
            )
          );
        })
      );
  }

  getPartnersAll(
    options?: {allocationType?:string,calcMethod?:string,partnerType?:string,partnerGroup?:string}
  ): Observable<Partner[]> {
    let params = new HttpParams();
    if (!options){
      options = {};
    }
    if (options.allocationType){
      params = params.set("allocation",options.allocationType);
    }
    if (options.calcMethod){
      params = params.set("calc",options.calcMethod);
    }
    if (options.partnerType){
      params = params.set("type",options.partnerType);
    }
    if (options.partnerGroup){
      params = params.set("group",options.partnerGroup);
    }
    let key = `PARTNER-LIST-ALL-${options.allocationType}-${options.calcMethod}-${options.partnerType}-${options.partnerGroup}`;
    return this.httpClient
      .getWithResponse<any>({
        url: `${this.apiPublic()}/partner/listPartnersAll`,
        key,
        params,
        cacheMins: 60
      })
      .pipe(
        map((response) => {
          return <Partner[]>(
            response.body.map(
              (val: any) =>
                new Partner({
                  partnerId: val.PARTNERID,
                  partnerName: val.PARTNERNAME,
                  address: val.ADDRESS,
                  telephone: val.TELEPHONE,
                  operatingHours: val.OPERATINGHOURS,
                  partnerMore: val.PARTNERMORE,
                })
            )
          );
        })
      );
  }

  getPartnerProducts(
    partnerProductListRequest: PartnerProductListRequest
  ): Observable<PartnerProduct[]> {
    return this.httpClient
      .postWithResponse<any>({
        url: `${this.apiPublic()}/partner/listPartnerProducts`,
        key: 'PARTNER-PRODUCT-LIST',
        body: partnerProductListRequest,
      })
      .pipe(
        map((response) => {
          return <PartnerProduct[]>response.body.data;
        })
      );
  }
}
