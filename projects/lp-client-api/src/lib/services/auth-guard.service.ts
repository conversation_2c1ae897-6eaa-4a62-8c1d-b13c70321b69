import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { KeyCloakService } from './key-cloak.service';
@Injectable({
  providedIn: 'root',
})
export class AuthGuardService  {
  constructor(
    protected readonly router: Router,
    protected readonly keyCloakService: KeyCloakService
  ) {}

  get authenticated(): boolean | undefined {
    return this.keyCloakService.authSuccess;
  }
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean> {
    return this.isAccessAllowed(route, state);
  }
  public async isAccessAllowed(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ) {
    let _return = false;
    // Force the user to log in if currently unauthenticated.
    if (!await this.keyCloakService.authed()) {
      localStorage.setItem('authurllocked', state.url);
      this.router.navigate(['/public/landing']);
      return false;
      //await this.keyCloakService.keycloak?.login();
    } else if (
      this.authenticated &&
      this.keyCloakService.userProfile?.mustRegister
    ) {
      localStorage.setItem('authurllocked', state.url);
      this.router.navigate(['/public/signup']);
      return false;
    }

    // Get the roles required from the route.
    const requiredRoles = route.data['roles'];

    // Allow the user to proceed if no additional roles are required to access the route.
    if (!(requiredRoles instanceof Array) || requiredRoles.length === 0) {
      _return = true;
    }
    if (!_return) {
      _return = requiredRoles.every((role: any) =>
        this.keyCloakService.userProfile?.realm_access.roles.includes(role)
      );
    }
    // Allow the user to proceed if all the required roles are present.
    return _return;
  }
}
