import { Injectable, Injector } from '@angular/core';
import { Router } from '@angular/router';
import { Preferences } from '@capacitor/preferences';
import { lastValueFrom } from 'rxjs/internal/lastValueFrom';
import { Observable, BehaviorSubject } from 'rxjs';
import { of } from 'rxjs/internal/observable/of';
import { catchError, map } from 'rxjs/operators';
import {
  Slip,
  SlipConfig,
  Terminal,
  WAPSlipData,
  WAPSlipConfig,
} from '../types/system';
import * as _ from 'lodash';
import { ImageService, SystemImage } from './image.service';
import { AbstractService } from './abstract.service';
import { BasicProfile } from '../types/member';
import { LogService } from './public-api';
import { MemberCommObject } from '../types/system';

@Injectable({
  providedIn: 'root',
})
export class TerminalService extends AbstractService {
  public terminal = new BehaviorSubject<Terminal>(<Terminal>{});
  public attendant = new BehaviorSubject<BasicProfile | null>(<BasicProfile>{});

  constructor(
    injector: Injector,
    protected readonly router: Router,
    private imageService: ImageService,
    private logService: LogService
  ) {
    super(injector);
  }

  public async getLocalTerminal(): Promise<Terminal> {
    let terminal!: Terminal;
    const result = await Preferences.get({ key: 'terminal' });
    if (result.value !== null) {
      terminal = JSON.parse(result.value);
      this.terminal.next(terminal);
    }

    return terminal;
  }

  private async setLocalTerminal(terminal: Terminal) {
    this.logService.debug(
      'TerminalService.setLocalTerminal',
      'Incoming terminal to be set'
    );
    // Only write the terminal object if different to the current stored terminal
    let currentTerminal: Terminal = await this.getLocalTerminal();
    if (
      currentTerminal !== undefined &&
      currentTerminal.logobase64 !== undefined
    ) {
      terminal.logobase64 = currentTerminal.logobase64; // base64 is not transmitted via api, make them equal for comparison
    }

    if (
      currentTerminal !== undefined &&
      currentTerminal.slipLogobase64 !== undefined
    ) {
      terminal.slipLogobase64 = currentTerminal.slipLogobase64; // base64 is not transmitted via api, make them equal for comparison
    }

    if (_.isEqual(currentTerminal, terminal)) {
      this.logService.debug(
        'TerminalService.setLocalTerminal',
        'Terminal data has not changed, do not set'
      );
      this.terminal.next(currentTerminal);
    } else {
      this.logService.debug(
        'TerminalService.setLocalTerminal',
        'Terminal data has changed, update'
      );
      let logoImage: SystemImage = {
        fileName: 'device_logo.png',
        webPath: terminal.logo!,
      };
      terminal.logobase64 = await this.imageService.saveImage(logoImage);

      let slipLogoImage: SystemImage = {
        fileName: 'slip_logo.png',
        webPath: terminal.slipLogo!,
      };
      terminal.slipLogobase64 = await this.imageService.saveImage(
        slipLogoImage
      );

      await Preferences.set({
        key: 'terminal',
        value: JSON.stringify(terminal),
      });

      this.lssConfig.apiBaseUrl = this.lssConfig.apiBaseUrlMask?.replace(
        '{clientId}',
        terminal.clientId.toLowerCase()
      );

      this.lssConfig.terminal = terminal;
      this.terminal.next(terminal);
    }
  }

  private async deleteLocalTerminal() {
    await Preferences.remove({
      key: 'terminal',
    });

    this.lssConfig.terminal = undefined;
    this.terminal.next(<Terminal>{});
  }

  private fetchTerminal(
    terminalId: String,
    deviceId: string,
    pin: string
  ): Observable<Terminal | undefined> {
    this.logService.warn(
      'TerminalService.fetchTerminal',
      `${this.apiPublic()}/terminal/${terminalId}?deviceId=${deviceId}&pin=${pin}`
    );
    return this.httpClient
      .get<Terminal[]>({
        url: `${this.apiPublic()}/terminal/${terminalId}?deviceId=${deviceId}&pin=${pin}`,
        cacheMins: 0,
        key: '',
      })
      .pipe(
        map((result) => {
          if (result.length === 1) {
            let terminal: Terminal = result[0];
            terminal.allowAccrual =
              terminal.allowAccrual.toString() === 'true' ? true : false;
            terminal.allowBalance =
              terminal.allowBalance.toString() === 'true' ? true : false;
            terminal.allowRedemption =
              terminal.allowRedemption.toString() === 'true' ? true : false;
            terminal.allowRegistration =
              terminal.allowRegistration.toString() === 'true' ? true : false;
            terminal.pinRequired =
              terminal.pinRequired.toString() === 'true' ? true : false;
            terminal.otpRequired =
              terminal.otpRequired.toString() === 'true' ? true : false;

            return terminal;
          } else {
            return undefined;
          }
        }),
        catchError((error) => {
          this.logService.error('TerminalService.fetchTerminal', error.message);
          return of(undefined);
        })
      );
  }

  async authenticateTerminal(
    terminalId: string,
    deviceId: string,
    pin: string
  ): Promise<boolean> {
    let terminal!: Terminal;
    if (terminalId === null || terminalId === undefined || terminalId === '') {
      return false;
    }
    let outcome: boolean = await lastValueFrom(
      this.fetchTerminal(terminalId, deviceId, pin).pipe(
        map((response) => {
          console.log(response);
          if (response !== undefined) {
            terminal = response;
            if (response !== undefined) {
              terminal = response;
            }

            if (
              terminal.status === 'STAA' &&
              (terminal.endDate === undefined ||
                terminal.endDate === null ||
                new Date(terminal.endDate) > new Date())
            ) {
              let expiryDate: Date = new Date();
              expiryDate.setHours(expiryDate.getHours() + 2);
              terminal.expiryDate = expiryDate;

              return true;
            } else {
              console.log('terminal.endDate: 2');
              this.deleteLocalTerminal();
              this.logService.error(
                'TerminalService.authenticateTerminal',
                'Terminal is no longer active',
                new Date(terminal.endDate),
                terminal.status
              );
              return false;
            }
          } else {
            return false;
          }
        }),
        catchError((error) => {
          this.logService.error(
            'TerminalService.authenticateTerminal',
            error.message
          );
          return of(false);
        })
      )
    );

    // Try to create the API ID configuration for the temrinal
    if (outcome) {
      if (terminal.apiIdRaw !== undefined) {
        let tmp: string[] = terminal.apiIdRaw.split(',');

        if (tmp.length === 3 || tmp.length === 4) {
          terminal.apiId = {
            apiId: tmp[0],
            secretStart: tmp[1],
            secretEnd: tmp[2],
            userId: tmp.length === 4 ? tmp[3] : '',
          };
        } else {
          outcome = false;
        }
      } else {
        outcome = false;
      }
      /*
      if (terminal.memberAccrualSlipRaw !== undefined) {
        terminal.memberAccrualSlip = JSON.parse(terminal.memberAccrualSlipRaw!);
      }

      if (terminal.memberRedeemSlipRaw !== undefined) {
        terminal.memberRedeemSlip = JSON.parse(terminal.memberRedeemSlipRaw!);
      }

      if (terminal.partnerAccrualSlipRaw !== undefined) {
        terminal.partnerAccrualSlip = JSON.parse(
          terminal.partnerAccrualSlipRaw!
        );
      }

      if (terminal.partnerRedeemSlipRaw !== undefined) {
        terminal.partnerRedeemSlip = JSON.parse(terminal.partnerRedeemSlipRaw!);
      }

      if (terminal.memberGiftCardSlipRaw !== undefined) {
        terminal.memberGiftCardSlip = JSON.parse(
          terminal.memberGiftCardSlipRaw!
        );
      }

      if (terminal.partnerGiftCardSlipRaw !== undefined) {
        terminal.partnerGiftCardSlip = JSON.parse(
          terminal.partnerGiftCardSlipRaw!
        );
      }*/
    }

    if (outcome) {
      await this.setLocalTerminal(terminal);
      return true;
    } else {
      return false;
    }
  }

  async logout(navigate: boolean) {
    await this.deleteLocalTerminal();
    if (navigate) {
      this.router.navigate(['/public']);
    }
  }

  getLogo(defaultLogo: string): string {
    return this.terminal.getValue().logobase64 === undefined
      ? defaultLogo
      : this.terminal.getValue().logobase64!;
  }

  getSlipLogo(defaultLogo: string): string {
    return this.terminal.getValue().slipLogobase64 === undefined
      ? defaultLogo
      : this.terminal.getValue().slipLogobase64!;
  }

  saveSlip(
    terminalId: string,
    slip: Slip,
    tranType: string,
    auditUser: string,
    memberCommObject: MemberCommObject,
    cacheMins?: number
  ): Observable<any> {
    //slip.slip!.logo = ''; // clear out the logo as we do not need to save that to the database
    let tmpSlip: WAPSlipData = Object.assign({}, slip.slip!);
    tmpSlip.logo = undefined;

    return this.httpClient
      .post<any>({
        url: `${this.apiPublic()}/terminal/${terminalId}/slip?mpacc=${
          slip.slip?.cardNumber
        }&invoiceNumber=${
          slip.slip?.invoiceNumber
        }&tranType=${tranType}&user=${auditUser}`,
        key: `TERMINAL-SLIP-SAVE-${memberCommObject.membershipNumber}`,
        body: tmpSlip,
        cacheMins,
      })
      .pipe(
        map((result) => {
          return result;
        })
      );
  }

  listSlips(
    terminalId: string,
    printDate: Date,
    invoiceNumber: string,
    memberCommObject: MemberCommObject,
    cacheMins?: number
  ): Observable<Slip[]> {
    return this.httpClient
      .get<Slip>({
        url: `${this.apiPublic()}/terminal/${terminalId}/slip?mpacc=${
          memberCommObject.membershipNumber
        }&printDate=${printDate}&invoiceNumber=${invoiceNumber}`,
        key: `TERMINAL-SLIP-LIST-${memberCommObject.membershipNumber}`,
        body: memberCommObject,
        cacheMins,
      })
      .pipe(
        map((result: any) => {
          return result;
        })
      );
  }

  getTerminalSlip(
    terminal: Terminal,
    type: string,
    func: string
  ): WAPSlipConfig[] {
    let slip: SlipConfig[] = terminal.slips.filter((f) => {
      return f.slipType === type && f.slipFunction === func;
    });
    return JSON.parse(slip[0].slipData);
  }
}
