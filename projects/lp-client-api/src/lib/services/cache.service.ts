import { Injectable } from '@angular/core'

@Injectable({
    providedIn: 'root'
  })
export class CacheService {
    storageType:'map'|'local'='map';
    internalStorage = new Map();
	constructor() { }

	save(options: LocalStorageSaveOptions) {
		// Set default values for optionals
		options.expirationMins = options.expirationMins || 0
		// Set expiration date in miliseconds
		const expirationMS = options.expirationMins !== 0 ? options.expirationMins * 60 * 1000 : 0
		const record = {
			//value: typeof options.data === 'string' ? options.data : JSON.stringify(options.data),
            value: options.data,
			expiration: expirationMS !== 0 ? new Date().getTime() + expirationMS : null,
			hasExpiration: expirationMS !== 0 ? true : false
		}
        if (this.storageType === 'map'){
            this.internalStorage.set(options.key, record);
        }else{
            localStorage.setItem(options.key, JSON.stringify(record))
        }
	}

	load(key: string) {
		// Get cached data from localstorage
        let item = null;
        if (this.storageType === 'map'){
            item = this.internalStorage.get(key);
        }else{
            item = localStorage.getItem(key);
        }
		if (item !== null) {
            let record = null;
            if (this.storageType === 'map'){
                record = item;
            }else{
                record = JSON.parse(item);
            }
			const now = new Date().getTime()
			// Expired data will return null
			if (!record || (record.hasExpiration && record.expiration <= now)) {
				return null
			} else {
				//return JSON.parse(record.value)
                return record.value;
			}
		}
		return null
	}

	remove(key: string) {
        if (this.storageType === 'map'){
            this.internalStorage.delete(key);
        }else{
            localStorage.removeItem(key)
        }
	}

	cleanLocalStorage() {
        if (this.storageType === 'map'){
            this.internalStorage.clear();
        }else{
            localStorage.clear()
        }
	}
}

export class LocalStorageSaveOptions {
	key: string = '';
	data: any;
	expirationMins?: number;
}
