import { Injectable, OnDestroy, OnInit } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { LssConfig } from '../types/lss-config';
import { Terminal } from '../types/system';
import { AlertController } from '@ionic/angular';
import { TerminalService } from './terminal.service';
import { LogService } from '../log/log.service';

@Injectable({
  providedIn: 'root',
})
export class TerminalGuardService {
  constructor(
    protected readonly router: Router,
    private lssConfig: LssConfig,
    private terminalService: TerminalService,
    private alertController: AlertController,
    private logService: LogService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean> {
    return this.isAccessAllowed();
  }

  public async isAccessAllowed(): Promise<boolean> {
    let currentTime: Date = new Date();
    let terminal!: Terminal;
    this.terminalService.terminal.subscribe((value) => (terminal = value));

    // Attempt to get the termina from storage if observable is undefined
    if (terminal === undefined || terminal.terminalId === undefined) {
      terminal = await this.terminalService.getLocalTerminal();
      this.logService.debug(
        'TerminalGuardService.isAccessAllowed',
        'Fetch storage attempt'
      );
    }

    if (
      terminal !== undefined &&
      terminal.terminalId !== undefined &&
      terminal.clientId !== undefined
    ) {
      this.logService.debug(
        'TerminalGuardService.isAccessAllowed',
        'Terminal device set, validate if still valid'
      );
      this.lssConfig.apiBaseUrl = this.lssConfig.apiBaseUrlMask?.replace(
        '{clientId}',
        terminal.clientId.toLowerCase()
      );

      if (
        terminal.status === 'STAA' &&
        (terminal.endDate === undefined ||
          terminal.endDate === null ||
          new Date(terminal.endDate) > new Date())
      ) {
        this.logService.debug(
          'TerminalGuardService.isAccessAllowed',
          'is terminal expired: ',
          currentTime.toISOString(),
          new Date(terminal.expiryDate).toISOString()
        );
        if (currentTime.getTime() > new Date(terminal.expiryDate).getTime()) {
          this.logService.debug(
            'TerminalGuardService.isAccessAllowed',
            'Terminal must be revalidated'
          );
          let authenticate: boolean =
            await this.terminalService.authenticateTerminal(
              terminal.terminalId,
              terminal.deviceId,
              terminal.pin
            );

          if (authenticate) {
            await this.isAccessAllowed().then((result) => {
              if (result === false) {
                this.router.navigate(['/public']);
                this.showAlert();
              }
            });
          } else {
            this.logService.debug(
              'TerminalGuardService.isAccessAllowed',
              'Authenticate failed'
            );
            await this.terminalService.logout(true);
            this.showAlert();
            return false;
          }
        } else {
          this.logService.debug(
            'TerminalGuardService.isAccessAllowed',
            'Terminal is still valid'
          );
          return true;
        }
      } else {
        this.logService.debug(
          'TerminalGuardService.isAccessAllowed',
          'Terminal no longer valid'
        );

        this.showAlert();
      }
    }

    this.logService.debug(
      'TerminalGuardService.isAccessAllowed',
      'Access not allowed'
    );
    this.router.navigate(['/public']);

    this.logService.debug(
      'TerminalGuardService.isAccessAllowed',
      'API url set: ',
      this.lssConfig.apiBaseUrl
    );

    return false;
  }

  async showAlert() {
    const alert = await this.alertController.create({
      header: 'Activation Error',
      message:
        'This device is no longer active, please re-authenticate or contact your administrator.',
      buttons: ['OK'],
      cssClass: 'system-alert',
    });

    alert.present();
  }
}
