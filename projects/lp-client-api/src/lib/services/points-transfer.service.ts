import { Injectable, Injector } from '@angular/core';
import { HttpParams, HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AbstractService } from './abstract.service';
import { SystemService } from './system.service'; // Add this import

@Injectable({
  providedIn: 'root'
})
export class PointsTransferService extends AbstractService {
  private apiPath = '/member/transfer';
  private nativeHttp: HttpClient;
  private systemService: SystemService;
  
  constructor(injector: Injector) {
    super(injector);
    // Get access to Angular's native HttpClient
    this.nativeHttp = injector.get(HttpClient);
    // Get access to SystemService
    this.systemService = injector.get(SystemService);
  }
  
  /**
   * Transfer points from one account to another
   * @param fromMembershipNumber The membership number to transfer points from
   * @param toMembershipNumber The membership number to transfer points to
   * @param pointsAmount The amount of points to transfer
   * @returns Observable of the transfer response
   */
  transferPoints(fromMembershipNumber: string, toMembershipNumber: string, pointsAmount: number): Observable<any> {
    const body = {
      mpacc: fromMembershipNumber,
      frommpacc: fromMembershipNumber,
      tompacc: toMembershipNumber,
      pcu: pointsAmount
    };

    let uniqueId = this.systemService.getUniqueId(
      this.lssConfig.apiIdKeyStart,
      this.lssConfig.apiIdKeyEnd,
      fromMembershipNumber
    );
    
    // Use MemberProfile.uniqueId for authentication as requested
    const headers = {
      'Content-Type': 'application/json',
      'LP_APIID': this.lssConfig.apiId || '',
      'LP_UniqueId': uniqueId
    };
    
    console.log('Using MemberProfile.uniqueId:',uniqueId);
    console.log('Headers:', headers);
    console.log('Body:', body);
    
    return this.nativeHttp.post<any>(
      `${this.apiPublic()}${this.apiPath}`,
      body,
      { headers }
    );
  }
  
  /**
   * Get transfer history for a membership number
   * @param membershipNumber The membership number to get history for
   * @returns Observable of the transfer history
   */
  getTransferHistory(membershipNumber: string): Observable<any> {
    const params = new HttpParams().set('mpacc', membershipNumber);
    
    const requestKey = `TRANSFER-HISTORY-${membershipNumber}-${Date.now()}`;
    
    return this.httpClient.get<any>({
      url: `${this.apiPublic()}/secure/loyaltyapi/1.0.0/member/transfer/history`,
      key: requestKey,
      params: params
    });
  }
}
