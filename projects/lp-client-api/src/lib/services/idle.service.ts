import { Injectable, Injector } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { AbstractService } from './abstract.service';

@Injectable({
  providedIn: 'root',
})
export class IdleService extends AbstractService {
  public timeout = new BehaviorSubject<number>(0);

  private userActivity!: ReturnType<typeof setTimeout>;

  private count = 0;
  private _state: 'started' | 'stopped' = 'stopped';

  constructor(injector: Injector) {
    super(injector);
    this.count = 0;
  }

  start() {
    if (this._state !== 'started') {
      window.addEventListener('mousemove', this.reset);
      window.addEventListener('scroll', this.reset);
      window.addEventListener('keydown', this.reset);
      this._state = 'started';
      this.incrementTimeout();
    }
  }

  stop() {
    if (this._state !== 'stopped') {
      clearTimeout(this.userActivity);
      window.removeEventListener('mousemove', this.reset);
      window.removeEventListener('scroll', this.reset);
      window.removeEventListener('keydown', this.reset);
      this._state = 'stopped';
      this.count = 0;
      this.timeout.next(this.count);
    }
  }

  private reset = () => {
    if (this._state !== 'started') {
      this._state = 'started';
    }
    clearTimeout(this.userActivity);
    this.timeout.next(0);
    this.count = 0;
    this.incrementTimeout();
  };

  private incrementTimeout(): void {
    this.userActivity = setTimeout(() => {
      this.count += 10;
      this.timeout.next(this.count);
      this.incrementTimeout();
    }, 10000);
  }
}
