import { Injectable, Injector } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { AbstractService } from './abstract.service';

export interface FirebaseTokenResponse {
  success: boolean;
  message?: string;
  error?: string;
  data?: any;
}

export interface DeviceTokenResponse {
  success: boolean;
  message?: string;
  error?: string;
  data?: any;
}

export interface FirebaseTokenPayload {
  mpacc: string;
  token: string;
  device: string;
  type: 'android' | 'ios' | 'web';
}

@Injectable({
  providedIn: 'root'
})
export class FirebaseMemberService extends AbstractService {

  constructor(injector: Injector) {
    super(injector);
  }

  /**
   * Link Firebase FCM token with member account using device token endpoint
   */
  linkFirebaseToken(token: string, memberId: string): Observable<FirebaseTokenResponse> {
    console.log('🔥 FirebaseMemberService.linkFirebaseToken called with token:', token.substring(0, 20) + '...');
    
    try {
      if (!memberId) {
        console.error('❌ No member ID available for token linking');
        return of({
          success: false,
          error: 'Member ID not available. Please login first.'
        });
      }

      const deviceType = this.getPlatform() as 'android' | 'ios' | 'web';
      const payload: FirebaseTokenPayload = {
        mpacc: memberId,
        token: token,
        device: this.getUniqueDeviceId(),
        type: deviceType
      };

      console.log('📤 Sending payload to backend:', {
        ...payload,
        token: payload.token.substring(0, 20) + '...'
      });

      // Use the correct API endpoint from abstract service configuration
      const url = `${this.apiSecure()}/member/device/token`;
      return this.httpClient.post<any>({
        url: url,
        cacheMins: 0,
        key: '',
        body: payload
      }).pipe(
        map((response: any) => {
          // Transform backend response to match FirebaseTokenResponse interface
          console.log('🔄 Transforming backend response:', response);
          return {
            success: response.status === 'success',
            message: response.message,
            error: response.status !== 'success' ? response.message || response.error : undefined,
            data: response.data || response
          } as FirebaseTokenResponse;
        })
      );
      
    } catch (error) {
      console.error('❌ Error in linkFirebaseToken:', error);
      // Return error response in case of exception
      return of({
        success: false,
        error: 'Failed to link Firebase token: ' + (error as Error).message
      });
    }
  }

  /**
   * Get all device tokens for a member
   */
  getDeviceTokens(memberId: string, device?: string): Observable<any> {
    console.log('📱 FirebaseMemberService.getDeviceTokens called for member:', memberId);
    
    try {
      if (!memberId) {
        console.error('❌ No member ID available for getting device tokens');
        return of({
          success: false,
          error: 'Member ID not available. Please login first.'
        });
      }

      const url = `${this.apiSecure()}/member/device/token`;
      let params = new HttpParams().set('mpacc', memberId);
      if (device) {
        params = params.set('device', device);
      }

      return this.httpClient.get({
        url: url,
        cacheMins: 0,
        key: '',
        params: params
      }).pipe(
        map((response: any) => {
          console.log('🔄 Transforming get device tokens response:', response);
          
          // Handle both cases: response is array directly OR response has data property
          const dataArray = Array.isArray(response) ? response : response.data;
          const hasData = dataArray && Array.isArray(dataArray) && dataArray.length > 0;
          
          return {
            success: hasData,
            message: response.message,
            error: !hasData ? 'No device tokens found' : undefined,
            data: dataArray
          };
        })
      );
      
    } catch (error) {
      console.error('❌ Error in getDeviceTokens:', error);
      return of({
        success: false,
        error: 'Failed to get device tokens: ' + (error as Error).message
      });
    }
  }

  /**
   * Delete a specific device token
   */
  deleteDeviceToken(memberId: string, token: string): Observable<any> {
    console.log('🗑️ FirebaseMemberService.deleteDeviceToken called for member:', memberId);
    
    try {
      if (!memberId || !token) {
        console.error('❌ Missing member ID or token for deletion');
        return of({
          success: false,
          error: 'Member ID and token are required for deletion.'
        });
      }

      const url = `${this.apiSecure()}/member/device/token`;
      const params = new HttpParams()
        .set('mpacc', memberId)
        .set('token', token);

      return this.httpClient.delete({
        url: url,
        cacheMins: 0,
        key: '',
        params: params
      }).pipe(
        map((response: any) => {
          console.log('🔄 Transforming delete device token response:', response);
          return {
            success: response.status === 'success' || response.success === true,
            message: response.message,
            error: response.status !== 'success' && response.success !== true ? response.message || response.error : undefined,
            data: response.data || response
          };
        })
      );
      
    } catch (error) {
      console.error('❌ Error in deleteDeviceToken:', error);
      return of({
        success: false,
        error: 'Failed to delete device token: ' + (error as Error).message
      });
    }
  }

  /**
   * Register device token for push notifications
   */
  registerDeviceToken(token: string, platform: 'ios' | 'android' | 'web'): Observable<DeviceTokenResponse> {
    console.log('📱 FirebaseMemberService.registerDeviceToken called with token:', token.substring(0, 20) + '...');
    
    try {
      const payload = {
        deviceToken: token,
        platform: platform,
        timestamp: new Date().toISOString(),
        appVersion: '1.0.0',
        deviceInfo: {
          userAgent: navigator.userAgent,
          language: navigator.language,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        }
      };

      console.log('📤 Sending device token payload to backend:', {
        ...payload,
        deviceToken: payload.deviceToken.substring(0, 20) + '...'
      });

      // Make HTTP request to backend API
      const url = `${this.apiSecure()}/members/device-token`;
      return this.httpClient.post<DeviceTokenResponse>({
        url: url,
        cacheMins: 0,
        key: '',
        body: payload
      });
      
    } catch (error) {
      console.error('❌ Error in registerDeviceToken:', error);
      // Return error response in case of exception
      return of({
        success: false,
        error: 'Failed to register device token: ' + (error as Error).message
      });
    }
  }

  /**
   * Unsubscribe from Firebase topic
   */
  unsubscribeFromTopic(topic: string): Observable<FirebaseTokenResponse> {
    console.log('🔕 Unsubscribing from topic:', topic);
    
    try {
      const payload = {
        topic: topic,
        action: 'unsubscribe',
        timestamp: new Date().toISOString()
      };

      const url = `${this.apiSecure()}/members/firebase-topic`;
      return this.httpClient.post<FirebaseTokenResponse>({
        url: url,
        cacheMins: 0,
        key: '',
        body: payload
      });
      
    } catch (error) {
      console.error('❌ Error in unsubscribeFromTopic:', error);
      return of({
        success: false,
        error: 'Failed to unsubscribe from topic: ' + (error as Error).message
      });
    }
  }

  /**
   * Subscribe to Firebase topic
   */
  subscribeToTopic(topic: string): Observable<FirebaseTokenResponse> {
    console.log('🔔 Subscribing to topic:', topic);
    
    try {
      const payload = {
        topic: topic,
        action: 'subscribe',
        timestamp: new Date().toISOString()
      };

      const url = `${this.apiSecure()}/members/firebase-topic`;
      return this.httpClient.post<FirebaseTokenResponse>({
        url: url,
        cacheMins: 0,
        key: '',
        body: payload
      });
      
    } catch (error) {
      console.error('❌ Error in subscribeToTopic:', error);
      return of({
        success: false,
        error: 'Failed to subscribe to topic: ' + (error as Error).message
      });
    }
  }

  /**
   * Get current platform
   */
  private getPlatform(): string {
    if (typeof window !== 'undefined') {
      // First check if we're in a Capacitor environment (native app)
      const isCapacitor = !!(window as any).Capacitor;
      
      // Additional checks for web environment
      const isWebEnvironment = !!(
        window.location && 
        window.location.protocol && 
        (window.location.protocol.startsWith('http') || window.location.protocol.startsWith('file'))
      );
      
      // Debug logging
      console.log('🔍 Platform Detection Debug:', {
        isCapacitor,
        isWebEnvironment,
        protocol: window.location?.protocol,
        userAgent: window.navigator.userAgent.substring(0, 50) + '...'
      });
      
      if (isCapacitor && !isWebEnvironment) {
        // We're in a native app, check the actual device platform
        const userAgent = window.navigator.userAgent.toLowerCase();
        
        if (/android/i.test(userAgent)) {
          console.log('📱 Detected platform: android (native app)');
          return 'android';
        } else if (/iphone|ipad|ipod/i.test(userAgent)) {
          console.log('📱 Detected platform: ios (native app)');
          return 'ios';
        }
      }
      
      // If not in Capacitor, or if we have web indicators, we're on web
      console.log('🌐 Detected platform: web (browser)');
      return 'web';
    }
    
    console.log('🌐 Detected platform: web (no window object)');
    return 'web';
  }

  /**
   * Generate or retrieve a unique device identifier
   */
  private getUniqueDeviceId(): string {
    const storageKey = 'device_unique_id';
    
    // Try to get existing device ID from localStorage
    let deviceId = localStorage.getItem(storageKey);
    
    if (!deviceId) {
      // Generate a new unique identifier combining multiple device characteristics
      const platform = this.getPlatform();
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 15);
      
      // Create a more comprehensive fingerprint
      const fingerprint = this.generateDeviceFingerprint();
      
      // Combine elements to create unique ID
      deviceId = `${platform}_${fingerprint}_${timestamp}_${random}`;
      
      // Store it for future use
      localStorage.setItem(storageKey, deviceId);
      console.log('🔑 Generated new unique device ID:', deviceId);
    } else {
      console.log('🔑 Retrieved existing device ID:', deviceId);
    }
    
    return deviceId;
  }

  /**
   * Generate a device fingerprint based on available browser/device characteristics
   */
  private generateDeviceFingerprint(): string {
    if (typeof window === 'undefined') {
      return 'server';
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx?.fillText('fingerprint', 10, 10);
    const canvasFingerprint = canvas.toDataURL().slice(-10);

    const characteristics = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      screen.colorDepth,
      new Date().getTimezoneOffset(),
      !!window.sessionStorage,
      !!window.localStorage,
      canvasFingerprint
    ].join('|');

    // Create a simple hash of the characteristics
    let hash = 0;
    for (let i = 0; i < characteristics.length; i++) {
      const char = characteristics.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }
}