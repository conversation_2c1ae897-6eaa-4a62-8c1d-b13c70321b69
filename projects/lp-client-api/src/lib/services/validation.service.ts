import { Injectable } from '@angular/core';
import { AbstractControl, FormGroup, ValidationErrors } from '@angular/forms';
import { PhoneNumberUtil } from 'google-libphonenumber';

@Injectable({
  providedIn: 'root',
})
export class ValidationService {
  static phoneUtil = PhoneNumberUtil.getInstance();

  private static validationMessages = new Map<String, any>();
  static initialize() {
    ValidationService.validationMessages.set('generic', {
      required: 'Field is required',
      minlength:
        '[compname] must be at least [requiredLength] characters long.',
      maxlength:
        '[compname] cannot be more than [requiredLength] characters long.',
        exactlength:
        '[compname] must be [requiredLength] characters long.',
      phone: '[compname] is not a valid phone number.',
      nationalIdNum: '[compname] is not a valid phone number.',
      min: '[compname] is less than the allowable minumum of [min]',
      matchValue: '[sourceLabel] does not match [targetLabel]',
      passwordInvalid: '[message]',
      pattern: '[message]',
    });
    ValidationService.validationMessages.set('vatNumber', {
      pattern: 'Vat Number can only contain numbers.',
    });
    ValidationService.validationMessages.set('taxNumber', {
      pattern: 'TAX Number can only contain numbers.',
    });
    ValidationService.validationMessages.set('rate', {
      pattern: 'Rate can only be a currency value.',
    });
    ValidationService.validationMessages.set('hours', {
      pattern: 'Hours can only be a numeric value.',
    });
    ValidationService.validationMessages.set('minutes', {
      pattern: 'Minutes can only be a numeric value.',
    });
    ValidationService.validationMessages.set('email', {
      pattern: 'Invalid email address',
    });
    ValidationService.validationMessages.set('emailAddress', {
      pattern: 'Invalid email address',
    });
  }
  
  // TODO: Change do errors to make use of a the label property
  showErrors(form: FormGroup, control: string, label: string): string[] {
    const errors: any = form.controls[control].errors;
    return this.doErrors(errors, control);
  }

  doErrors(errors: any, compId: string, compLabel?: string): string[] {
    errors = errors || {};
    const _return: string[] = [];
    compId = compId.toLowerCase();
    compLabel = compLabel || compId.charAt(0).toUpperCase() + compId.slice(1);
    for (let error in errors) {
      let errorString: string;

      if (
        ValidationService.validationMessages.get(compId) &&
        ValidationService.validationMessages.get(compId)[error]
      ) {
        errorString = ValidationService.validationMessages.get(compId)[error];
      } else if (ValidationService.validationMessages.get('generic')[error]) {
        errorString =
          ValidationService.validationMessages.get('generic')[error];
      } else {
        errorString = `Unkown Type : ${error}`;
      }
      errorString = errorString.replace('[compname]', compLabel);

      //console.log(errors[error]);
      const replace = errorString.match(/\[.*?\]/g);
      if (replace != null && replace.length > 0) {
        replace.forEach((data) => {
          errorString = errorString.replace(
            data,
            errors[error][data.replace('[', '').replace(']', '')]
          );
        });
      }
      _return.push(errorString);
    }
    /*
    if (this.validationMessages[compName] != null) {
      this.validationMessages[compName].forEach(row => {
        if (row.type in errors && !(row.type in errorsValidated)) {
          errorsValidated.push(row.type);
          _return.push(row.message);
        }
      });
    }

    this.validationMessages.generic.forEach(row => {
      
      if (row.type in errors && errorsValidated.indexOf(row.type) == -1) {
        let addMessage = row.message.replace('[compname]',
          compName.charAt(0).toUpperCase() + compName.slice(1)
        );

        const replace = row.message.match(/\[.*?\]/g);

        if (replace != null) {
          if (replace.length > 0) {
            replace.forEach(data => {
              addMessage = addMessage.replace(
                data,
                errors[row.type][data.replace('[', '').replace(']', '')]
              );
            });
          }
        }

        errorsValidated.push(row.type);
        _return.push(addMessage);
      }
    });*/
    return _return;
  }

  static phone(control: AbstractControl): ValidationErrors | null {
    if (control.value) {
      try {
        //(control.value as IonIntlTelInputModel)
        const number = ValidationService.phoneUtil.parseAndKeepRawInput(
          control.value.internationalNumber
        );
        if (ValidationService.phoneUtil.isValidNumber(number)) {
          return null;
        }
      } catch (e) {}
      return { phone: { value: control.value } };
    }
    return null;
  }
}

ValidationService.initialize();
