import { Injectable } from '@angular/core';
import {
  HttpEvent,
  HttpRequest,
  HttpHandler,
  HttpInterceptor,
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { KeyCloakService } from './key-cloak.service';

@Injectable({
  providedIn: 'root',
})
export class InterceptorService implements HttpInterceptor {
  constructor(private kc: KeyCloakService) {}

  intercept(
    request: HttpRequest<any>,
    next: <PERSON>ttpHandler
  ): Observable<HttpEvent<any>> {
    if (this.kc.authSuccess && this.kc.keycloak) {
      const headers = request.headers.append(
        'Authorization',
        'Bearer ' + this.kc.keycloak.token
      );
      request = request.clone({
        headers: headers,
      });
    }

    return next.handle(request);
  }
}
