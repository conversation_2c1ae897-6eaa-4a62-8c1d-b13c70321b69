import { Injectable, Injector, Type } from '@angular/core';
import { LssConfig } from '../types/lss-config';
import { HttpClientService } from './http-client.service';

@Injectable({
  providedIn: 'root',
})
export abstract class AbstractService {
  private _lssConfig: LssConfig;
  private _httpClient: HttpClientService;
  private _apiSecure: string = '';
  private _apiPublic: string = '';
  constructor(private injector: Injector) {
    this._httpClient = injector.get<HttpClientService>(
      HttpClientService as Type<HttpClientService>
    );

    this._lssConfig = injector.get<LssConfig>(LssConfig as Type<LssConfig>);
  }

  get lssConfig() {
    return this._lssConfig;
  }

  get httpClient() {
    return this._httpClient;
  }

  apiSecure(): string {
    return this._lssConfig.apiBaseUrl + 'extsecure/loyaltyapi/1.0.0';
  }

  apiPublic(): string {
    return this._lssConfig.apiBaseUrl + 'public/loyaltyapi/1.0.0';
  }
}
