import { Injectable, Injector } from '@angular/core';
import { HttpHeaders, HttpParams, HttpClient } from '@angular/common/http';
import { Observable, of, catchError } from 'rxjs';
import { AbstractService } from './abstract.service';
import { SystemService } from './system.service';

@Injectable({
  providedIn: 'root'
})
export class AccountPoolService extends AbstractService {
  private apiPath = '/member/accountpool';
  private systemService: SystemService;
  private nativeHttp: HttpClient;
  
  constructor(injector: Injector) {
    super(injector);
    // Get access to SystemService
    this.systemService = injector.get(SystemService);
    // Get access to Angular's native HttpClient like in points-transfer.service.ts
    this.nativeHttp = injector.get(HttpClient);
  }

  /**
   * Create a new account pool
   */
  createPool(
    mpacc: string,
    lang: string,
    name: string,
    email: string,
    countryCode: string,
    telephone: string,
    split: number
  ): Observable<any> {
    const payload = {
      mpacc,
      lang,
      name,
      email,
      countryCode,
      telephone,
      split: 3272126 // Using hardcoded value as in the original code
    };

    // Match pattern exactly from points-transfer.service.ts
    let uniqueId = this.systemService.getUniqueId(
      this.lssConfig.apiIdKeyStart,
      this.lssConfig.apiIdKeyEnd,
      mpacc
    );
console.log('--------',  this.lssConfig.apiIdKeyStart, '   ', this.lssConfig.apiIdKeyEnd,'   ',     mpacc)
console.log('uniqueId', uniqueId)
    // Match header format exactly from points-transfer.service.ts
    const headers = {
      'Content-Type': 'application/json',
      'LP_APIID': this.lssConfig.apiId || '',
      'LP_UniqueId': uniqueId
    };
    
    // Match logging pattern from points-transfer.service.ts
    console.log('Using MemberProfile.uniqueId:', uniqueId);
    console.log('Headers:', headers);
    console.log('Body:', payload);
    
    // Use apiSecure instead of apiPublic for all endpoints
    return this.nativeHttp.post<any>(
      `${this.apiSecure()}${this.apiPath}/create`,
      payload,
      { headers }
    );
  }

  /**
   * Find a pool for the given account number
   */
  findPool(mpacc: string): Observable<any> {
    // Match pattern exactly from points-transfer.service.ts
    console.log('--------find ',  this.lssConfig.apiIdKeyStart, '   ', this.lssConfig.apiIdKeyEnd,'   ',     mpacc)
    let uniqueId = this.systemService.getUniqueId(
      this.lssConfig.apiIdKeyStart,
      this.lssConfig.apiIdKeyEnd,
      mpacc
    );
    
    // Match header format exactly from points-transfer.service.ts
    const headers = {
      'Content-Type': 'application/json',
      'LP_APIID': this.lssConfig.apiId || '',
      'LP_UniqueId': uniqueId
    };
    
    console.log('Using MemberProfile.uniqueId:', uniqueId);
    console.log('Headers:', headers);
    
    // Use apiSecure instead of apiPublic for all endpoints
    return this.nativeHttp.get<any>(
      `${this.apiSecure()}${this.apiPath}/find`,
      { 
        headers,
        params: { mpacc }
      }
    );
  }

  /**
   * Find a pool by its MPACC (pool account number)
   */
  findPoolByMpacc(poolMpacc: string): Observable<any> {
    console.log('=== FIND POOL BY MPACC DEBUG ===');
    console.log('Searching for pool with MPACC:', poolMpacc);
    
    // Generate unique ID for request
    let uniqueId = this.systemService.getUniqueId(
      this.lssConfig.apiIdKeyStart,
      this.lssConfig.apiIdKeyEnd,
      poolMpacc
    );
    
    // Match header format exactly from other methods
    const headers = {
      'Content-Type': 'application/json',
      'LP_APIID': this.lssConfig.apiId || '',
      'LP_UniqueId': uniqueId
    };
    
    console.log('Using unique ID:', uniqueId);
    console.log('Headers:', headers);
    
    // Use the find endpoint with pool MPACC
    return this.nativeHttp.get<any>(
      `${this.apiSecure()}${this.apiPath}/find`,
      { 
        headers,
        params: { mpacc: poolMpacc }
      }
    );
  }

  /**
   * Check if there's a pending pool invitation
   */
  checkInviteStatus(mpacc: string): Observable<any> {
    console.log('=== CHECK INVITE STATUS DEBUG ===');
    console.log('Checking invite status for mpacc:', mpacc);
    
    // Generate unique ID for request
    let uniqueId = this.systemService.getUniqueId(
      this.lssConfig.apiIdKeyStart,
      this.lssConfig.apiIdKeyEnd,
      mpacc
    );
    
    // Match header format exactly from other working methods
    const headers = {
      'Content-Type': 'application/json',
      'LP_APIID': this.lssConfig.apiId || '',
      'LP_UniqueId': uniqueId
    };
    
    console.log('Using MemberProfile.uniqueId:', uniqueId);
    console.log('Headers:', headers);
    
    // Use apiSecure instead of apiPublic for all endpoints
    return this.nativeHttp.get<any>(
      `${this.apiSecure()}${this.apiPath}/invite-status`,
      { 
        headers,
        params: { mpacc }
      }
    ).pipe(
      catchError(error => {
        console.error('Error checking invite status:', error);
        // Return a default response indicating no invite
        return of({ status: 'N' });
      })
    );
  }

  /**
   * Join or request to join a pool
   */
  joinPool(poolId: number, mpacc: string, action: 'INVT' | 'REQS', auditUser: string): Observable<any> {
    console.log('=== JOIN POOL API DEBUG ===');
    console.log('Method parameters:', {
      poolId,
      mpacc,
      action,
      auditUser
    });

    // Generate unique ID for request
    let uniqueId = this.systemService.getUniqueId(
      this.lssConfig.apiIdKeyStart,
      this.lssConfig.apiIdKeyEnd,
      auditUser
    );
    
    const fullUrl = `${this.apiSecure()}${this.apiPath}/join`;
    
    // Use form-encoded data format (like the working processPoolInvite method)
    const formData = new HttpParams()
      .set('poolId', poolId.toString())
      .set('mpacc', mpacc)
      .set('type', 'MEMB')
      .set('privacy', 'N')
      .set('action', action)
      .set('auditUser', auditUser);
    
    const headers = new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded',
      'LP_APIID': this.lssConfig.apiId || '',
      'LP_UniqueId': uniqueId
    });
    
    console.log('API Configuration:', {
      apiSecure: this.apiSecure(),
      apiPath: this.apiPath,
      fullUrl: fullUrl
    });
    console.log('Sending as form-encoded data:', formData.toString());
    console.log('Request Headers:', headers);
    
    // Send as form-encoded data (matching the working processPoolInvite pattern)
    return this.nativeHttp.post<any>(
      fullUrl,
      formData.toString(),
      { headers: headers }
    );
  }

  /**
   * Process pool invitation or membership
   */
  processPoolInvite(
    poolId: number | string, 
    mpacc: string, 
    type: 'ACCP' | 'JOIN' | 'REMV' | 'EXIT', 
    auditUser: string
  ): Observable<any> {
    console.log('=== PROCESS POOL INVITE DEBUG ===');
    console.log('Method called with parameters:', {
      poolId,
      mpacc,
      type,
      auditUser
    });
    
    // Validate parameters
    console.log('Parameter validation:');
    console.log('poolId is null/undefined?', poolId == null);
    console.log('mpacc is null/undefined?', mpacc == null);
    console.log('type is null/undefined?', type == null);
    console.log('auditUser is null/undefined?', auditUser == null);
    console.log('poolId value and type:', poolId, typeof poolId);
    console.log('mpacc value and type:', mpacc, typeof mpacc);

    // Use exactly the same approach as in points-transfer.service.ts
    let uniqueId = this.systemService.getUniqueId(
      this.lssConfig.apiIdKeyStart,
      this.lssConfig.apiIdKeyEnd,
      mpacc
    );

    const fullUrl = `${this.apiSecure()}${this.apiPath}/process-invite`;
    
    // Try form-encoded data first (like in joinPool method)
    // Ensure parameters are in the exact order expected by the SQL procedure
    const formData = new HttpParams()
      .set('poolId', poolId.toString())
      .set('mpacc', mpacc)
      .set('type', type)
      .set('auditUser', auditUser)
      .set('privacy', 'N'); // Privacy might need to be last
    
    const formHeaders = new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded',
      'LP_APIID': this.lssConfig.apiId || '',
      'LP_UniqueId': uniqueId
    });
    
    console.log('API Configuration:', {
      apiSecure: this.apiSecure(),
      apiPath: this.apiPath,
      fullUrl: fullUrl
    });
    console.log('Sending as form-encoded data:', formData.toString());
    console.log('Request Headers:', formHeaders);
    console.log('UniqueId generation:', {
      apiIdKeyStart: this.lssConfig.apiIdKeyStart,
      apiIdKeyEnd: this.lssConfig.apiIdKeyEnd,
      mpacc: mpacc,
      generatedUniqueId: uniqueId
    });

    // Send as form-encoded data (server expects this format)
    const formDataString = formData.toString();
    console.log('=== SENDING FORM-ENCODED REQUEST ===');
    console.log('URL:', fullUrl);
    console.log('Form data string:', formDataString);
    console.log('Form data includes privacy?', formDataString.includes('privacy=N'));
    console.log('Headers:', formHeaders);
    
    return this.nativeHttp.post<any>(
      fullUrl,
      formDataString,
      { headers: formHeaders }
    ).pipe(
      catchError(error => {
        console.error('=== PROCESS POOL INVITE ERROR ===');
        console.error('Error details:', {
          status: error?.status,
          statusText: error?.statusText,
          message: error?.message,
          error: error?.error,
          url: error?.url
        });
        console.error('Full error object:', error);
        
        // If form-encoded fails, try JSON as fallback
        console.log('Form-encoded failed, trying JSON format...');
        
        const payload = {
          poolId: poolId.toString(),
          mpacc,
          type,
          auditUser,
          privacy: 'N'
        };
        
        const jsonHeaders = {
          'Content-Type': 'application/json',
          'LP_APIID': this.lssConfig.apiId || '',
          'LP_UniqueId': uniqueId
        };
        
        console.log('JSON Payload:', payload);
        
        return this.nativeHttp.post<any>(
          fullUrl,
          payload,
          { headers: jsonHeaders }
        );
      })
    );
  }

  // Keep the getBaseUrl helper only
  private getBaseUrl(): string {
    return this.apiSecure();
  }
}
