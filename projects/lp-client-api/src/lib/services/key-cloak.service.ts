import { Injectable } from '@angular/core';
import Keycloak, {
  KeycloakConfig,
  KeycloakInitOptions,
} from 'node_modules/keycloak-lp-ionic';
import { KCUser } from '../types/keycloak-types';
import { BehaviorSubject, Observable, delay, of } from 'rxjs';
import { Router } from '@angular/router';
import { MemberProfile } from '../types/member';

@Injectable({
  providedIn: 'root',
})
export class KeyCloakService {
  public authStatus: BehaviorSubject<any> = new BehaviorSubject(null);
  public keycloak!: Keycloak;
  private user?: KCUser;
  public authSuccess: boolean = false;
  private internalStatus: boolean = false;
  private running: boolean = false;

  constructor(private _router: Router) {}

  init(initData: { config: KeycloakConfig; initOptions: KeycloakInitOptions }) {
    this.authStatus.next({ eventName: 'init', eventData: null });
    this.keycloak = new Keycloak(initData.config);
    
    this.keycloak.init(initData.initOptions).then((data) => {
      this.authStatus.next({ eventName: 'init-done', eventData: data });
    }).catch(e => {
      this.authStatus.next({ eventName: 'init-failed', eventData: e });
    });
    this.keycloak.onAuthSuccess = () => {
      this.user = this.keycloak?.tokenParsed;
      if (
        this.user?.LP_APIID === undefined ||
        this.user?.LP_MPACC === undefined
      ) {
        if (this.user != undefined) {
          this.user.mustRegister = true;
          localStorage.setItem('authurllocked', '/public/signup');
        }
      }

      this.authSuccess = true;
      this.internalStatus = true;
      this.authStatus.next({ eventName: 'login', eventData: this.user });

      let url: string | any = localStorage.getItem('authurllocked');
      localStorage.removeItem('authurllocked');
      if (url) {
        this._router.navigateByUrl(url);
      }
      console.log('Session authenticated');
    };

    this.keycloak.onAuthRefreshSuccess = () => {
      console.log('This is refresh');
      if (this.keycloak && this.keycloak.tokenParsed) {
        this.internalStatus = true;
        this.user = this.keycloak?.tokenParsed;
        this.authStatus.next({
          eventName: 'refresh',
          eventData: this.keycloak.tokenParsed.exp,
        });
      }
    };

    this.keycloak.onAuthRefreshError = () => {
      this.authSuccess = false;
      if (this.keycloak && this.keycloak.tokenParsed) {
        this.authStatus.next({
          eventName: 'refresh_error',
          eventData: this.keycloak.tokenParsed.exp,
        });
      }
    };

    this.keycloak.onAuthError = (errors) => {
      console.log('Authentication Errors:', errors);
    };

    this.keycloak.onAuthLogout = () => {
      this.user = undefined;
      this.authSuccess = false;
      this.internalStatus = false;
      this.authStatus.next({ eventName: 'logout', eventData: null });
      console.log('Session logged out');
    };

    this.keycloak.onTokenExpired = () => {
      //this.authSuccess = false;
      this.internalStatus = false;
      this.authStatus.next({ eventName: 'expired', eventData: null });
      console.log('Token expired');
    };
    this.keycloak.onReady = (data : boolean) => {
      this.authStatus.next({ eventName: 'init-ready', eventData: data });
    }
  }
  async authed(forceLoad?:boolean): Promise<boolean>{
    if (this.running){
      console.log('Already Running delay');
      forceLoad = false;
      delay(500);
    }
    this.running = true;
    if (this.internalStatus && !forceLoad){
      console.log('Status Good');
    }else if(this.keycloak?.refreshToken){
      console.log('Refresh Start');
      let work = await this.keycloak?.updateToken(60000);
      console.log('Refresh Done');
    }
    this.running = false;
    return this.internalStatus;
  }
  get userProfile(): KCUser | undefined {
    return this.user;
  }

  get memberProfile(): MemberProfile | undefined {
    return this.memberProfile;
  }

  get userId(): string {
    if (this.authSuccess) {
      return this.userProfile?.preferred_username;
    }
    return 'Not Set';
  }
  get lpUniueReference(): string {
    if (this.authSuccess && this.user && this.user.LP_MPACC) {
      return this.user.LP_MPACC;
    }
    return 'Not Set';
  }

  public login(): Promise<void>{
    return this.keycloak.login();
  }
  public changePassword(): Promise<void>{
    return this.keycloak.login({action:"UPDATE_PASSWORD"});
  }
  public lostPassword(): Promise<void>{
    return this.keycloak.login({action:"lost-password"});
  }
}

export interface KCAuthEvent {
  eventName: 'login' | 'refresh' | 'logout' | 'expired' | 'refresh_error';
  eventData: any;
}
