import {
  AbstractControl,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';

export class CustomValidators {
  public static passwordValidate(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;

      if (!value) {
        return null;
      }

      const isPasswordValid =
        /^(?=(.*[A-Za-z]){1})(?=.*\d{1})(?=.*?\W{1}).{8,16}$/.test(value);

      return !isPasswordValid
        ? {
            passwordInvalid: {
              message:
                'Please specify a password with a minimum of 6 characters and a maximum of 16 characters containing at least 1 lower case character, 1 upper case character, 1 numeric value and 1 special character.',
            },
          }
        : null;
    };
  }

  public static matchValue(
    source: string,
    sourceLabel: string,
    target: string,
    targetLabel: string
  ): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const sourceControl = (control as FormGroup).controls[source];
      const targetControl = (control as FormGroup).controls[target];

      if (!sourceControl || !targetControl) {
        return null;
      }

      if (targetControl.errors && !targetControl.errors['matchValue']) {
        return null;
      }

      if (sourceControl.value !== targetControl.value) {
        targetControl.setErrors({
          matchValue: { sourceLabel: sourceLabel, targetLabel: targetLabel },
        });
        return null;
      } else {
        targetControl.setErrors(null);
        return null;
      }
    };
  }

  public static atLeastOneRequired(components: string[]): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      let isValid = false;

      isValid = components.some(function (value) {
        let component = (control as FormGroup).controls[value];
        return (
          component.value !== undefined &&
          component.value !== null &&
          component.value !== ''
        );
      });

      return !isValid ? { atLeastOneRequired: true } : null;
    };
  }

  public static requiredIfValidator(dependentControlName: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.parent) {
        return null;
      }

      const dependentControl = control.parent.get(dependentControlName);

      if (!dependentControl) {
        throw new Error(
          `Could not find dependent control: ${dependentControlName}`
        );
      }

      // Check if dependent control has a value
      const isRequired = !!dependentControl.value;

      // If required and current control is empty, return error
      if (isRequired && !control.value?.trim()) {
        return { required: true };
      }

      return null;
    };
  }
}
