{"name": "base64-js", "version": "1.3.0", "description": "Base64 encoding/decoding in pure JS", "keywords": ["base64"], "homepage": "https://github.com/beatgammit/base64-js", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "license": "MIT", "author": "<PERSON><PERSON> <PERSON> Little <<EMAIL>>", "files": ["test", "index.js", "base64js.min.js"], "main": "index.js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | uglifyjs -m > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "devDependencies": {"benchmark": "^2.1.4", "browserify": "^14.0.0", "standard": "*", "tape": "4.x", "uglify-js": "^2.8.29"}}