{"name": "keycloak-lp-ionic", "version": "1.0.0", "description": "Keycloak adapter for Ionicframework with Capacitor support.", "main": "keycloak.js", "repository": {"type": "git", "url": "git+https://github.com/JohannesBauer97/keycloak-ionic.git"}, "keywords": ["capacitor", "adapter", "auth", "authentication", "oauth2", "openid", "keycloak"], "dependencies": {"base64-js": "1.3.0", "js-sha256": "0.9.0", "jwt-decode": "^3.1.2", "promise-polyfill": "8.1.3"}, "author": "<PERSON> | <PERSON>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/JohannesBauer97/keycloak-ionic/issues"}, "homepage": "https://github.com/JohannesBauer97/keycloak-ionic#readme"}