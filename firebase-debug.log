[debug] [2025-07-11T13:05:29.541Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-11T13:05:29.543Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-11T15:02:05.186Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-11T15:02:05.190Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-14T06:02:56.674Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-14T06:02:56.678Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-15T06:09:04.404Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-15T06:09:04.407Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-16T08:28:01.854Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-16T08:28:01.902Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-22T06:01:48.377Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-22T06:01:48.379Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-22T06:45:25.351Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-22T06:45:25.354Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-22T06:48:03.711Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-22T06:48:03.715Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-23T06:35:13.950Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-23T06:35:13.951Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-23T12:03:58.085Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-23T12:03:58.087Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-24T09:02:57.788Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-24T09:02:57.789Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-25T10:24:54.234Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-25T10:24:54.235Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-25T12:33:20.119Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-25T12:33:20.120Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-27T18:55:35.548Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-27T18:55:35.549Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-28T05:34:57.192Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-28T05:34:57.193Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-29T05:18:44.526Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-29T05:18:44.531Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-31T11:51:02.457Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-31T11:51:02.458Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-04T05:23:40.912Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-04T05:23:40.914Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-04T05:48:29.158Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-04T05:48:29.159Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-05T06:17:20.868Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-05T06:17:20.870Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-05T07:01:02.083Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-05T07:01:02.084Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-07T05:15:06.993Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-07T05:15:06.995Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-07T08:10:59.225Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-07T08:10:59.227Z] > authorizing via signed-in user (<EMAIL>)
