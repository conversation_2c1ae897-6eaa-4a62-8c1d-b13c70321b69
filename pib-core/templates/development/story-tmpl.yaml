template:
  id: pib-story-template-v3
  name: PIB Story Document
  version: 3.0
  methodology: PIB-METHOD
  output:
    format: markdown
    filename: docs/stories/{{epic_num}}.{{story_num}}.{{story_title_short}}.md
    title: "Story {{epic_num}}.{{story_num}}: {{story_title_short}}"

workflow:
  mode: interactive
  elicitation: advanced-elicitation
  agent_integration: true
  lever_compliance: true

agent_config:
  primary_agents: [pm, dev-agent, code-reviewer, qa-tester]
  editable_sections: 
    - Status
    - Story
    - Acceptance Criteria
    - Tasks / Subtasks
    - Dev Notes
    - Testing
    - LEVER Assessment
    - Change Log

sections:
  - id: status
    title: Status
    type: choice
    choices: [Draft, Approved, InProgress, CodeReview, QATesting, Review, Done]
    instruction: Select the current status of the story with PIB Method workflow stages
    owner: pm
    editors: [pm, dev-agent, code-reviewer, qa-tester]
    
  - id: story
    title: Story
    type: template-text
    template: |
      **As a** {{role}},
      **I want** {{action}},
      **so that** {{benefit}}
    instruction: Define the user story using the standard format with role, action, and benefit. Consider LEVER principles - does this story leverage existing functionality or extend current capabilities?
    elicit: true
    owner: pm
    editors: [pm]
    lever_consideration: true
    
  - id: acceptance-criteria
    title: Acceptance Criteria
    type: numbered-list
    instruction: Copy the acceptance criteria numbered list from the epic file. Ensure criteria are testable and support LEVER compliance validation.
    elicit: true
    owner: pm
    editors: [pm, dev-agent]
    lever_compliance: true
    
  - id: lever-assessment
    title: LEVER Framework Assessment
    instruction: |
      Document how this story applies LEVER principles throughout implementation.
    owner: dev-agent
    editors: [dev-agent, code-reviewer]
    sections:
      - id: leverage-analysis
        title: Leverage - Existing Code and Patterns
        instruction: Identify existing code, patterns, and libraries that can be leveraged
        type: bullet-list
      - id: extend-opportunities
        title: Extend - Extension Over Creation
        instruction: Identify opportunities to extend existing functionality rather than creating new
        type: bullet-list
      - id: verify-strategy
        title: Verify - Testing and Validation Plan
        instruction: Define comprehensive testing approach for this story
        type: bullet-list
      - id: eliminate-duplication
        title: Eliminate - Prevent Duplication
        instruction: Document how this story avoids or eliminates code duplication
        type: bullet-list
      - id: reduce-complexity
        title: Reduce - Complexity Minimization
        instruction: Explain how implementation minimizes complexity while meeting requirements
        type: bullet-list

  - id: tasks-subtasks
    title: Tasks / Subtasks
    type: numbered-list
    instruction: |
      Break down the story into specific tasks that can be executed by PIB Method agents. Each task should:
      - Be completable by a single agent in one focused session
      - Have clear deliverables and acceptance criteria
      - Support LEVER compliance validation
      - Include handoff points to other agents (dev-agent → code-reviewer → qa-tester)
    owner: dev-agent
    editors: [dev-agent]
    agent_assignment: true
    
  - id: technical-notes
    title: Technical Implementation Notes
    instruction: |
      Document technical implementation details, architectural decisions, and integration points.
      Include MCP tool requirements for specialized agents.
    owner: dev-agent
    editors: [dev-agent, architect]
    sections:
      - id: architecture-integration
        title: Architecture Integration Points
        instruction: How this story integrates with overall system architecture
      - id: mcp-requirements
        title: MCP Tool Requirements
        instruction: Specify MCP tools needed (Context7, Perplexity, Firecrawl, Playwright)
      - id: dependencies
        title: Dependencies and Prerequisites
        instruction: List technical dependencies and prerequisite work
      - id: integration-points
        title: Integration Points
        instruction: Document integration with other system components

  - id: dev-notes
    title: Development Notes
    type: free-text
    instruction: |
      Space for development team to capture implementation notes, decisions, and discoveries during development.
      Focus on LEVER compliance achievements and any deviations from planned approach.
    owner: dev-agent
    editors: [dev-agent, code-reviewer, change-implementer]
    
  - id: code-review
    title: Code Review
    instruction: |
      Code review results and feedback from the PIB Method code-reviewer agent.
    owner: code-reviewer
    editors: [code-reviewer, change-implementer]
    sections:
      - id: review-status
        title: Review Status
        type: choice
        choices: [NotStarted, InProgress, ChangesRequested, Approved]
        instruction: Current status of code review process
      - id: lever-score
        title: LEVER Compliance Score
        type: score
        scale: "1-5"
        instruction: Overall LEVER framework compliance score (minimum 4/5 required)
      - id: review-feedback
        title: Review Feedback
        type: free-text
        instruction: Detailed feedback from code review with specific suggestions
      - id: improvement-areas
        title: Areas for Improvement
        type: bullet-list
        instruction: Specific areas identified for improvement with LEVER impact

  - id: testing
    title: Testing
    instruction: |
      Testing approach and results from PIB Method qa-tester agent using Playwright MCP integration.
    owner: qa-tester
    editors: [qa-tester]
    mcp_integration: playwright
    sections:
      - id: test-strategy
        title: Test Strategy
        instruction: Overall testing approach for this story including unit, integration, and E2E testing
      - id: test-cases
        title: Test Cases
        type: numbered-list
        instruction: Specific test cases with expected results
      - id: automation-tests
        title: Automated Tests
        instruction: Automated test implementation using Playwright MCP tools
      - id: test-results
        title: Test Results
        type: table
        columns: [Test Case, Status, Result, Notes]
        instruction: Results of test execution with pass/fail status

  - id: pib-workflow
    title: PIB Method Workflow Status
    instruction: |
      Track progress through PIB Method agent workflow stages.
    sections:
      - id: agent-handoffs
        title: Agent Handoff Status
        type: table
        columns: [From Agent, To Agent, Status, Handoff Date, Notes]
        instruction: Track handoffs between PIB Method agents
      - id: quality-gates
        title: Quality Gate Status
        type: table
        columns: [Quality Gate, Status, Date, Score, Notes]
        instruction: Track quality gate validation results
      - id: workflow-metrics
        title: Workflow Metrics
        instruction: Track development efficiency and quality metrics

  - id: deployment
    title: Deployment
    instruction: |
      Deployment status and environment progression for this story.
    owner: dev-agent
    editors: [dev-agent, platform-engineer]
    sections:
      - id: deployment-status
        title: Deployment Status
        type: choice
        choices: [NotDeployed, Development, Staging, Production]
        instruction: Current deployment status
      - id: deployment-notes
        title: Deployment Notes
        instruction: Notes about deployment process and any issues encountered
      - id: environment-validation
        title: Environment Validation
        instruction: Validation results in each environment

  - id: change-log
    title: Change Log
    type: table
    columns: [Date, Version, Change, Author, Agent, LEVER Impact]
    instruction: Track all changes to this story document including which PIB Method agent made the change and LEVER impact
    owner: system
    editors: [pm, dev-agent, code-reviewer, qa-tester, change-implementer]