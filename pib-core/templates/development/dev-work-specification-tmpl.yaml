template:
  id: pib-dev-work-specification-v3
  name: PIB Development Work Specification
  version: 3.0
  methodology: PIB-METHOD
  output:
    format: markdown
    filename: docs/dev-work/{{work_item_id}}-dev-spec.md
    title: "Development Work Specification: {{work_item_title}}"

workflow:
  mode: structured
  agent_integration: true
  lever_compliance: true
  primary_agents: [dev-agent, code-reviewer, qa-tester]

sections:
  - id: work-overview
    title: Work Overview
    instruction: |
      Define the development work item with clear scope and deliverables.
    sections:
      - id: work-description
        title: Work Description
        instruction: Detailed description of the development work to be performed
      - id: acceptance-criteria
        title: Acceptance Criteria
        type: numbered-list
        instruction: Clear, testable acceptance criteria that define completion
      - id: business-value
        title: Business Value
        instruction: Explain the business value and impact of this work

  - id: lever-planning
    title: LEVER Framework Planning
    instruction: |
      Plan how this work will apply LEVER principles throughout implementation.
    sections:
      - id: leverage-analysis
        title: Leverage - Existing Code and Patterns
        type: bullet-list
        instruction: Identify existing code, patterns, and libraries that can be leveraged
      - id: extend-opportunities
        title: Extend - Extension Over Creation
        type: bullet-list
        instruction: Identify opportunities to extend existing functionality
      - id: verify-plan
        title: Verify - Testing and Validation Plan
        type: bullet-list
        instruction: Define comprehensive testing approach
      - id: eliminate-strategy
        title: Eliminate - Prevent Duplication
        type: bullet-list
        instruction: Strategies to avoid or eliminate code duplication
      - id: reduce-approach
        title: Reduce - Complexity Minimization
        type: bullet-list
        instruction: How implementation will minimize complexity

  - id: technical-specification
    title: Technical Specification
    instruction: |
      Define technical requirements and implementation details.
    sections:
      - id: technical-requirements
        title: Technical Requirements
        type: bullet-list
        instruction: Specific technical requirements and constraints
      - id: architecture-integration
        title: Architecture Integration
        instruction: How this work integrates with existing system architecture
      - id: dependencies
        title: Dependencies
        type: bullet-list
        instruction: Technical dependencies and prerequisites
      - id: api-changes
        title: API Changes (if applicable)
        instruction: Document any API changes or new endpoints required

  - id: implementation-approach
    title: Implementation Approach
    instruction: |
      Define the planned implementation approach and methodology.
    sections:
      - id: development-strategy
        title: Development Strategy
        instruction: High-level approach to implementing this work
      - id: code-organization
        title: Code Organization
        instruction: How new code will be organized and structured
      - id: integration-points
        title: Integration Points
        type: bullet-list
        instruction: Key integration points with existing systems
      - id: rollback-strategy
        title: Rollback Strategy
        instruction: Plan for rolling back changes if issues arise

  - id: testing-specification
    title: Testing Specification
    instruction: |
      Define comprehensive testing approach for this work item.
    pib_integration: qa-tester
    mcp_tools: [playwright]
    sections:
      - id: unit-tests
        title: Unit Testing Requirements
        type: bullet-list
        instruction: Unit tests to be implemented
      - id: integration-tests
        title: Integration Testing Requirements
        type: bullet-list
        instruction: Integration tests to be implemented
      - id: e2e-tests
        title: End-to-End Testing Requirements
        type: bullet-list
        instruction: E2E tests using Playwright MCP integration
      - id: performance-tests
        title: Performance Testing Requirements
        type: bullet-list
        instruction: Performance testing requirements and benchmarks

  - id: quality-gates
    title: Quality Gates and Reviews
    instruction: |
      Define quality gates and review requirements for this work.
    sections:
      - id: code-review-criteria
        title: Code Review Criteria
        type: bullet-list
        instruction: Specific criteria for code review approval
      - id: lever-compliance-targets
        title: LEVER Compliance Targets
        instruction: Minimum LEVER compliance scores and requirements (minimum 4/5)
      - id: performance-criteria
        title: Performance Criteria
        type: bullet-list
        instruction: Performance benchmarks that must be met
      - id: security-requirements
        title: Security Requirements
        type: bullet-list
        instruction: Security requirements and validation criteria

  - id: delivery-timeline
    title: Delivery Timeline and Milestones
    instruction: |
      Define delivery timeline and key milestones.
    sections:
      - id: implementation-phases
        title: Implementation Phases
        type: table
        columns: [Phase, Description, Agent, Duration, Deliverables]
        instruction: Break down implementation into phases with agent assignments
      - id: key-milestones
        title: Key Milestones
        type: table
        columns: [Milestone, Date, Criteria, Validation]
        instruction: Define key milestones with validation criteria
      - id: dependencies-timeline
        title: Dependencies Timeline
        instruction: Timeline for resolving dependencies and prerequisites

  - id: agent-coordination
    title: PIB Agent Coordination
    instruction: |
      Define how PIB Method agents will coordinate on this work.
    sections:
      - id: agent-assignments
        title: Agent Assignments
        type: table
        columns: [Agent, Role, Responsibilities, MCP Tools]
        instruction: Assign specific agents to work phases
      - id: handoff-points
        title: Handoff Points
        type: table
        columns: [From Agent, To Agent, Deliverables, Criteria]
        instruction: Define clear handoff points between agents
      - id: collaboration-protocols
        title: Collaboration Protocols
        instruction: How agents will collaborate and communicate during work

  - id: risk-mitigation
    title: Risk Assessment and Mitigation
    instruction: |
      Identify risks and mitigation strategies for this work.
    sections:
      - id: technical-risks
        title: Technical Risks
        type: table
        columns: [Risk, Probability, Impact, Mitigation, Owner]
        instruction: Identify and plan mitigation for technical risks
      - id: schedule-risks
        title: Schedule Risks
        type: table
        columns: [Risk, Probability, Impact, Mitigation, Owner]
        instruction: Identify and plan mitigation for schedule risks
      - id: quality-risks
        title: Quality Risks
        type: table
        columns: [Risk, Probability, Impact, Mitigation, Owner]
        instruction: Identify and plan mitigation for quality risks

  - id: success-criteria
    title: Success Criteria and Validation
    instruction: |
      Define clear success criteria and validation approaches.
    sections:
      - id: functional-success
        title: Functional Success Criteria
        type: bullet-list
        instruction: Functional requirements that must be met for success
      - id: quality-success
        title: Quality Success Criteria
        type: bullet-list
        instruction: Quality metrics and LEVER compliance requirements
      - id: performance-success
        title: Performance Success Criteria
        type: bullet-list
        instruction: Performance benchmarks that define success
      - id: validation-approach
        title: Validation Approach
        instruction: How success will be validated and measured

  - id: change-log
    title: Change Log
    type: table
    columns: [Date, Version, Change, Author, Agent, LEVER Impact]
    instruction: Track all changes to this specification including agent involvement