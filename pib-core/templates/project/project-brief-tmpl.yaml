template:
  id: pib-project-brief-template-v3
  name: PIB Project Brief
  version: 3.0
  methodology: PIB-METHOD
  output:
    format: markdown
    filename: docs/project-brief.md
    title: "{{project_name}} Project Brief"

workflow:
  mode: interactive
  elicitation: basic-elicitation
  agent_integration: true
  lever_compliance: true

sections:
  - id: introduction
    title: Introduction / Problem Statement
    instruction: |
      Describe the core idea, the problem being solved, or the opportunity being addressed. Why is this project needed?
      Apply Q-LEVER questioning - what assumptions are we making about the problem space?
    elicit: true
    lever_consideration: true

  - id: vision-goals
    title: Vision & Goals
    instruction: |
      Define the high-level vision and specific goals for the project.
    elicit: true
    sections:
      - id: vision
        title: Vision
        instruction: Describe the high-level desired future state or impact of this project
      - id: primary-goals
        title: Primary Goals
        type: bullet-list
        instruction: List 2-5 specific, measurable, achievable, relevant, time-bound (SMART) goals for the Minimum Viable Product (MVP). Consider LEVER principles - can we leverage existing solutions to achieve these goals?
        lever_assessment: true
      - id: success-metrics
        title: Success Metrics (Initial Ideas)
        type: bullet-list
        instruction: How will we measure if the project/MVP is successful? List potential KPIs that can verify our assumptions

  - id: target-audience
    title: Target Audience / Users
    instruction: |
      Describe the primary users of this product/system. Who are they? What are their key characteristics or needs relevant to this project?
      Consider existing user bases that could be leveraged or extended.
    elicit: true
    lever_consideration: true

  - id: mvp-scope
    title: Key Features / Scope (High-Level Ideas for MVP)
    instruction: |
      List the core functionalities or features envisioned for the MVP. Keep this high-level; details will go in the PRD/Epics.
      Apply LEVER principles - identify features that can leverage existing systems or extend current capabilities.
    elicit: true
    type: bullet-list
    lever_assessment: true

  - id: post-mvp-scope
    title: Post MVP Features / Scope and Ideas
    instruction: |
      List the core functionalities or features envisioned as potential for POST MVP. Keep this high-level; details will go in the PRD/Epics/Architecture.
      Consider how these features can extend the MVP foundation.
    elicit: true
    type: bullet-list
    lever_consideration: true

  - id: technical-constraints
    title: Known Technical Constraints or Preferences
    instruction: |
      Document technical constraints, preferences, and initial architectural thoughts.
      Consider how to leverage existing technical infrastructure and reduce complexity.
    elicit: true
    sections:
      - id: constraints
        title: Constraints
        type: bullet-list
        instruction: List any known limitations and technical mandates or preferences - e.g., budget, timeline, specific technology mandates, required integrations, compliance needs
      - id: architectural-preferences
        title: Initial Architectural Preferences
        instruction: Capture any early thoughts or strong preferences regarding repository structure (e.g., monorepo, polyrepo) and overall service architecture (e.g., monolith, microservices, serverless components). Consider LEVER principles - what existing architectures can be leveraged or extended?
        lever_consideration: true
      - id: risks
        title: Risks
        type: bullet-list
        instruction: Identify potential risks - e.g., technical challenges, resource availability, market acceptance, dependencies
      - id: user-preferences
        title: User Preferences
        type: bullet-list
        instruction: Any specific requests from the user that are not a high level feature that could direct technology or library choices, or anything else that came up in the brainstorming

  - id: lever-preliminary-assessment
    title: Preliminary LEVER Assessment
    instruction: |
      Initial assessment of how this project can apply LEVER principles.
    sections:
      - id: leverage-opportunities
        title: Leverage Opportunities
        type: bullet-list
        instruction: Identify existing systems, platforms, technologies, or patterns that could be leveraged
      - id: extension-possibilities
        title: Extension Possibilities
        type: bullet-list
        instruction: Identify ways to extend existing solutions rather than building from scratch
      - id: verification-strategy
        title: Verification Strategy
        instruction: How will we verify that our solution meets user needs and technical requirements?
      - id: duplication-risks
        title: Duplication Risks
        type: bullet-list
        instruction: Identify potential areas where we might duplicate existing functionality
      - id: complexity-concerns
        title: Complexity Concerns
        type: bullet-list
        instruction: Identify areas where complexity could be reduced while maintaining functionality

  - id: pib-integration
    title: PIB Method Integration
    instruction: |
      Document how this project will integrate with PIB Method workflows and agents.
    sections:
      - id: agent-involvement
        title: Expected Agent Involvement
        instruction: Identify which PIB Method agents are likely to be involved (analyst, architect, dev-agent, etc.)
      - id: mcp-requirements
        title: Potential MCP Tool Requirements
        instruction: Identify potential MCP tool needs (Context7, Perplexity, Firecrawl, Playwright)
      - id: workflow-considerations
        title: Workflow Considerations
        instruction: Any special workflow considerations for this project type

  - id: relevant-research
    title: Relevant Research (Optional)
    instruction: |
      Link to or summarize findings from any initial research conducted (e.g., analyst agent research reports).
      This section can be populated by the PIB Method analyst agent using Perplexity and Firecrawl MCP tools.
    optional: true
    agent_support: analyst

  - id: next-steps
    title: Next Steps and Agent Prompts
    sections:
      - id: pm-prompt
        title: PM Agent Prompt
        content: |
          This Project Brief provides the full context for {{project_name}}. Please start in 'PRD Generation Mode', review the brief thoroughly to work with the user to create the PIB Method PRD section by section, asking for any necessary clarification or suggesting improvements while applying LEVER framework principles throughout.
          
          Key considerations:
          - Apply Q-LEVER questioning throughout PRD development
          - Identify opportunities to leverage existing solutions
          - Design for extension over creation
          - Plan comprehensive verification strategies
          - Eliminate potential duplication
          - Reduce complexity while meeting requirements
      - id: analyst-prompt
        title: Analyst Agent Prompt (if research needed)
        content: |
          Based on this Project Brief for {{project_name}}, please conduct comprehensive research using Perplexity and Firecrawl MCP tools to:
          
          - Research existing solutions in this problem space
          - Identify competitive landscape and differentiation opportunities
          - Validate market assumptions and user needs
          - Research technical approaches and best practices
          - Update the "Relevant Research" section with findings
          
          Focus on findings that support LEVER framework application - existing solutions to leverage, extension opportunities, and complexity reduction strategies.

  - id: change-log
    title: Change Log
    type: table
    columns: [Date, Version, Description, Author, LEVER Impact]
    instruction: Track document versions and changes with LEVER framework impact assessment