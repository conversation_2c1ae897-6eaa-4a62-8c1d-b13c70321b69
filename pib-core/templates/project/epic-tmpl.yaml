id: epic
name: Epic Template
description: Template for creating comprehensive epics with goals, requirements, and success criteria
type: project
version: "1.0.0"

metadata:
  author: PIB Method System
  created: 2025-01-27
  tags:
    - epic
    - requirements
    - planning
    - lever

structure:
  - id: header
    title: Epic Header
    sections:
      - id: title
        title: Epic Title
        content: |
          # Epic: {{epic_title}}
          
  - id: overview
    title: Overview
    sections:
      - id: overview-content
        content: |
          ## Overview
          {{epic_overview}}
          
  - id: goals
    title: Goals
    sections:
      - id: goals-content
        content: |
          ## Goals
          - **Primary Goal**: {{primary_goal}}
          - **Secondary Goal**: {{secondary_goal}}
          - **Business Goal**: {{business_goal}}
          
  - id: success-criteria
    title: Success Criteria
    sections:
      - id: success-content
        content: |
          ## Success Criteria
          {{#each success_criteria}}
          - [ ] {{this}}
          {{/each}}
          
  - id: requirements
    title: Requirements
    sections:
      - id: core-functionality
        title: Core Functionality
        content: |
          ## Requirements
          
          ### Core Functionality
          {{#each core_functionality}}
          {{@index}}. **{{this.name}}**
          {{#each this.features}}
             - {{this}}
          {{/each}}
          
          {{/each}}
          
      - id: technical-requirements
        title: Technical Requirements
        content: |
          ### Technical Requirements
          {{#each technical_requirements}}
          - **{{this.category}}**: {{this.requirement}}
          {{/each}}
          
  - id: constraints
    title: Constraints & Assumptions
    sections:
      - id: constraints-content
        content: |
          ## Constraints & Assumptions
          
          ### Constraints
          {{#each constraints}}
          - {{this}}
          {{/each}}
          
          ### Assumptions
          {{#each assumptions}}
          - {{this}}
          {{/each}}
          
  - id: dependencies
    title: Dependencies
    sections:
      - id: dependencies-content
        content: |
          ## Dependencies
          {{#each dependencies}}
          - {{this}}
          {{/each}}
          
  - id: risks
    title: Risks & Mitigation
    sections:
      - id: risks-content
        content: |
          ## Risks & Mitigation
          {{#each risks}}
          - **Risk**: {{this.risk}}
            - **Impact**: {{this.impact}}
            - **Mitigation**: {{this.mitigation}}
          {{/each}}
          
  - id: lever-application
    title: LEVER Framework Application
    sections:
      - id: lever-content
        content: |
          ## LEVER Framework Application
          
          ### Leverage
          - {{leverage_opportunities}}
          
          ### Extend
          - {{extension_points}}
          
          ### Verify
          - {{verification_strategy}}
          
          ### Eliminate
          - {{duplication_to_eliminate}}
          
          ### Reduce
          - {{complexity_reduction}}
          
  - id: footer
    title: Epic Footer
    sections:
      - id: references
        content: |
          ## Related Documents
          - PRD: `docs/prd.md`
          - Architecture: `docs/architecture.md`
          - Stories: `docs/stories/` (to be generated)

output:
  format: markdown
  filename: docs/epics/{{epic_name}}-epic.md
  title: "{{epic_title}} Epic"