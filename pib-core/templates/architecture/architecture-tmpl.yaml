template:
  id: pib-architecture-template-v3
  name: PIB Architecture Document
  version: 3.0
  methodology: PIB-METHOD
  output:
    format: markdown
    filename: docs/architecture.md
    title: "{{project_name}} Architecture Document"

workflow:
  mode: interactive
  elicitation: advanced-elicitation
  agent_integration: true
  lever_compliance: true
  mcp_tools:
    - context7  # For technical documentation and patterns

sections:
  - id: introduction
    title: Introduction
    instruction: |
      If available, review any provided relevant documents to gather all relevant context before beginning. If at a minimum you cannot locate docs/prd.md ask the user what docs will provide the basis for the architecture. Apply LEVER principles throughout architecture planning.
    sections:
      - id: intro-content
        content: |
          This document outlines the overall project architecture for {{project_name}}, including backend systems, shared services, and non-UI specific concerns. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development, ensuring consistency and adherence to chosen patterns and technologies while following PIB Method LEVER framework principles.
          
          **PIB Method Integration:**
          This architecture is designed for implementation by PIB Method agents with orchestrated workflows, quality gates, and LEVER framework compliance.
          
          **Relationship to Frontend Architecture:**
          If the project includes a significant user interface, a separate Frontend Architecture Document will detail the frontend-specific design and MUST be used in conjunction with this document. Core technology stack choices documented herein (see "Tech Stack") are definitive for the entire project, including any frontend components.
      - id: starter-template
        title: Starter Template or Existing Project
        instruction: |
          Before proceeding further with architecture design, check if the project is based on a starter template or existing codebase. Apply LEVER principles - leverage existing patterns, extend current capabilities:
          
          1. Review the PRD and brainstorming brief for any mentions of:
          - Starter templates (e.g., Create React App, Next.js, Vue CLI, Angular CLI, etc.)
          - Existing projects or codebases being used as a foundation
          - Boilerplate projects or scaffolding tools
          - Previous projects to be cloned or adapted
          
          2. If a starter template or existing project is mentioned:
          - Ask the user to provide access via one of these methods:
            - Link to the starter template documentation
            - Upload/attach the project files (for small projects)
            - Share a link to the project repository (GitHub, GitLab, etc.)
          - Use Context7 MCP to research template documentation and best practices
          - Analyze the starter/existing project to understand:
            - Pre-configured technology stack and versions
            - Project structure and organization patterns
            - Built-in scripts and tooling
            - Existing architectural patterns and conventions
            - Any limitations or constraints imposed by the starter
          - Use this analysis to inform and align your architecture decisions
          - Document how the architecture leverages and extends the existing foundation
          
          3. If no starter template is mentioned but this is a greenfield project:
          - Research best practices using Context7 for the chosen technology stack
          - Document rationale for architectural choices
          - Design for LEVER compliance from the start

  - id: lever-assessment
    title: LEVER Framework Assessment
    instruction: |
      Document how this architecture applies LEVER principles throughout the system design.
    sections:
      - id: leverage-opportunities
        title: Leverage - Existing Patterns and Technologies
        instruction: Document what existing patterns, frameworks, libraries, and services will be leveraged
      - id: extend-strategies
        title: Extend - Extension Over Creation
        instruction: Identify opportunities to extend existing systems rather than building from scratch
      - id: verify-approach
        title: Verify - Testing and Validation Strategy
        instruction: Define comprehensive testing and validation approaches integrated into the architecture
      - id: eliminate-duplication
        title: Eliminate - Duplication Prevention
        instruction: Document strategies to prevent code duplication and redundant systems
      - id: reduce-complexity
        title: Reduce - Complexity Minimization
        instruction: Explain how the architecture minimizes complexity while meeting requirements

  - id: tech-stack
    title: Technology Stack
    instruction: |
      Document the complete technology stack with specific versions where critical. Use Context7 to research current best practices and compatibility requirements. Apply LEVER principles - leverage proven technologies, extend existing tool chains.
    elicit: true
    context7_research: true
    sections:
      - id: backend-stack
        title: Backend Technology Stack
        instruction: List all backend technologies with versions and rationale
      - id: frontend-stack
        title: Frontend Technology Stack
        instruction: List all frontend technologies with versions and rationale (if applicable)
      - id: database-stack
        title: Database and Storage
        instruction: Document database choices, storage solutions, and data management approaches
      - id: infrastructure-stack
        title: Infrastructure and Deployment
        instruction: Document deployment platform, containerization, CI/CD, and infrastructure choices
      - id: development-tools
        title: Development and Testing Tools
        instruction: Document development environment, testing frameworks, and developer tooling

  - id: system-architecture
    title: System Architecture
    instruction: |
      Define the high-level system architecture with clear component relationships. Use mermaid diagrams for visual representation. Apply LEVER principles throughout design.
    sections:
      - id: architectural-overview
        title: Architectural Overview
        instruction: Provide high-level system overview with mermaid diagram
        mermaid_required: true
      - id: component-architecture
        title: Component Architecture
        instruction: Detail major system components and their responsibilities
      - id: data-flow
        title: Data Flow Architecture
        instruction: Document how data flows through the system with mermaid diagram
        mermaid_required: true
      - id: integration-points
        title: Integration Points
        instruction: Document external integrations and API connections

  - id: implementation-guidance
    title: Implementation Guidance for PIB Agents
    instruction: |
      Provide specific guidance for PIB Method agents implementing this architecture.
    sections:
      - id: agent-coordination
        title: Agent Implementation Coordination
        instruction: Define how different PIB agents should coordinate on implementation
      - id: development-phases
        title: Development Phases and Handoffs
        instruction: Document phases of development and agent handoff points
      - id: quality-gates
        title: Quality Gates and LEVER Compliance
        instruction: Define quality gates and LEVER compliance checkpoints
      - id: mcp-tool-requirements
        title: MCP Tool Requirements
        instruction: Specify MCP tool requirements for different implementation phases

  - id: security-architecture
    title: Security Architecture
    instruction: |
      Document security considerations and implementation requirements. Research security best practices using Context7.
    context7_research: true
    sections:
      - id: authentication
        title: Authentication and Authorization
        instruction: Document authentication and authorization strategies
      - id: data-security
        title: Data Security and Privacy
        instruction: Document data protection, encryption, and privacy measures
      - id: api-security
        title: API Security
        instruction: Document API security patterns and requirements
      - id: infrastructure-security
        title: Infrastructure Security
        instruction: Document infrastructure security measures and compliance

  - id: performance-scalability
    title: Performance and Scalability
    instruction: |
      Document performance requirements and scalability strategies. Research performance patterns using Context7.
    context7_research: true
    sections:
      - id: performance-requirements
        title: Performance Requirements
        instruction: Document specific performance requirements and targets
      - id: scalability-strategy
        title: Scalability Strategy
        instruction: Document horizontal and vertical scaling approaches
      - id: caching-strategy
        title: Caching Strategy
        instruction: Document caching layers and strategies
      - id: monitoring-observability
        title: Monitoring and Observability
        instruction: Document monitoring, logging, and observability requirements

  - id: data-architecture
    title: Data Architecture
    instruction: |
      Document data models, storage strategies, and data management approaches. Apply LEVER principles to data design.
    sections:
      - id: data-models
        title: Data Models
        instruction: Document core data models and relationships with ERD diagrams
        mermaid_required: true
      - id: database-design
        title: Database Design
        instruction: Document database schema, indexing strategy, and optimization approaches
      - id: data-flow
        title: Data Flow and Processing
        instruction: Document data processing pipelines and transformation logic
      - id: backup-recovery
        title: Backup and Recovery
        instruction: Document data backup, recovery, and disaster recovery strategies

  - id: deployment-operations
    title: Deployment and Operations
    instruction: |
      Document deployment strategies, operational procedures, and maintenance approaches.
    sections:
      - id: deployment-strategy
        title: Deployment Strategy
        instruction: Document deployment approaches, environments, and release procedures
      - id: cicd-pipeline
        title: CI/CD Pipeline
        instruction: Document continuous integration and deployment pipeline design
      - id: environment-management
        title: Environment Management
        instruction: Document development, staging, and production environment strategies
      - id: operational-procedures
        title: Operational Procedures
        instruction: Document operational procedures, maintenance, and support requirements

  - id: pib-integration
    title: PIB Method Integration Details
    instruction: |
      Document specific integration points with PIB Method workflows and tooling.
    sections:
      - id: workflow-integration
        title: Workflow Integration
        instruction: Document how architecture supports PIB Method workflows
      - id: agent-handoffs
        title: Agent Handoff Points
        instruction: Define clear handoff points between PIB Method agents
      - id: quality-validation
        title: Quality Validation Checkpoints
        instruction: Define architecture validation and quality checkpoints
      - id: lever-compliance-monitoring
        title: LEVER Compliance Monitoring
        instruction: Document how LEVER compliance will be monitored and enforced

  - id: risk-mitigation
    title: Risk Assessment and Mitigation
    instruction: |
      Identify potential risks and mitigation strategies for the architecture.
    sections:
      - id: technical-risks
        title: Technical Risks
        instruction: Identify technical risks and mitigation strategies
      - id: scalability-risks
        title: Scalability Risks
        instruction: Identify scalability bottlenecks and mitigation approaches
      - id: security-risks
        title: Security Risks
        instruction: Identify security risks and mitigation measures
      - id: operational-risks
        title: Operational Risks
        instruction: Identify operational risks and contingency plans

  - id: implementation-roadmap
    title: Implementation Roadmap
    instruction: |
      Define the implementation roadmap aligned with PIB Method development cycles.
    sections:
      - id: phase-planning
        title: Implementation Phases
        instruction: Break down implementation into manageable phases with LEVER compliance goals
      - id: dependency-management
        title: Dependency Management
        instruction: Document implementation dependencies and critical path
      - id: milestone-definition
        title: Milestone Definition
        instruction: Define clear milestones with validation criteria
      - id: success-metrics
        title: Success Metrics
        instruction: Define metrics for measuring implementation success and LEVER compliance

  - id: appendices
    title: Appendices
    sections:
      - id: glossary
        title: Glossary
        instruction: Define technical terms and acronyms used in the architecture
      - id: references
        title: References and Resources
        instruction: List all external references, documentation, and resources used
      - id: context7-research
        title: Context7 Research Summary
        instruction: Summarize key findings from Context7 research that informed architectural decisions
      - id: change-log
        title: Change Log
        type: table
        columns: [Date, Version, Description, Author, LEVER Impact]
        instruction: Track document versions and changes with LEVER impact assessment