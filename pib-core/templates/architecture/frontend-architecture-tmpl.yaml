template:
  id: pib-frontend-architecture-template-v3
  name: PIB Frontend Architecture Document
  version: 3.0
  methodology: PIB-METHOD
  output:
    format: markdown
    filename: docs/frontend-architecture.md
    title: "{{project_name}} Frontend Architecture Document"

workflow:
  mode: interactive
  elicitation: advanced-elicitation
  agent_integration: true
  lever_compliance: true
  mcp_tools:
    - context7  # For frontend framework documentation and patterns

sections:
  - id: introduction
    title: Introduction
    instruction: |
      Review provided documents including PRD, UX-UI Specification, and main Architecture Document. Focus on extracting technical implementation details needed for AI frontend development agents. Apply LEVER principles throughout frontend architecture planning.
    sections:
      - id: intro-content
        content: |
          This document outlines the frontend architecture for {{project_name}}, including UI components, state management, routing, and user interface patterns. This architecture is designed for implementation by PIB Method agents with LEVER framework compliance and comprehensive quality gates.
          
          **PIB Method Integration:**
          This frontend architecture supports PIB Method agent workflows, including dev-agent implementation, code-reviewer validation, and qa-tester browser automation using Playwright MCP integration.
      - id: template-framework-selection
        title: Template and Framework Selection
        instruction: |
          Before proceeding with frontend architecture design, check if the project is using a frontend starter template or existing codebase. Apply LEVER principles - leverage existing templates and extend proven patterns:
          
          1. Review the PRD, main architecture document, and brainstorming brief for mentions of:
             - Frontend starter templates (e.g., Create React App, Next.js, Vite, Vue CLI, Angular CLI, etc.)
             - UI kit or component library starters
             - Existing frontend projects being used as a foundation
             - Admin dashboard templates or other specialized starters
             - Design system implementations
          
          2. If a frontend starter template or existing project is mentioned:
             - Ask the user to provide access via link, repository, or documentation
             - Use Context7 MCP to research template documentation and best practices
             - Analyze the template/existing project to understand patterns and conventions
             - Document how the architecture leverages and extends the existing foundation
          
          3. If no starter template is mentioned:
             - Research frontend best practices using Context7 for the chosen technology stack
             - Design for LEVER compliance from the start
             - Document rationale for framework and tooling choices

  - id: lever-assessment
    title: LEVER Framework Assessment for Frontend
    instruction: |
      Document how this frontend architecture applies LEVER principles throughout UI development.
    sections:
      - id: leverage-frontend
        title: Leverage - UI Patterns and Components
        instruction: Document existing UI patterns, component libraries, and design systems to leverage
      - id: extend-ui
        title: Extend - Component and Pattern Extension
        instruction: Identify opportunities to extend existing UI components and patterns
      - id: verify-ui
        title: Verify - Frontend Testing Strategy
        instruction: Define comprehensive frontend testing using Playwright MCP integration
      - id: eliminate-ui-duplication
        title: Eliminate - UI Component Duplication
        instruction: Document strategies to prevent UI component and style duplication
      - id: reduce-ui-complexity
        title: Reduce - Frontend Complexity Minimization
        instruction: Explain how frontend architecture minimizes complexity while delivering rich UX

  - id: technology-stack
    title: Frontend Technology Stack
    instruction: |
      Document the complete frontend technology stack with specific versions. Use Context7 to research current best practices and compatibility requirements.
    context7_research: true
    sections:
      - id: core-framework
        title: Core Frontend Framework
        instruction: Document primary frontend framework (React, Vue, Angular, etc.) with version and rationale
      - id: build-tools
        title: Build Tools and Bundlers
        instruction: Document build tools, bundlers, and development server configuration
      - id: ui-component-library
        title: UI Component Library
        instruction: Document chosen UI component library or design system
      - id: styling-approach
        title: Styling and Theming
        instruction: Document CSS framework, styling approach, and theming strategy
      - id: state-management
        title: State Management
        instruction: Document state management solution and data flow patterns
      - id: routing
        title: Routing and Navigation
        instruction: Document routing solution and navigation patterns

  - id: component-architecture
    title: Component Architecture
    instruction: |
      Define the component architecture and organization patterns. Apply LEVER principles to component design.
    sections:
      - id: component-hierarchy
        title: Component Hierarchy
        instruction: Document component organization and hierarchy with mermaid diagram
        mermaid_required: true
      - id: design-patterns
        title: Design Patterns
        instruction: Document frontend design patterns and architectural decisions
      - id: component-standards
        title: Component Standards
        instruction: Define standards for component development, naming, and organization
      - id: reusability-strategy
        title: Component Reusability Strategy
        instruction: Document how components are designed for maximum reuse and extension

  - id: state-management-architecture
    title: State Management Architecture
    instruction: |
      Document state management patterns and data flow architecture.
    sections:
      - id: state-structure
        title: State Structure
        instruction: Document application state structure and organization
      - id: data-flow
        title: Data Flow Patterns
        instruction: Document data flow patterns with mermaid diagram
        mermaid_required: true
      - id: api-integration
        title: API Integration
        instruction: Document how frontend integrates with backend APIs
      - id: local-state-management
        title: Local State Management
        instruction: Document patterns for component-level state management

  - id: ui-ux-implementation
    title: UI/UX Implementation Guidelines
    instruction: |
      Document how UI/UX specifications are implemented in code.
    sections:
      - id: design-system-integration
        title: Design System Integration
        instruction: How design system tokens and components are implemented
      - id: responsive-design
        title: Responsive Design Implementation
        instruction: Document responsive design patterns and breakpoint strategies
      - id: accessibility-implementation
        title: Accessibility Implementation
        instruction: Document accessibility implementation patterns and standards
      - id: animation-interactions
        title: Animations and Interactions
        instruction: Document animation libraries and interaction patterns

  - id: testing-strategy
    title: Frontend Testing Strategy
    instruction: |
      Document comprehensive frontend testing approach using PIB Method qa-tester agent with Playwright MCP integration.
    pib_integration: qa-tester
    mcp_tools: [playwright]
    sections:
      - id: unit-testing
        title: Component Unit Testing
        instruction: Document unit testing approach for components and utilities
      - id: integration-testing
        title: Integration Testing
        instruction: Document integration testing patterns for component interactions
      - id: e2e-testing
        title: End-to-End Testing with Playwright
        instruction: Document E2E testing strategy using Playwright MCP integration
      - id: visual-testing
        title: Visual Regression Testing
        instruction: Document visual testing approach using Playwright screenshot capabilities
      - id: accessibility-testing
        title: Accessibility Testing
        instruction: Document automated accessibility testing integration

  - id: performance-optimization
    title: Frontend Performance Optimization
    instruction: |
      Document frontend performance optimization strategies and implementation.
    context7_research: true
    sections:
      - id: bundle-optimization
        title: Bundle Size Optimization
        instruction: Document code splitting, lazy loading, and bundle optimization strategies
      - id: rendering-optimization
        title: Rendering Performance
        instruction: Document rendering optimization patterns and performance monitoring
      - id: asset-optimization
        title: Asset Optimization
        instruction: Document image, font, and other asset optimization strategies
      - id: caching-strategy
        title: Frontend Caching Strategy
        instruction: Document client-side caching and offline strategies

  - id: development-workflow
    title: Development Workflow and PIB Integration
    instruction: |
      Document development workflow integration with PIB Method agents.
    sections:
      - id: dev-environment
        title: Development Environment Setup
        instruction: Document local development environment and tooling setup
      - id: pib-agent-integration
        title: PIB Agent Integration
        instruction: How PIB Method agents (dev-agent, code-reviewer, qa-tester) work with frontend code
      - id: quality-gates
        title: Frontend Quality Gates
        instruction: Define quality gates specific to frontend development with LEVER compliance
      - id: deployment-pipeline
        title: Frontend Deployment Pipeline
        instruction: Document frontend build and deployment pipeline integration

  - id: security-considerations
    title: Frontend Security
    instruction: |
      Document frontend security considerations and implementation patterns.
    context7_research: true
    sections:
      - id: authentication-ui
        title: Authentication UI Patterns
        instruction: Document authentication and authorization UI implementation
      - id: data-security
        title: Client-Side Data Security
        instruction: Document secure data handling in frontend code
      - id: xss-prevention
        title: XSS Prevention
        instruction: Document XSS prevention strategies and implementation
      - id: secure-communication
        title: Secure API Communication
        instruction: Document secure API communication patterns

  - id: pib-specific-considerations
    title: PIB Method Specific Considerations
    instruction: |
      Document frontend architecture considerations specific to PIB Method development.
    sections:
      - id: agent-friendly-patterns
        title: Agent-Friendly Development Patterns
        instruction: Patterns that facilitate AI agent development and understanding
      - id: lever-integration
        title: LEVER Framework Integration
        instruction: How frontend architecture supports LEVER framework principles
      - id: mcp-tool-integration
        title: MCP Tool Integration Points
        instruction: Integration points for MCP tools (Context7 for research, Playwright for testing)
      - id: quality-automation
        title: Quality Automation
        instruction: Automated quality checks and LEVER compliance validation

  - id: implementation-roadmap
    title: Frontend Implementation Roadmap
    instruction: |
      Define implementation roadmap aligned with PIB Method development cycles.
    sections:
      - id: component-development-phases
        title: Component Development Phases
        instruction: Break down frontend implementation into manageable phases
      - id: testing-integration-timeline
        title: Testing Integration Timeline
        instruction: Timeline for integrating comprehensive testing with Playwright
      - id: performance-optimization-schedule
        title: Performance Optimization Schedule
        instruction: Schedule for implementing performance optimizations
      - id: lever-compliance-milestones
        title: LEVER Compliance Milestones
        instruction: Milestones for achieving and maintaining LEVER compliance

  - id: appendices
    title: Appendices
    sections:
      - id: component-library
        title: Component Library Reference
        instruction: Reference documentation for UI components and patterns
      - id: context7-research
        title: Context7 Research Summary
        instruction: Summary of Context7 research findings that informed frontend decisions
      - id: browser-compatibility
        title: Browser Compatibility Matrix
        instruction: Document browser support requirements and testing matrix
      - id: change-log
        title: Change Log
        type: table
        columns: [Date, Version, Description, Author, LEVER Impact]
        instruction: Track document versions and changes with LEVER impact assessment