# PIB Advanced Elicitation Task

## Purpose

- Provide optional reflective and brainstorming actions to enhance content quality with LEVER framework integration
- Enable deeper exploration of ideas through structured elicitation techniques with PIB Method agent coordination
- Support iterative refinement through multiple analytical perspectives and Q-LEVER questioning
- Usable during template-driven document creation or any PIB Method agent conversation
- Integrate with PIB Method agents and MCP tools for enhanced elicitation capabilities

## PIB Method Integration

### Agent Coordination
- **Primary Agents**: pm, analyst, architect
- **Supporting Agents**: dev-agent, code-reviewer, orchestrator
- **MCP Tools**: perplexity (for research-enhanced elicitation), context7 (for pattern validation)

### LEVER Framework Integration
- **Question Phase**: Apply Q-LEVER questioning to all elicitation activities
- **Leverage Analysis**: Identify existing solutions or patterns during elicitation
- **Extension Opportunities**: Explore ways to extend current approaches
- **Verification Planning**: Plan validation approaches for elicited ideas
- **Duplication Elimination**: Identify and eliminate redundant concepts
- **Complexity Reduction**: Simplify complex ideas while maintaining value

## Usage Scenarios

### Scenario 1: PIB Template Document Creation

After outputting a section during PIB Method document creation:

1. **Section Review**: Ask user to review the drafted section with LEVER compliance considerations
2. **Offer Enhanced Elicitation**: Present 9 carefully selected elicitation methods with PIB Method enhancements
3. **Simple Selection**: User types a number (0-8) to engage method, or 9 to proceed
4. **Execute & Loop**: Apply selected method with LEVER principles, then re-offer choices until user proceeds
5. **Agent Handoff**: Prepare enhanced content for next PIB Method agent in workflow

### Scenario 2: PIB Agent Collaboration Elicitation

PIB Method agents can request advanced elicitation on any output:

- Agent or user says "do advanced elicitation" or "PIB: elicit-ideas"
- Current agent selects 9 relevant methods for the context with PIB Method enhancements
- Same simple 0-9 selection process with agent coordination
- Results are prepared for optimal handoff to next agent

### Scenario 3: Cross-Agent Elicitation

During PIB Method workflows with multiple agents:

- Any agent can trigger elicitation on behalf of another agent's work
- Elicitation results include agent-specific recommendations
- Enhanced context preservation for multi-agent workflows

## Task Instructions

### 1. Intelligent Method Selection with PIB Enhancement

**Context Analysis**: Before presenting options, analyze with PIB Method considerations:

- **Content Type**: Technical specs, user stories, architecture, requirements, etc.
- **Complexity Level**: Simple, moderate, or complex content with LEVER implications
- **Stakeholder Needs**: Who will use this information and which PIB agents are involved
- **Risk Level**: High-impact decisions vs routine items with LEVER compliance impact
- **Creative Potential**: Opportunities for innovation, leveraging, or extension
- **Agent Workflow Stage**: Where in the PIB Method workflow this elicitation occurs
- **MCP Tool Opportunities**: Whether research or pattern validation would enhance elicitation

**PIB Method Selection Strategy**:

1. **Always Include Core Methods** (choose 3-4):
   - Expand or Contract for Audience (with agent-specific perspectives)
   - Critique and Refine (with LEVER compliance assessment)
   - Identify Potential Risks (including LEVER framework risks)
   - Assess Alignment with Goals (including PIB Method quality gates)

2. **Context-Specific Methods with PIB Enhancement** (choose 4-5):
   - **Technical Content**: Tree of Thoughts (with Context7 research), ReWOO, Meta-Prompting
   - **Requirements**: Stakeholder Perspectives (including agent perspectives), Edge Cases, User Journey Mapping
   - **Architecture**: Alternative Approaches (leveraging existing patterns), Impact Analysis, Integration Assessment
   - **Process**: SCAMPER Method, Process Mapping, Risk-Benefit Analysis
   - **Creative**: Analogical Reasoning, Constraint Relaxation, Future Scenarios

3. **PIB-Specific Methods** (choose 1-2):
   - **LEVER Compliance Analysis**: Assess how ideas align with LEVER principles
   - **Agent Workflow Integration**: Evaluate how ideas integrate with PIB Method agent workflows
   - **MCP Tool Enhancement**: Identify opportunities to enhance elicitation with MCP tools
   - **Quality Gate Preparation**: Prepare elicited content for PIB Method quality gates

### 2. Enhanced Elicitation Methods for PIB Method

#### Core PIB Method Elicitation Methods

**0. Q-LEVER Deep Dive**
Apply comprehensive Q-LEVER questioning to the content:
- **Question**: What assumptions are we making? What questions should we ask?
- **Leverage**: What existing solutions, patterns, or knowledge can we leverage?
- **Extend**: How can we extend current approaches rather than starting from scratch?
- **Verify**: How will we verify that our approach works?
- **Eliminate**: What duplication or unnecessary complexity can we eliminate?
- **Reduce**: How can we simplify while maintaining effectiveness?

**1. Agent Perspective Analysis**
Examine the content from each PIB Method agent's perspective:
- **Analyst**: Research and validation opportunities
- **PM**: User value and requirements clarity
- **Architect**: Technical feasibility and integration
- **Dev-Agent**: Implementation complexity and patterns
- **Code-Reviewer**: Quality and maintainability concerns
- **QA-Tester**: Testing and validation approaches

**2. MCP Tool Enhancement**
Identify opportunities to enhance the content using MCP tools:
- **Context7**: Pattern research and best practices validation
- **Perplexity**: Market research and trend analysis
- **Firecrawl**: Competitive analysis and web research
- **Playwright**: Testing and validation automation

**3. Workflow Integration Assessment**
Evaluate how the content integrates with PIB Method workflows:
- Handoff points between agents
- Quality gate requirements
- Context preservation needs
- Automation opportunities

#### Traditional Elicitation Methods (PIB Enhanced)

**4. Expand or Contract for Audience** (with Agent Focus)
- **Expand**: Add detail for technical agents (architect, dev-agent)
- **Contract**: Simplify for stakeholder communication
- **Agent-Specific**: Tailor content for specific agent consumption

**5. Critique and Refine** (with LEVER Compliance)
- Apply rigorous critique with LEVER framework assessment
- Identify improvement opportunities that align with PIB Method principles
- Assess quality gate readiness

**6. Identify Potential Risks** (with PIB Method Context)
- Technical risks affecting agent workflows
- LEVER compliance risks
- Integration risks between agents
- Quality gate failure risks

**7. Alternative Approaches** (with Leverage Focus)
- Explore alternatives that leverage existing solutions
- Consider extension approaches over new creation
- Evaluate complexity reduction alternatives

**8. Stakeholder Impact Analysis** (with Agent Impact)
- Assess impact on different PIB Method agents
- Evaluate user and business stakeholder impact
- Consider long-term maintenance and evolution impact

### 3. PIB Method Execution Protocol

#### Enhanced Execution Process

1. **Context Preparation**
   - Identify current PIB Method workflow stage
   - Determine involved agents and their needs
   - Assess MCP tool enhancement opportunities
   - Evaluate LEVER compliance requirements

2. **Method Application**
   - Apply selected elicitation method with PIB enhancements
   - Integrate LEVER framework considerations throughout
   - Consider agent workflow implications
   - Identify MCP tool research opportunities

3. **Result Processing**
   - Format results for optimal agent consumption
   - Include LEVER compliance assessment
   - Prepare context for agent handoffs
   - Document elicitation insights for future reference

4. **Quality Gate Preparation**
   - Ensure elicited content meets PIB Method quality standards
   - Prepare for relevant quality gate validation
   - Document assumptions and decisions for reviewers

#### Agent-Specific Enhancements

**For PM Agent Elicitation**:
- Focus on user value and business requirements
- Include stakeholder perspective analysis
- Emphasize requirement clarity and testability

**For Architect Agent Elicitation**:
- Emphasize technical feasibility and integration
- Include Context7 pattern research suggestions
- Focus on extensibility and maintainability

**For Dev-Agent Elicitation**:
- Focus on implementation complexity and patterns
- Include code organization and structure considerations
- Emphasize testability and quality assurance

**For Analyst Agent Elicitation**:
- Include research and validation strategies
- Suggest Perplexity and Firecrawl research opportunities
- Focus on market and competitive analysis

## Implementation Notes

### MCP Tool Integration
- **Context7**: Automatically suggest pattern research for technical elicitation
- **Perplexity**: Offer market research for strategic elicitation
- **Firecrawl**: Suggest competitive analysis for business elicitation
- **Playwright**: Include testing perspective in feature elicitation

### Quality Assurance
- All elicitation results must support LEVER framework compliance
- Results should enhance rather than complicate agent workflows
- Maintain focus on practical, actionable insights
- Document elicitation rationale for future reference

### Workflow Integration
- Elicitation can be triggered at any point in PIB Method workflows
- Results are automatically formatted for optimal agent handoff
- Context preservation is maintained throughout elicitation process
- Quality gate implications are always considered

## Usage Examples

### Example 1: PRD Section Elicitation
After drafting functional requirements:
```
I've drafted the functional requirements section. Let's use advanced elicitation to enhance it.

[Presents 9 methods including Q-LEVER Deep Dive, Agent Perspective Analysis, etc.]

User selects 0 (Q-LEVER Deep Dive):
- Question: Are we assuming users want X without validation?
- Leverage: Can we use existing authentication patterns?
- Extend: How can we extend current user management?
- Verify: How will we test these requirements?
- Eliminate: Are any requirements duplicative?
- Reduce: Can we simplify without losing value?
```

### Example 2: Architecture Elicitation with Context7
After drafting system architecture:
```
I've drafted the system architecture. Let's enhance it with elicitation.

User selects 2 (MCP Tool Enhancement):
- Context7 research: Microservices patterns for similar systems
- Pattern validation: Industry best practices for this architecture
- Integration research: Common integration patterns
- Performance research: Scalability considerations

[Results integrated back into architecture with research references]
```

This advanced elicitation task maintains BMAD's powerful interactive capabilities while fully integrating with PIB Method agent workflows, LEVER framework principles, and MCP tool capabilities for enhanced research and validation.