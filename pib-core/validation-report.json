{"passed": 62, "failed": 11, "warnings": 0, "details": [{"test": "Directory Structure - pib-core", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.844Z"}, {"test": "Directory Structure - pib-core/agents", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.844Z"}, {"test": "Directory Structure - pib-core/agents/core", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.844Z"}, {"test": "Directory Structure - pib-core/agents/specialized", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Directory Structure - pib-core/templates", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Directory Structure - pib-core/templates/project", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Directory Structure - pib-core/templates/architecture", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Directory Structure - pib-core/templates/development", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Directory Structure - pib-core/tasks", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Directory Structure - pib-core/tasks/workflows", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Directory Structure - pib-core/tasks/creation", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Directory Structure - pib-core/checklists", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Directory Structure - pib-core/data", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Directory Structure - pib-core/config", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Directory Structure - pib-core/utils", "passed": true, "message": "Directory exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Config File - pib-core/config/template-registry.yaml", "passed": true, "message": "File exists", "level": "info", "timestamp": "2025-08-05T11:50:34.845Z"}, {"test": "Template Registry Validation", "passed": true, "message": "Valid YAML structure with required keys", "level": "info", "timestamp": "2025-08-05T11:50:34.848Z"}, {"test": "Config File - pib-core/data/pib-method-kb.md", "passed": true, "message": "File exists", "level": "info", "timestamp": "2025-08-05T11:50:34.848Z"}, {"test": "Knowledge Base Validation", "passed": true, "message": "Contains all required sections", "level": "info", "timestamp": "2025-08-05T11:50:34.849Z"}, {"test": "Config File - pib-core/data/technical-preferences.yaml", "passed": true, "message": "File exists", "level": "info", "timestamp": "2025-08-05T11:50:34.849Z"}, {"test": "Config Validation - pib-core/data/technical-preferences.yaml", "passed": false, "message": "Validation error: Missing LEVER framework integration", "level": "info", "timestamp": "2025-08-05T11:50:34.850Z"}, {"test": "Config File - pib-core/package.json", "passed": true, "message": "File exists", "level": "info", "timestamp": "2025-08-05T11:50:34.850Z"}, {"test": "Package.json Validation", "passed": true, "message": "Valid with required dependencies and scripts", "level": "info", "timestamp": "2025-08-05T11:50:34.850Z"}, {"test": "Template File - prd-tmpl.yaml", "passed": true, "message": "Template exists", "level": "info", "timestamp": "2025-08-05T11:50:34.850Z"}, {"test": "Template Validation - prd-tmpl.yaml", "passed": true, "message": "Valid PIB-METHOD template with required structure", "level": "info", "timestamp": "2025-08-05T11:50:34.852Z"}, {"test": "Template File - project-brief-tmpl.yaml", "passed": true, "message": "Template exists", "level": "info", "timestamp": "2025-08-05T11:50:34.852Z"}, {"test": "Template Validation - project-brief-tmpl.yaml", "passed": true, "message": "Valid PIB-METHOD template with required structure", "level": "info", "timestamp": "2025-08-05T11:50:34.853Z"}, {"test": "Template File - architecture-tmpl.yaml", "passed": true, "message": "Template exists", "level": "info", "timestamp": "2025-08-05T11:50:34.853Z"}, {"test": "Template Validation - architecture-tmpl.yaml", "passed": true, "message": "Valid PIB-METHOD template with required structure", "level": "info", "timestamp": "2025-08-05T11:50:34.854Z"}, {"test": "Template File - frontend-architecture-tmpl.yaml", "passed": true, "message": "Template exists", "level": "info", "timestamp": "2025-08-05T11:50:34.854Z"}, {"test": "Template Validation - frontend-architecture-tmpl.yaml", "passed": true, "message": "Valid PIB-METHOD template with required structure", "level": "info", "timestamp": "2025-08-05T11:50:34.855Z"}, {"test": "Template File - story-tmpl.yaml", "passed": true, "message": "Template exists", "level": "info", "timestamp": "2025-08-05T11:50:34.855Z"}, {"test": "Template Validation - story-tmpl.yaml", "passed": true, "message": "Valid PIB-METHOD template with required structure", "level": "info", "timestamp": "2025-08-05T11:50:34.856Z"}, {"test": "Template File - dev-work-specification-tmpl.yaml", "passed": true, "message": "Template exists", "level": "info", "timestamp": "2025-08-05T11:50:34.856Z"}, {"test": "Template Validation - dev-work-specification-tmpl.yaml", "passed": true, "message": "Valid PIB-METHOD template with required structure", "level": "info", "timestamp": "2025-08-05T11:50:34.857Z"}, {"test": "Agent File - analyst.md", "passed": true, "message": "Agent file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.857Z"}, {"test": "Agent Validation - analyst.md", "passed": false, "message": "Agent validation error: bad indentation of a mapping entry (11:60)\n\n  8 |  ... \n  9 |  ... FILE - it contains your complete persona definition\n 10 |  ...  defined in the 'agent' and 'persona' sections below\n 11 |  ... our name/role and mention `PIB: help` command\n-----------------------------------------^\n 12 |  ... ent files during activation\n 13 |  ... s when user selects them for execution via command or request ...", "level": "info", "timestamp": "2025-08-05T11:50:34.858Z"}, {"test": "Agent File - pm.md", "passed": true, "message": "Agent file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.858Z"}, {"test": "Agent Validation - pm.md", "passed": false, "message": "Agent validation error: bad indentation of a mapping entry (11:60)\n\n  8 |  ... \n  9 |  ... FILE - it contains your complete persona definition\n 10 |  ...  defined in the 'agent' and 'persona' sections below\n 11 |  ... our name/role and mention `PIB: help` command\n-----------------------------------------^\n 12 |  ... ent files during activation\n 13 |  ... s when user selects them for execution via command or request ...", "level": "info", "timestamp": "2025-08-05T11:50:34.858Z"}, {"test": "Agent File - architect.md", "passed": true, "message": "Agent file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.858Z"}, {"test": "Agent <PERSON> - architect.md", "passed": false, "message": "Agent validation error: bad indentation of a mapping entry (11:60)\n\n  8 |  ... \n  9 |  ... FILE - it contains your complete persona definition\n 10 |  ...  defined in the 'agent' and 'persona' sections below\n 11 |  ... our name/role and mention `PIB: help` command\n-----------------------------------------^\n 12 |  ... ent files during activation\n 13 |  ... s when user selects them for execution via command or request ...", "level": "info", "timestamp": "2025-08-05T11:50:34.858Z"}, {"test": "Agent File - dev.md", "passed": true, "message": "Agent file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.858Z"}, {"test": "Agent Validation - dev.md", "passed": false, "message": "Agent validation error: bad indentation of a mapping entry (11:60)\n\n  8 |  ... \n  9 |  ... FILE - it contains your complete persona definition\n 10 |  ...  defined in the 'agent' and 'persona' sections below\n 11 |  ... our name/role and mention `PIB: help` command\n-----------------------------------------^\n 12 |  ... ent files during activation\n 13 |  ... s when user selects them for execution via command or request ...", "level": "info", "timestamp": "2025-08-05T11:50:34.859Z"}, {"test": "Agent File - code-reviewer.md", "passed": true, "message": "Agent file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.859Z"}, {"test": "Agent Validation - code-reviewer.md", "passed": false, "message": "Agent validation error: Agent must reference PIB-METHOD integration (found: mcp_tools)", "level": "info", "timestamp": "2025-08-05T11:50:34.859Z"}, {"test": "Agent File - qa-tester.md", "passed": true, "message": "Agent file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.859Z"}, {"test": "Agent Validation - qa-tester.md", "passed": false, "message": "Agent validation error: Agent must reference PIB-METHOD integration (found: mcp_tools)", "level": "info", "timestamp": "2025-08-05T11:50:34.859Z"}, {"test": "Agent File - change-implementer.md", "passed": true, "message": "Agent file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.859Z"}, {"test": "Agent Validation - change-implementer.md", "passed": false, "message": "Agent validation error: Agent must reference PIB-METHOD integration (found: mcp_tools)", "level": "info", "timestamp": "2025-08-05T11:50:34.859Z"}, {"test": "Agent File - platform-engineer.md", "passed": true, "message": "Agent file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.859Z"}, {"test": "Agent Validation - platform-engineer.md", "passed": false, "message": "Agent validation error: Agent must reference PIB-METHOD integration (found: )", "level": "info", "timestamp": "2025-08-05T11:50:34.859Z"}, {"test": "Agent File - task-executor.md", "passed": true, "message": "Agent file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.859Z"}, {"test": "Agent Validation - task-executor.md", "passed": false, "message": "Agent validation error: Agent must reference PIB-METHOD integration (found: mcp_tools)", "level": "info", "timestamp": "2025-08-05T11:50:34.860Z"}, {"test": "Agent File - orchestrator.md", "passed": true, "message": "Agent file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.860Z"}, {"test": "Agent Validation - orchestrator.md", "passed": false, "message": "Agent validation error: Agent must reference PIB-METHOD integration (found: )", "level": "info", "timestamp": "2025-08-05T11:50:34.860Z"}, {"test": "Workflow File - advanced-elicitation.md", "passed": true, "message": "Workflow file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.860Z"}, {"test": "Workflow Integration - advanced-elicitation.md", "passed": true, "message": "Workflow includes PIB Method integration", "level": "info", "timestamp": "2025-08-05T11:50:34.860Z"}, {"test": "Workflow File - pib-orchestration-workflow.md", "passed": true, "message": "Workflow file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.860Z"}, {"test": "Workflow Integration - pib-orchestration-workflow.md", "passed": true, "message": "Workflow includes PIB Method integration", "level": "info", "timestamp": "2025-08-05T11:50:34.861Z"}, {"test": "Workflow File - project-initialization.md", "passed": true, "message": "Workflow file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.861Z"}, {"test": "Workflow Integration - project-initialization.md", "passed": true, "message": "Workflow includes PIB Method integration", "level": "info", "timestamp": "2025-08-05T11:50:34.861Z"}, {"test": "Checklist File - pib-method-master-checklist.md", "passed": true, "message": "Checklist file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.861Z"}, {"test": "Checklist Content - pib-method-master-checklist.md", "passed": true, "message": "Contains 147 checklist items", "level": "info", "timestamp": "2025-08-05T11:50:34.861Z"}, {"test": "Checklist File - lever-compliance-checklist.md", "passed": true, "message": "Checklist file exists", "level": "info", "timestamp": "2025-08-05T11:50:34.861Z"}, {"test": "Checklist Content - lever-compliance-checklist.md", "passed": true, "message": "Contains 91 checklist items", "level": "info", "timestamp": "2025-08-05T11:50:34.861Z"}, {"test": "Template Resolver <PERSON>t", "passed": true, "message": "Template resolver exists", "level": "info", "timestamp": "2025-08-05T11:50:34.861Z"}, {"test": "LEVER Integration - pib-method-kb.md", "passed": true, "message": "Contains 6/6 LEVER keywords", "level": "info", "timestamp": "2025-08-05T11:50:34.863Z"}, {"test": "LEVER Integration - prd-tmpl.yaml", "passed": true, "message": "Contains 6/6 LEVER keywords", "level": "info", "timestamp": "2025-08-05T11:50:34.863Z"}, {"test": "LEVER Integration - lever-compliance-checklist.md", "passed": true, "message": "Contains 6/6 LEVER keywords", "level": "info", "timestamp": "2025-08-05T11:50:34.863Z"}, {"test": "MCP Integration - pib-method-kb.md", "passed": true, "message": "References 4/4 MCP tools: Context7, <PERSON><PERSON>ity, Firecrawl, Playwright", "level": "info", "timestamp": "2025-08-05T11:50:34.864Z"}, {"test": "MCP Integration - technical-preferences.yaml", "passed": true, "message": "References 4/4 MCP tools: Context7, <PERSON><PERSON>ity, Firecrawl, Playwright", "level": "info", "timestamp": "2025-08-05T11:50:34.864Z"}, {"test": "MCP Integration - template-registry.yaml", "passed": true, "message": "References 4/4 MCP tools: Context7, <PERSON><PERSON>ity, Firecrawl, Playwright", "level": "info", "timestamp": "2025-08-05T11:50:34.864Z"}, {"test": "Template Resolver Execution", "passed": true, "message": "<PERSON><PERSON><PERSON> executes successfully", "level": "info", "timestamp": "2025-08-05T11:50:34.906Z"}]}