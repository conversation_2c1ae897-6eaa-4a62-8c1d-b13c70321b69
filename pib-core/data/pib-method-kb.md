# PIB-METHOD Knowledge Base

## INDEX OF TOPICS

- [PIB-METHOD Knowledge Base](#pib-method-knowledge-base)
  - [INDEX OF TOPICS](#index-of-topics)
  - [PIB-METHOD - CORE PHILOSOPHY](#pib-method---core-philosophy)
  - [PIB-METHOD - CONSOLIDATED CAPABILITIES](#pib-method---consolidated-capabilities)
  - [LEVER FRAMEWORK INTEGRATION](#lever-framework-integration)
  - [MCP TOOL INTEGRATION](#mcp-tool-integration)
  - [AGENT ECOSYSTEM](#agent-ecosystem)
  - [WORKFLOW PATTERNS](#workflow-patterns)
  - [QUALITY ASSURANCE](#quality-assurance)
  - [GETTING STARTED WITH PIB-METHOD](#getting-started-with-pib-method)
  - [BEST PRACTICES AND GUIDELINES](#best-practices-and-guidelines)

## PIB-METHOD - CORE PHILOSOPHY

PIB-METHOD (Productive Intelligence-Based Method) is a consolidated development methodology that combines the best of BMAD-METHOD's interactive elicitation capabilities with PIB's agent orchestration and LEVER framework compliance. This unified approach transforms development into a systematic, AI-coordinated process with built-in quality assurance.

### Core Principles

1. **Intelligent Agent Coordination**: Specialized AI agents handle specific aspects of development with seamless handoffs
2. **LEVER Framework Compliance**: All development follows Leverage, Extend, Verify, Eliminate, Reduce principles
3. **Interactive Elicitation**: BMAD's proven elicitation techniques enhance content quality and decision-making
4. **MCP Tool Integration**: Enhanced capabilities through Context7, Perplexity, Firecrawl, and Playwright
5. **Quality-First Approach**: Built-in quality gates ensure consistent, high-quality outcomes
6. **Template-Driven Development**: Comprehensive templates with interactive sections guide development

### When to Use PIB-METHOD

- **New Projects (Greenfield)**: Complete end-to-end development with full agent coordination
- **Existing Projects (Brownfield)**: Feature additions and enhancements with LEVER compliance
- **Complex Projects**: Multi-agent workflows with sophisticated coordination requirements  
- **Quality-Critical Projects**: Built-in quality gates and LEVER framework compliance
- **Research-Intensive Projects**: MCP tool integration for enhanced research capabilities
- **Team Collaboration**: Structured agent handoffs and context preservation

## PIB-METHOD - CONSOLIDATED CAPABILITIES

### From BMAD-METHOD Integration
- **Interactive Elicitation**: Advanced elicitation techniques for enhanced content quality
- **Template System**: Sophisticated YAML-based templates with intelligent sections
- **Brainstorming Support**: Structured brainstorming and ideation workflows
- **Document Creation**: Professional PRDs, architecture docs, and user stories
- **Brownfield Support**: Specialized workflows for existing project enhancement

### From PIB Integration  
- **Agent Orchestration**: Coordinated multi-agent workflows with quality gates
- **LEVER Framework**: Systematic approach to leveraging, extending, verifying, eliminating, and reducing
- **MCP Tool Integration**: Enhanced research, development, and testing capabilities
- **Quality Assurance**: Built-in code review, testing, and validation workflows
- **Workflow Automation**: Automated handoffs and context preservation

### New Consolidated Features
- **Unified Template Registry**: Single source for all templates with agent integration
- **Enhanced Agent Coordination**: BMAD elicitation combined with PIB orchestration
- **MCP-Enhanced Elicitation**: Research-backed elicitation using Perplexity and Firecrawl
- **LEVER-Compliant Templates**: All templates include LEVER framework integration
- **Quality Gate Integration**: Templates and workflows include built-in quality validation

## LEVER FRAMEWORK INTEGRATION

### Q-LEVER Principles (Enhanced)

The PIB-METHOD extends LEVER with Q-LEVER - starting with questioning assumptions:

- **Question**: Question assumptions, requirements, and approaches before implementation
- **Leverage**: Identify and reuse existing solutions, patterns, and resources
- **Extend**: Extend existing capabilities rather than building from scratch
- **Verify**: Implement comprehensive testing and validation approaches
- **Eliminate**: Remove duplication, unnecessary complexity, and redundant processes
- **Reduce**: Minimize complexity while maintaining full functionality

### LEVER Compliance Standards

All PIB-METHOD work must achieve minimum LEVER compliance scores:

- **Individual Principles**: Minimum 4/5 score on each LEVER principle
- **Overall Score**: Minimum 4/5 overall LEVER compliance score
- **Quality Gates**: LEVER compliance validated at each workflow stage
- **Documentation**: LEVER assessment documented for all deliverables

### LEVER Integration in Workflows

Every PIB-METHOD workflow includes:

1. **Initial LEVER Assessment**: Evaluate opportunities for each LEVER principle
2. **Ongoing LEVER Monitoring**: Track LEVER compliance throughout development
3. **LEVER Quality Gates**: Validate LEVER compliance before workflow progression
4. **Final LEVER Validation**: Comprehensive LEVER assessment before completion

## MCP TOOL INTEGRATION

### Tool Distribution by Capability

**Context7 - Technical Research and Patterns**
- **Agents**: architect, dev-agent, code-reviewer, change-implementer, task-executor
- **Use Cases**: Pattern research, best practices, technical documentation, framework guidance
- **Integration**: Automatic suggestions for technical research and validation

**Perplexity - Market Research and Analysis**
- **Agents**: analyst (primary), pm (supporting)
- **Use Cases**: Market research, competitive analysis, trend identification, validation research
- **Integration**: Enhanced elicitation with research-backed insights

**Firecrawl - Web Research and Content Extraction**
- **Agents**: analyst (primary)
- **Use Cases**: Web scraping, content extraction, competitive intelligence, research automation
- **Integration**: Automated competitive analysis and market research

**Playwright - Testing and Browser Automation**
- **Agents**: qa-tester (primary), dev-agent (supporting)
- **Use Cases**: E2E testing, browser automation, visual testing, performance testing
- **Integration**: Comprehensive testing workflows with automated validation

### MCP Tool Workflow Integration

**Research Phase**:
- Perplexity and Firecrawl for comprehensive market and competitive analysis
- Context7 for technical pattern research and validation
- Enhanced elicitation with research-backed insights

**Design Phase**:
- Context7 for architectural pattern research and best practices
- Integration of research findings into design decisions
- LEVER-compliant design approaches based on researched patterns

**Implementation Phase**:
- Context7 for implementation guidance and pattern validation
- Real-time access to technical documentation and best practices
- LEVER compliance validation through pattern matching

**Testing Phase**:
- Playwright for comprehensive browser automation and testing
- Context7 for testing framework research and best practices
- Automated quality validation and reporting

## AGENT ECOSYSTEM

### Core Agent Categories

**Analysis & Planning Agents**
- **Analyst**: Market research, competitive analysis, validation (Perplexity, Firecrawl, Context7)
- **PM**: Requirements definition, PRD creation, epic planning, stakeholder coordination

**Design & Architecture Agents**
- **Architect**: System design, technical architecture, pattern research (Context7)

**Implementation Agents**
- **Dev-Agent**: Primary development and implementation (Context7)
- **Task-Executor**: Focused sub-task execution and coordination (Context7)

**Quality Assurance Agents**
- **Code-Reviewer**: Code review, LEVER compliance validation (Context7)
- **Change-Implementer**: Feedback implementation and refinement (Context7)
- **QA-Tester**: Comprehensive testing and validation (Playwright)

**Infrastructure & Coordination Agents**
- **Platform-Engineer**: Infrastructure, DevOps, developer experience
- **Orchestrator**: Multi-agent workflow coordination and management

### Agent Coordination Protocols

**Handoff Management**:
- Complete context preservation across agent transitions
- Quality validation before handoff acceptance
- Clear deliverables and success criteria
- LEVER compliance verification

**Communication Standards**:
- Structured handoff documentation with context packages
- Quality metrics and LEVER scores included in all handoffs
- Clear requirements and expectations for receiving agents
- Escalation protocols for conflicts and issues

**Quality Gates**:
- Entry criteria validation before agent activation
- Process validation during agent work
- Exit criteria validation before handoff
- LEVER compliance assessment at each gate

## WORKFLOW PATTERNS

### Sequential Development Pattern
```mermaid
graph TD
    A[Analyst Research] --> B[PM Requirements]
    B --> C[Architect Design]
    C --> D[Dev-Agent Implementation]
    D --> E[Code-Reviewer Quality Check]
    E --> F{LEVER ≥ 4/5?}
    F -->|No| G[Change-Implementer]
    G --> E
    F -->|Yes| H[QA-Tester Validation]
    H --> I[Production Ready]
```

### Parallel Development Pattern
```mermaid
graph TD
    A[Project Initiation] --> B[Analyst Research]
    B --> C[PM Requirements]
    C --> D[Parallel Design]
    D --> E[System Architecture]
    D --> F[Frontend Architecture]
    D --> G[Infrastructure Planning]
    E --> H[Integration Point]
    F --> H
    G --> H
    H --> I[Coordinated Implementation]
```

### Iterative Refinement Pattern
```mermaid
graph TD
    A[Initial Implementation] --> B[Code Review]
    B --> C{Quality Acceptable?}
    C -->|No| D[Change Implementation]
    D --> B
    C -->|Yes| E[QA Testing]
    E --> F{Tests Pass?}
    F -->|No| G[Debug & Fix]
    G --> E
    F -->|Yes| H[Production Deployment]
```

## QUALITY ASSURANCE

### Quality Gate Framework

**Project Initiation Gates**:
- Project brief complete with LEVER assessment
- Market research validated with MCP tool integration
- Technical constraints identified and documented
- Agent coordination plan established

**Requirements Gates**:
- PRD complete with interactive elicitation
- Epics and stories defined with LEVER compliance
- Technical assumptions validated with Context7 research
- Quality standards established and documented

**Architecture Gates**:
- System architecture complete with pattern research
- Technical specifications validated with Context7
- Integration points defined and validated
- LEVER compliance assessment minimum 4/5

**Implementation Gates**:
- Code complete with comprehensive documentation
- LEVER self-assessment completed and validated
- Integration testing passed with quality metrics
- Code review completed with minimum 4/5 LEVER score

**Testing Gates**:
- Comprehensive test suite with Playwright automation
- All quality criteria met with documented validation
- Performance benchmarks achieved and documented
- Deployment readiness confirmed with final validation

### Quality Metrics and KPIs

**LEVER Compliance Metrics**:
- Individual principle scores (1-5 scale)
- Overall LEVER compliance score
- Quality gate passage rates
- Improvement trends over time

**Workflow Efficiency Metrics**:
- Agent handoff success rates
- Quality gate passage on first attempt
- Rework and revision rates
- Time from requirements to deployment

**Quality Outcome Metrics**:
- Defect rates in production
- User satisfaction with delivered features
- Maintainability and extensibility measures
- Technical debt accumulation rates

## GETTING STARTED WITH PIB-METHOD

### Initial Project Setup

1. **Environment Preparation**
   ```bash
   # Initialize PIB-METHOD project
   cp -r pib-core/ your-project/
   cd your-project
   node pib-core/utils/template-resolver.js list
   ```

2. **Agent Coordination Setup**
   - Configure agent access and MCP tool integration
   - Establish quality gates and LEVER compliance standards
   - Set up workflow coordination and handoff protocols

3. **Initial Research Phase**
   ```
   Activate Analyst Agent:
   - Use Perplexity for market research
   - Use Firecrawl for competitive analysis
   - Apply Q-LEVER questioning to research findings
   - Prepare research summary for PM handoff
   ```

4. **Requirements Definition**
   ```
   Activate PM Agent:
   - Create project brief using PIB-METHOD templates
   - Develop comprehensive PRD with interactive elicitation
   - Define epics and stories with LEVER compliance
   - Establish agent coordination plan
   ```

5. **Architecture and Design**
   ```
   Activate Architect Agent:
   - Research patterns using Context7
   - Design system architecture with LEVER principles
   - Create technical specifications with integration points
   - Validate design through quality gates
   ```

### Template Usage Guide

**Finding Templates**:
```bash
# List all available templates
node pib-core/utils/template-resolver.js list

# Find templates by agent
node pib-core/utils/template-resolver.js find-by-agent pm

# Find templates by use case
node pib-core/utils/template-resolver.js find-by-use-case project_initiation
```

**Using Templates**:
- All templates include interactive elicitation sections
- LEVER framework integration is built into every template
- Agent coordination points are clearly marked
- MCP tool integration suggestions are included

**Template Categories**:
- **Project Templates**: Project brief, PRD, project initialization
- **Architecture Templates**: System architecture, frontend architecture, technical design
- **Development Templates**: User stories, development specifications, implementation plans
- **Documentation Templates**: Various documentation types with PIB-METHOD integration

## BEST PRACTICES AND GUIDELINES

### LEVER Framework Application

**Question Phase (Critical First Step)**:
- Always start with Q-LEVER questioning of assumptions
- Validate requirements and constraints before proceeding
- Use MCP tools for research-backed validation
- Document all assumptions and decisions

**Leverage Identification**:
- Research existing solutions using Context7 and Perplexity
- Identify reusable patterns, libraries, and frameworks
- Evaluate existing project assets and capabilities
- Plan integration of existing solutions

**Extension Strategies**:
- Prefer extending existing solutions over creating new ones
- Build upon established patterns and frameworks
- Enhance existing capabilities rather than replacing them
- Maintain compatibility with existing systems

**Verification Planning**:
- Design comprehensive testing strategies using Playwright
- Plan validation approaches for all requirements
- Establish quality metrics and success criteria
- Implement automated validation where possible

**Duplication Elimination**:
- Identify and consolidate redundant functionality
- Establish single sources of truth for all information
- Streamline processes and remove unnecessary steps
- Maintain clean, organized code and documentation

**Complexity Reduction**:
- Simplify solutions while maintaining full functionality
- Choose proven, stable technologies over experimental ones
- Minimize dependencies and integration complexity
- Maintain clear, understandable architectures and processes

### Agent Coordination Best Practices

**Clear Handoffs**:
- Provide complete context packages for receiving agents
- Include quality metrics and LEVER assessments
- Specify clear deliverables and success criteria
- Validate handoff completion before proceeding

**Quality Gates**:
- Enforce entry criteria before activating agents
- Monitor process compliance during agent work
- Validate exit criteria before allowing progression
- Maintain minimum LEVER compliance standards

**Context Preservation**:
- Maintain complete workflow history and decisions
- Preserve agent insights and recommendations
- Document quality assessments and improvements
- Enable knowledge transfer between workflow phases

### MCP Tool Usage Best Practices

**Research Integration**:
- Use Perplexity for market and competitive research
- Use Firecrawl for automated web research and analysis
- Use Context7 for technical pattern research and validation
- Integrate research findings into all decision-making

**Development Support**:
- Use Context7 for implementation guidance and best practices
- Research patterns and frameworks before implementation
- Validate architectural decisions against industry standards
- Maintain alignment with proven development practices

**Testing Enhancement**:
- Use Playwright for comprehensive browser automation
- Implement visual regression testing and validation
- Automate performance testing and monitoring
- Maintain comprehensive test coverage with automation

This knowledge base provides comprehensive guidance for effective PIB-METHOD usage, combining the best capabilities of BMAD and PIB methodologies with LEVER framework compliance and MCP tool integration.