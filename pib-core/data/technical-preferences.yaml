technical_preferences:
  version: "3.0"
  methodology: "PIB-METHOD"
  description: "Consolidated technical preferences for PIB-METHOD projects with LEVER framework integration"
  
  # User-defined preferences should be customized per project
  user_preferences:
    languages:
      primary: []  # e.g., ["TypeScript", "Python", "Go"]
      secondary: []  # e.g., ["JavaScript", "Rust", "Java"]
      
    frameworks:
      frontend: []  # e.g., ["React", "Next.js", "Vue.js"]
      backend: []   # e.g., ["FastAPI", "Express", "Spring Boot"]
      testing: []   # e.g., ["Jest", "Pytest", "Playwright"]
      
    architecture_patterns:
      preferred: []  # e.g., ["Microservices", "Event-driven", "CQRS"]
      avoid: []      # e.g., ["Monolith", "Tight coupling"]
      
    deployment:
      platforms: []     # e.g., ["AWS", "GCP", "Vercel"]
      containerization: []  # e.g., ["Docker", "Kubernetes"]
      ci_cd: []         # e.g., ["GitHub Actions", "GitLab CI"]
      
    databases:
      relational: []    # e.g., ["PostgreSQL", "MySQL"]
      nosql: []         # e.g., ["MongoDB", "Redis"]
      caching: []       # e.g., ["Redis", "Memcached"]
      
    developer_tools:
      ide: []           # e.g., ["VS Code", "IntelliJ", "Neovim"]
      version_control: []  # e.g., ["Git", "GitHub", "GitLab"]
      package_managers: [] # e.g., ["npm", "yarn", "pip", "cargo"]
      
  # PIB-METHOD recommended defaults with LEVER framework integration
  pib_defaults:
    methodology_alignment:
      lever_compliance: true
      agent_coordination: true
      mcp_tool_integration: true
      quality_gates_enabled: true
      
    recommended_patterns:
      architecture:
        - name: "Modular Monolith"
          rationale: "Balances simplicity with scalability, supports LEVER principles"
          lever_alignment: "Extends existing patterns, reduces complexity"
        - name: "API-First Design"
          rationale: "Enables agent coordination and testing automation"
          lever_alignment: "Verifies through API contracts, eliminates integration issues"
        - name: "Component-Based Frontend"
          rationale: "Supports reusability and extension principles"
          lever_alignment: "Leverages existing components, eliminates duplication"
          
      development_practices:
        - name: "Test-Driven Development"
          rationale: "Supports LEVER verification principle"
          mcp_integration: "Playwright for E2E testing automation"
        - name: "Documentation-Driven Development"
          rationale: "Enables agent coordination and knowledge transfer"
          mcp_integration: "Context7 for pattern research and validation"
        - name: "Continuous Integration"
          rationale: "Supports LEVER verification and quality gates"
          agent_coordination: "Automated quality validation between agents"
          
    technology_recommendations:
      languages:
        - name: "TypeScript"
          rationale: "Type safety supports LEVER verification and reduces complexity"
          use_cases: ["Frontend", "Backend", "Full-stack"]
        - name: "Python"
          rationale: "Extensive libraries support leveraging existing solutions"
          use_cases: ["Backend", "Data processing", "AI/ML integration"]
          
      frameworks:
        - name: "Next.js"
          rationale: "Full-stack framework reduces complexity, extensive ecosystem"
          lever_alignment: "Leverages React ecosystem, extends web capabilities"
          mcp_integration: "Playwright testing support, Context7 documentation"
        - name: "FastAPI"
          rationale: "High performance, automatic documentation, testing support"
          lever_alignment: "Leverages Python ecosystem, verifies through auto-docs"
          
      databases:
        - name: "PostgreSQL"
          rationale: "Mature, feature-rich, supports complex queries and JSON"
          lever_alignment: "Leverages SQL standards, extends with JSON capabilities"
        - name: "Redis"
          rationale: "High-performance caching, supports multiple data structures"
          lever_alignment: "Extends database capabilities, reduces query complexity"
          
      deployment:
        - name: "Docker"
          rationale: "Environment consistency, supports LEVER reduce principle"
          lever_alignment: "Eliminates environment differences, reduces deployment complexity"
        - name: "Vercel/Netlify"
          rationale: "Simplified deployment for frontend applications"
          lever_alignment: "Leverages platform automation, reduces operational complexity"
          
    anti_patterns:
      avoid:
        - name: "Premature Optimization"
          rationale: "Violates LEVER reduce principle by adding unnecessary complexity"
          alternative: "Profile first, optimize based on actual bottlenecks"
        - name: "Not Invented Here Syndrome"
          rationale: "Violates LEVER leverage principle"
          alternative: "Research existing solutions using Context7 and Perplexity"
        - name: "Monolithic Frontend Components"
          rationale: "Violates LEVER eliminate and reduce principles"
          alternative: "Component composition and reusability patterns"
        - name: "Manual Testing Only"
          rationale: "Violates LEVER verify principle"
          alternative: "Automated testing with Playwright integration"
          
  # MCP tool integration preferences
  mcp_tool_preferences:
    context7:
      usage_priority: "high"
      use_cases:
        - "Pattern research before implementation"
        - "Framework documentation during development"
        - "Best practices validation during code review"
      agents: ["architect", "dev-agent", "code-reviewer"]
      
    perplexity:
      usage_priority: "high"
      use_cases:
        - "Market research during project initiation"
        - "Competitive analysis for feature planning"
        - "Technology trend research for architecture decisions"
      agents: ["analyst", "pm"]
      
    firecrawl:
      usage_priority: "medium"
      use_cases:
        - "Competitive analysis and benchmarking"
        - "Documentation extraction from external sources"
        - "Market research automation"
      agents: ["analyst"]
      
    playwright:
      usage_priority: "high"
      use_cases:
        - "End-to-end testing automation"
        - "Visual regression testing"
        - "Performance testing and monitoring"
      agents: ["qa-tester", "dev-agent"]
      
  # Quality and LEVER compliance preferences
  quality_preferences:
    lever_framework:
      enabled: true
      minimum_compliance_score: 4.0
      scoring_strictness: "standard"  # options: lenient, standard, strict
      automated_validation: true
      principles:
        leverage: "Reuse existing solutions and patterns"
        extend: "Build upon existing rather than replace"
        verify: "Comprehensive testing and validation"
        eliminate: "Remove duplication and redundancy"
        reduce: "Minimize complexity while maintaining functionality"
      
    testing_requirements:
      unit_test_coverage: 80          # minimum percentage
      integration_testing: true
      e2e_testing: true
      performance_testing: true
      accessibility_testing: true
      
    code_quality:
      linting: true
      type_checking: true
      code_formatting: true
      documentation_coverage: 75      # minimum percentage
      
    security_requirements:
      dependency_scanning: true
      security_linting: true
      vulnerability_monitoring: true
      
  # Agent coordination preferences
  agent_coordination:
    workflow_patterns:
      preferred: ["sequential_development", "iterative_refinement"]
      complex_projects: ["parallel_development", "orchestrated_workflows"]
      
    handoff_protocols:
      context_preservation: "comprehensive"    # options: minimal, standard, comprehensive
      quality_validation: "strict"            # options: lenient, standard, strict
      documentation_requirements: "detailed"   # options: minimal, standard, detailed
      
    quality_gates:
      enforcement_level: "strict"             # options: lenient, standard, strict
      bypass_authorization: "architect_only"  # who can bypass quality gates
      escalation_protocols: true
      
  # Project-specific customization guidance
  customization_guide:
    how_to_customize:
      - "Copy this file to your project directory"
      - "Update user_preferences section with your specific choices"
      - "Modify pib_defaults based on project requirements"
      - "Adjust quality_preferences based on project criticality"
      - "Configure agent_coordination based on team structure"
      
    common_customizations:
      startup_project:
        lever_compliance: "standard"
        testing_requirements: "medium"
        agent_coordination: "simplified"
        
      enterprise_project:
        lever_compliance: "strict"
        testing_requirements: "comprehensive"
        agent_coordination: "full_orchestration"
        security_requirements: "enhanced"
        
      research_project:
        mcp_tool_usage: "extensive"
        documentation_requirements: "comprehensive"
        flexibility: "high"
        
    validation_rules:
      - "All preferences must align with LEVER framework principles"
      - "MCP tool preferences must match agent capabilities"
      - "Quality requirements must be achievable with chosen technologies"
      - "Agent coordination must support chosen workflow patterns"