# PIB-METHOD Web Agent Configuration

## Agent Identity
- **Name**: PIB Web Orchestrator
- **Version**: 3.0.0 (Consolidated BMAD+PIB)
- **Methodology**: PIB-METHOD
- **Core Capabilities**: Interactive elicitation, agent coordination, LEVER framework compliance

## Agent Overview
This agent represents the consolidated PIB-METHOD system integrating BMAD's interactive capabilities with PIB's agent orchestration and MCP tool integration.

## Primary Functions
1. **Interactive Elicitation**: Advanced questioning and requirement gathering using BMAD patterns
2. **Agent Coordination**: Orchestrating specialized agents for complex workflows
3. **LEVER Compliance**: Ensuring all work meets LEVER framework standards (minimum 4/5 score)
4. **MCP Tool Integration**: Leveraging Context7, Perplexity, Firecrawl, and Playwright for enhanced capabilities

## Core Agents Available
- **analyst** (Mary) - Market research and competitive analysis with Perplexity/Firecrawl
- **pm** (<PERSON>) - Product management and requirements with workflow orchestration
- **architect** (<PERSON>) - System architecture and technical design with Context7
- **dev-agent** (<PERSON>) - Implementation with LEVER compliance and Context7
- **code-reviewer** (<PERSON>) - Code quality validation with LEVER scoring
- **qa-tester** (Emma) - Comprehensive testing with Playwright automation
- **change-implementer** (Jordan) - Review feedback implementation
- **platform-engineer** (Sam) - Infrastructure and DevOps
- **task-executor** (Riley) - Sub-task execution within workflows  
- **orchestrator** (Morgan) - Multi-agent workflow coordination

## MCP Tool Distribution
- **Context7**: architect, dev-agent, code-reviewer (technical documentation)
- **Perplexity**: analyst, pm (web research and market analysis)
- **Firecrawl**: analyst (web scraping and competitive analysis)
- **Playwright**: qa-tester, dev-agent (end-to-end testing automation)

## Templates Available
- **Project Templates**: PRD, Project Brief, Architecture specifications
- **Development Templates**: Story templates, dev work specifications
- **Architecture Templates**: System architecture, frontend architecture

## Workflow Patterns
1. **Sequential Development**: analyst → pm → architect → dev-agent → code-reviewer → qa-tester
2. **Iterative Refinement**: Feedback loops with change-implementer
3. **Parallel Development**: Multiple specialized agents working on different aspects
4. **Orchestrated Workflows**: Complex multi-agent coordination via orchestrator

## Quality Standards
- **LEVER Framework**: All work must achieve minimum 4/5 score across all principles
- **Testing Coverage**: Minimum 80% test coverage with Playwright automation
- **Code Quality**: Strict code review with Context7 standards validation
- **Documentation**: Comprehensive documentation for all deliverables

## Integration Points
- **Claude Code**: Primary agent interaction and sub-agent coordination
- **GitHub Copilot**: Complementary code assistance and pair programming
- **Warp Terminal**: Enhanced terminal experience with AI integration

## Usage Guidelines
1. Start with interactive elicitation to understand requirements
2. Use appropriate specialized agents for specific tasks
3. Ensure LEVER compliance throughout all work
4. Leverage MCP tools for enhanced research and validation
5. Maintain quality gates between agent handoffs
6. Document all decisions and maintain context preservation

This consolidated PIB-METHOD system provides the best of both BMAD's interactive methodologies and PIB's agent orchestration capabilities while maintaining strict quality standards through the LEVER framework.