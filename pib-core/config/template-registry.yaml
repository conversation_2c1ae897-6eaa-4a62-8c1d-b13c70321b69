template_registry:
  version: 3.0
  methodology: PIB-METHOD
  description: "Consolidated template registry for PIB-METHOD system integrating BMAD and PIB capabilities"
  
  categories:
    project:
      description: "Project initiation and planning templates"
      templates:
        - id: pib-project-brief-template-v3
          name: "PIB Project Brief"
          file: "project/project-brief-tmpl.yaml"
          description: "Project brief with LEVER framework integration"
          use_cases: ["project_initiation", "requirements_gathering"]
          agents: ["pm", "analyst"]
          mcp_tools: ["perplexity", "firecrawl"]
        - id: pib-prd-template-v3
          name: "PIB Product Requirements Document"
          file: "project/prd-tmpl.yaml"
          description: "Comprehensive PRD with interactive elicitation and LEVER compliance"
          use_cases: ["requirements_definition", "epic_planning"]
          agents: ["pm"]
          interactive: true
          
    architecture:
      description: "System and technical architecture templates"
      templates:
        - id: pib-architecture-template-v3
          name: "PIB Architecture Document"
          file: "architecture/architecture-tmpl.yaml"
          description: "System architecture with LEVER framework and Context7 integration"
          use_cases: ["technical_architecture", "system_design"]
          agents: ["architect"]
          mcp_tools: ["context7"]
        - id: pib-frontend-architecture-template-v3
          name: "PIB Frontend Architecture Document"
          file: "architecture/frontend-architecture-tmpl.yaml"
          description: "Frontend architecture with Playwright testing integration"
          use_cases: ["frontend_design", "ui_architecture"]
          agents: ["architect", "dev-agent"]
          mcp_tools: ["context7", "playwright"]
          
    development:
      description: "Development and implementation templates"
      templates:
        - id: pib-story-template-v3
          name: "PIB Story Document"
          file: "development/story-tmpl.yaml"
          description: "User story with PIB agent workflow integration"
          use_cases: ["story_definition", "development_planning"]
          agents: ["pm", "dev-agent", "code-reviewer", "qa-tester"]
          interactive: true
        - id: pib-dev-work-specification-v3
          name: "PIB Development Work Specification"
          file: "development/dev-work-specification-tmpl.yaml"
          description: "Detailed development work specification with LEVER planning"
          use_cases: ["development_specification", "agent_coordination"]
          agents: ["dev-agent", "code-reviewer", "qa-tester"]
          
    documentation:
      description: "Documentation and communication templates"
      templates: []
      # To be populated with additional documentation templates
      
  template_inheritance:
    description: "Template inheritance and extension capabilities"
    base_templates:
      - id: "pib-base-template"
        common_sections:
          - "change-log"
          - "lever-assessment"
          - "pib-integration"
    
  workflow_integration:
    description: "Integration with PIB Method workflows"
    agent_coordination:
      analyst:
        primary_templates: ["project-brief", "competitive-analysis", "market-research"]
        mcp_tools: ["perplexity", "firecrawl", "context7"]
      pm:
        primary_templates: ["project-brief", "prd", "story"]
        workflow_stages: ["requirements", "planning", "coordination"]
      architect:
        primary_templates: ["architecture", "frontend-architecture", "technical-design"]
        mcp_tools: ["context7"]
      dev-agent:
        primary_templates: ["story", "dev-work-specification", "implementation-plan"]
        workflow_stages: ["implementation", "integration"]
      code-reviewer:
        primary_templates: ["review-checklist", "quality-assessment"]
        quality_gates: ["lever-compliance", "standards-adherence"]
      qa-tester:
        primary_templates: ["test-plan", "test-specification"]
        mcp_tools: ["playwright"]
        
  lever_integration:
    description: "LEVER framework integration across all templates"
    required_sections:
      - "lever-assessment"
      - "lever-compliance-targets"
    quality_gates:
      minimum_lever_score: 4.0
      compliance_validation: true
      
  mcp_tool_distribution:
    description: "MCP tool requirements by template category"
    context7:
      templates: ["architecture", "frontend-architecture", "technical-design"]
      use_cases: ["pattern_research", "best_practices", "documentation"]
    perplexity:
      templates: ["project-brief", "competitive-analysis", "market-research"]
      use_cases: ["research", "analysis", "trend_identification"]
    firecrawl:
      templates: ["competitive-analysis", "market-research"]
      use_cases: ["web_scraping", "content_extraction"]
    playwright:
      templates: ["test-specification", "qa-plan", "frontend-architecture"]
      use_cases: ["e2e_testing", "browser_automation", "visual_testing"]
      
  migration_mapping:
    description: "Mapping from legacy BMAD and PIB templates to consolidated templates"
    bmad_legacy:
      "prd-tmpl.yaml": "project/prd-tmpl.yaml"
      "architecture-tmpl.yaml": "architecture/architecture-tmpl.yaml"
      "story-tmpl.yaml": "development/story-tmpl.yaml"
      "front-end-architecture-tmpl.yaml": "architecture/frontend-architecture-tmpl.yaml"
    pib_legacy:
      "prd-tmpl.md": "project/prd-tmpl.yaml"
      "architecture-tmpl.md": "architecture/architecture-tmpl.yaml"
      "story-tmpl.md": "development/story-tmpl.yaml"
      "project-brief-tmpl.md": "project/project-brief-tmpl.yaml"
      
  validation_rules:
    description: "Template validation and quality rules"
    required_metadata:
      - "template.id"
      - "template.name"
      - "template.version"
      - "template.methodology"
    lever_compliance:
      required_sections: ["lever-assessment"]
      minimum_score: 4.0
    agent_integration:
      required_sections: ["pib-integration", "agent-coordination"]
    quality_gates:
      - "syntax_validation"
      - "lever_compliance_check"
      - "agent_integration_validation"
      - "mcp_tool_compatibility"