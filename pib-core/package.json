{"name": "pib-core", "version": "3.0.0", "description": "PIB-METHOD Core System - Consolidated BMAD and PIB capabilities with LEVER framework integration", "main": "index.js", "scripts": {"template-resolver": "node utils/template-resolver.js", "list-templates": "node utils/template-resolver.js list", "validate-templates": "node utils/validate-all-templates.js"}, "keywords": ["pib-method", "bmad-method", "ai-agents", "development-methodology", "lever-framework", "mcp-tools"], "author": "PIB-METHOD Development Team", "license": "MIT", "dependencies": {"js-yaml": "^4.1.0"}, "devDependencies": {}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/pib-method"}, "pib-method": {"version": "3.0", "methodology": "PIB-METHOD", "integration": "BMAD + PIB Consolidated", "lever_compliance": true, "mcp_tools": ["context7", "perplexity", "firecrawl", "playwright"], "agents": ["analyst", "pm", "architect", "dev-agent", "code-reviewer", "qa-tester", "change-implementer", "platform-engineer", "task-executor", "orchestrator"]}}