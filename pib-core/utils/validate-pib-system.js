#!/usr/bin/env node

/**
 * PIB-METHOD System Validation Script
 * 
 * Comprehensive validation of the consolidated PIB-METHOD system integrating
 * BMAD interactive capabilities with PIB agent coordination and LEVER framework.
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const yaml = require('js-yaml');

class PIBSystemValidator {
    constructor() {
        this.results = {
            passed: 0,
            failed: 0,
            warnings: 0,
            details: []
        };
        this.startTime = Date.now();
    }

    /**
     * Log test result
     */
    logResult(testName, passed, message, level = 'info') {
        const result = {
            test: testName,
            passed,
            message,
            level,
            timestamp: new Date().toISOString()
        };
        
        this.results.details.push(result);
        
        if (passed) {
            this.results.passed++;
            console.log(`✅ ${testName}: ${message}`);
        } else {
            if (level === 'warning') {
                this.results.warnings++;
                console.log(`⚠️  ${testName}: ${message}`);
            } else {
                this.results.failed++;
                console.log(`❌ ${testName}: ${message}`);
            }
        }
    }

    /**
     * Test directory structure
     */
    testDirectoryStructure() {
        console.log('\n🔍 Testing Directory Structure...\n');
        
        const requiredDirs = [
            'pib-core',
            'pib-core/agents',
            'pib-core/agents/core',
            'pib-core/agents/specialized',
            'pib-core/templates',
            'pib-core/templates/project',
            'pib-core/templates/architecture',
            'pib-core/templates/development',
            'pib-core/tasks',
            'pib-core/tasks/workflows',
            'pib-core/tasks/creation',
            'pib-core/checklists',
            'pib-core/data',
            'pib-core/config',
            'pib-core/utils'
        ];

        for (const dir of requiredDirs) {
            if (fs.existsSync(dir) && fs.statSync(dir).isDirectory()) {
                this.logResult(`Directory Structure - ${dir}`, true, 'Directory exists');
            } else {
                this.logResult(`Directory Structure - ${dir}`, false, 'Required directory missing');
            }
        }
    }

    /**
     * Test core configuration files
     */
    testConfigurationFiles() {
        console.log('\n🔍 Testing Configuration Files...\n');
        
        const configFiles = [
            {
                path: 'pib-core/config/template-registry.yaml',
                required: true,
                validate: this.validateTemplateRegistry.bind(this)
            },
            {
                path: 'pib-core/data/pib-method-kb.md',
                required: true,
                validate: this.validateKnowledgeBase.bind(this)
            },
            {
                path: 'pib-core/data/technical-preferences.yaml',
                required: true,
                validate: this.validateTechnicalPreferences.bind(this)
            },
            {
                path: 'pib-core/package.json',
                required: true,
                validate: this.validatePackageJson.bind(this)
            }
        ];

        for (const config of configFiles) {
            if (fs.existsSync(config.path)) {
                this.logResult(`Config File - ${config.path}`, true, 'File exists');
                
                try {
                    config.validate(config.path);
                } catch (error) {
                    this.logResult(`Config Validation - ${config.path}`, false, `Validation error: ${error.message}`);
                }
            } else {
                this.logResult(`Config File - ${config.path}`, !config.required, 
                    config.required ? 'Required file missing' : 'Optional file missing', 
                    config.required ? 'error' : 'warning');
            }
        }
    }

    /**
     * Validate template registry
     */
    validateTemplateRegistry(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const registry = yaml.load(content);
        
        if (!registry.template_registry) {
            throw new Error('Missing template_registry root key');
        }
        
        if (!registry.template_registry.version) {
            throw new Error('Missing version in template registry');
        }
        
        if (!registry.template_registry.categories) {
            throw new Error('Missing categories in template registry');
        }
        
        this.logResult('Template Registry Validation', true, 'Valid YAML structure with required keys');
    }

    /**
     * Validate knowledge base
     */
    validateKnowledgeBase(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        const requiredSections = [
            'PIB-METHOD - CORE PHILOSOPHY',
            'LEVER FRAMEWORK INTEGRATION',
            'MCP TOOL INTEGRATION',
            'AGENT ECOSYSTEM'
        ];
        
        for (const section of requiredSections) {
            if (!content.includes(section)) {
                throw new Error(`Missing required section: ${section}`);
            }
        }
        
        this.logResult('Knowledge Base Validation', true, 'Contains all required sections');
    }

    /**
     * Validate technical preferences
     */
    validateTechnicalPreferences(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const prefs = yaml.load(content);
        
        if (!prefs.technical_preferences) {
            throw new Error('Missing technical_preferences root key');
        }
        
        if (!prefs.technical_preferences.lever_framework) {
            throw new Error('Missing LEVER framework integration');
        }
        
        if (!prefs.technical_preferences.mcp_tool_preferences) {
            throw new Error('Missing MCP tool preferences');
        }
        
        this.logResult('Technical Preferences Validation', true, 'Valid structure with LEVER and MCP integration');
    }

    /**
     * Validate package.json
     */
    validatePackageJson(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const pkg = JSON.parse(content);
        
        if (!pkg.dependencies || !pkg.dependencies['js-yaml']) {
            throw new Error('Missing required js-yaml dependency');
        }
        
        if (!pkg.scripts || !pkg.scripts['template-resolver']) {
            throw new Error('Missing template-resolver script');
        }
        
        this.logResult('Package.json Validation', true, 'Valid with required dependencies and scripts');
    }

    /**
     * Test template files
     */
    testTemplateFiles() {
        console.log('\n🔍 Testing Template Files...\n');
        
        const templateFiles = [
            'pib-core/templates/project/prd-tmpl.yaml',
            'pib-core/templates/project/project-brief-tmpl.yaml',
            'pib-core/templates/architecture/architecture-tmpl.yaml',
            'pib-core/templates/architecture/frontend-architecture-tmpl.yaml',
            'pib-core/templates/development/story-tmpl.yaml',
            'pib-core/templates/development/dev-work-specification-tmpl.yaml'
        ];

        for (const templateFile of templateFiles) {
            if (fs.existsSync(templateFile)) {
                this.logResult(`Template File - ${path.basename(templateFile)}`, true, 'Template exists');
                
                try {
                    this.validateTemplate(templateFile);
                } catch (error) {
                    this.logResult(`Template Validation - ${path.basename(templateFile)}`, false, 
                        `Template validation error: ${error.message}`);
                }
            } else {
                this.logResult(`Template File - ${path.basename(templateFile)}`, false, 'Required template missing');
            }
        }
    }

    /**
     * Validate individual template file
     */
    validateTemplate(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const template = yaml.load(content);
        
        if (!template.template) {
            throw new Error('Missing template root key');
        }
        
        if (!template.template.methodology || template.template.methodology !== 'PIB-METHOD') {
            throw new Error('Template must specify methodology: PIB-METHOD');
        }
        
        if (!template.workflow || !template.workflow.agent_integration) {
            throw new Error('Template must specify agent_integration: true');
        }
        
        if (!template.sections || !Array.isArray(template.sections)) {
            throw new Error('Template must have sections array');
        }
        
        this.logResult(`Template Validation - ${path.basename(filePath)}`, true, 
            'Valid PIB-METHOD template with required structure');
    }

    /**
     * Test agent files
     */
    testAgentFiles() {
        console.log('\n🔍 Testing Agent Files...\n');
        
        // Core agents from BMAD
        const coreAgents = [
            'pib-core/agents/core/analyst.md',
            'pib-core/agents/core/pm.md',
            'pib-core/agents/core/architect.md',
            'pib-core/agents/core/dev.md'
        ];
        
        // Specialized agents from PIB
        const specializedAgents = [
            'pib-core/agents/specialized/code-reviewer.md',
            'pib-core/agents/specialized/qa-tester.md',
            'pib-core/agents/specialized/change-implementer.md',
            'pib-core/agents/specialized/platform-engineer.md',
            'pib-core/agents/specialized/task-executor.md',
            'pib-core/agents/specialized/orchestrator.md'
        ];

        [...coreAgents, ...specializedAgents].forEach(agentFile => {
            if (fs.existsSync(agentFile)) {
                this.logResult(`Agent File - ${path.basename(agentFile)}`, true, 'Agent file exists');
                
                try {
                    this.validateAgentFile(agentFile);
                } catch (error) {
                    this.logResult(`Agent Validation - ${path.basename(agentFile)}`, false, 
                        `Agent validation error: ${error.message}`);
                }
            } else {
                this.logResult(`Agent File - ${path.basename(agentFile)}`, false, 'Required agent file missing');
            }
        });
    }

    /**
     * Validate agent file structure
     */
    validateAgentFile(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Check for PIB-METHOD integration (flexible matching)
        const pibReferences = ['PIB-METHOD', 'PIB Method', 'PIB ENHANCEMENTS', 'mcp_tools', 'sub_agent_coordination', 'orchestration_capable'];
        const foundReferences = pibReferences.filter(ref => content.includes(ref));
        if (foundReferences.length < 2) {
            throw new Error('Agent must reference PIB-METHOD integration (found: ' + foundReferences.join(', ') + ')');
        }
        
        // Check for LEVER framework reference (flexible matching)
        const leverReferences = ['LEVER', 'lever', 'Leverage', 'Extend', 'Verify', 'Eliminate', 'Reduce'];
        const foundLeverRefs = leverReferences.filter(ref => content.includes(ref));
        if (foundLeverRefs.length < 1) {
            throw new Error('Agent must reference LEVER framework (found: ' + foundLeverRefs.join(', ') + ')');
        }
        
        // Check for agent structure (YAML or markdown with proper sections)
        if (content.includes('```yaml') || content.includes('agent:')) {
            // YAML-based agent
            const yamlMatch = content.match(/```yaml\n([\s\S]*?)\n```/);
            if (yamlMatch) {
                const agentConfig = yaml.load(yamlMatch[1]);
                if (!agentConfig.agent || !agentConfig.agent.name) {
                    throw new Error('YAML agent configuration missing required fields');
                }
            }
        }
        
        this.logResult(`Agent Validation - ${path.basename(filePath)}`, true, 
            'Valid agent with PIB-METHOD and LEVER integration');
    }

    /**
     * Test workflow and task files
     */
    testWorkflowFiles() {
        console.log('\n🔍 Testing Workflow and Task Files...\n');
        
        const workflowFiles = [
            'pib-core/tasks/workflows/advanced-elicitation.md',
            'pib-core/tasks/workflows/pib-orchestration-workflow.md',
            'pib-core/tasks/creation/project-initialization.md'
        ];

        workflowFiles.forEach(workflowFile => {
            if (fs.existsSync(workflowFile)) {
                this.logResult(`Workflow File - ${path.basename(workflowFile)}`, true, 'Workflow file exists');
                
                const content = fs.readFileSync(workflowFile, 'utf8');
                if (content.includes('PIB Method') || content.includes('PIB-METHOD')) {
                    this.logResult(`Workflow Integration - ${path.basename(workflowFile)}`, true, 
                        'Workflow includes PIB Method integration');
                } else {
                    this.logResult(`Workflow Integration - ${path.basename(workflowFile)}`, false, 
                        'Workflow missing PIB Method integration');
                }
            } else {
                this.logResult(`Workflow File - ${path.basename(workflowFile)}`, false, 'Required workflow file missing');
            }
        });
    }

    /**
     * Test checklist files
     */
    testChecklistFiles() {
        console.log('\n🔍 Testing Checklist Files...\n');
        
        const checklistFiles = [
            'pib-core/checklists/pib-method-master-checklist.md',
            'pib-core/checklists/lever-compliance-checklist.md'
        ];

        checklistFiles.forEach(checklistFile => {
            if (fs.existsSync(checklistFile)) {
                this.logResult(`Checklist File - ${path.basename(checklistFile)}`, true, 'Checklist file exists');
                
                const content = fs.readFileSync(checklistFile, 'utf8');
                
                // Count checklist items
                const checklistItems = content.match(/- \[ \]/g);
                if (checklistItems && checklistItems.length > 10) {
                    this.logResult(`Checklist Content - ${path.basename(checklistFile)}`, true, 
                        `Contains ${checklistItems.length} checklist items`);
                } else {
                    this.logResult(`Checklist Content - ${path.basename(checklistFile)}`, false, 
                        'Insufficient checklist items');
                }
            } else {
                this.logResult(`Checklist File - ${path.basename(checklistFile)}`, false, 'Required checklist file missing');
            }
        });
    }

    /**
     * Test utility scripts
     */
    testUtilityScripts() {
        console.log('\n🔍 Testing Utility Scripts...\n');
        
        if (fs.existsSync('pib-core/utils/template-resolver.js')) {
            this.logResult('Template Resolver Script', true, 'Template resolver exists');
            
            // Test that the script can be executed
            exec('node pib-core/utils/template-resolver.js --help', (error, stdout, stderr) => {
                if (error) {
                    this.logResult('Template Resolver Execution', false, `Script execution failed: ${error.message}`);
                } else {
                    this.logResult('Template Resolver Execution', true, 'Script executes successfully');
                }
            });
        } else {
            this.logResult('Template Resolver Script', false, 'Template resolver script missing');
        }
    }

    /**
     * Test LEVER framework integration
     */
    testLEVERIntegration() {
        console.log('\n🔍 Testing LEVER Framework Integration...\n');
        
        // Check that LEVER is referenced throughout the system
        const criticalFiles = [
            'pib-core/data/pib-method-kb.md',
            'pib-core/templates/project/prd-tmpl.yaml',
            'pib-core/checklists/lever-compliance-checklist.md'
        ];

        criticalFiles.forEach(file => {
            if (fs.existsSync(file)) {
                const content = fs.readFileSync(file, 'utf8');
                
                const leverKeywords = ['LEVER', 'Leverage', 'Extend', 'Verify', 'Eliminate', 'Reduce'];
                const foundKeywords = leverKeywords.filter(keyword => content.includes(keyword));
                
                if (foundKeywords.length >= 4) {
                    this.logResult(`LEVER Integration - ${path.basename(file)}`, true, 
                        `Contains ${foundKeywords.length}/6 LEVER keywords`);
                } else {
                    this.logResult(`LEVER Integration - ${path.basename(file)}`, false, 
                        `Insufficient LEVER integration (${foundKeywords.length}/6 keywords)`);
                }
            }
        });
    }

    /**
     * Test MCP tool integration references
     */
    testMCPIntegration() {
        console.log('\n🔍 Testing MCP Tool Integration...\n');
        
        const mcpTools = ['Context7', 'Perplexity', 'Firecrawl', 'Playwright'];
        const mcpFiles = [
            'pib-core/data/pib-method-kb.md',
            'pib-core/data/technical-preferences.yaml',
            'pib-core/config/template-registry.yaml'
        ];

        mcpFiles.forEach(file => {
            if (fs.existsSync(file)) {
                const content = fs.readFileSync(file, 'utf8');
                
                const foundTools = mcpTools.filter(tool => content.toLowerCase().includes(tool.toLowerCase()));
                
                if (foundTools.length >= 3) {
                    this.logResult(`MCP Integration - ${path.basename(file)}`, true, 
                        `References ${foundTools.length}/4 MCP tools: ${foundTools.join(', ')}`);
                } else {
                    this.logResult(`MCP Integration - ${path.basename(file)}`, false, 
                        `Insufficient MCP tool references (${foundTools.length}/4)`);
                }
            }
        });
    }

    /**
     * Generate validation report
     */
    generateReport() {
        const endTime = Date.now();
        const duration = Math.round((endTime - this.startTime) / 1000);
        
        console.log('\n' + '='.repeat(80));
        console.log('🎯 PIB-METHOD SYSTEM VALIDATION REPORT');
        console.log('='.repeat(80));
        
        console.log(`\n📊 Summary:`);
        console.log(`   ✅ Passed: ${this.results.passed}`);
        console.log(`   ❌ Failed: ${this.results.failed}`);
        console.log(`   ⚠️  Warnings: ${this.results.warnings}`);
        console.log(`   ⏱️  Duration: ${duration}s`);
        
        const totalTests = this.results.passed + this.results.failed;
        const successRate = totalTests > 0 ? Math.round((this.results.passed / totalTests) * 100) : 0;
        console.log(`   📈 Success Rate: ${successRate}%`);
        
        if (this.results.failed === 0) {
            console.log(`\n🎉 VALIDATION SUCCESSFUL! 🎉`);
            console.log(`   The PIB-METHOD system has been successfully consolidated and validated.`);
            console.log(`   All critical components are present and properly integrated.`);
        } else {
            console.log(`\n🚨 VALIDATION FAILED 🚨`);
            console.log(`   ${this.results.failed} critical issues must be resolved before system deployment.`);
        }
        
        if (this.results.warnings > 0) {
            console.log(`\n⚠️  ${this.results.warnings} warnings should be addressed for optimal functionality.`);
        }
        
        console.log('\n📋 Next Steps:');
        if (this.results.failed === 0) {
            console.log('   1. ✅ System validation complete');
            console.log('   2. 🚀 Ready for sync script unification (Story 1.3)');
            console.log('   3. 🎯 Proceed with end-to-end system testing');
        } else {
            console.log('   1. 🔧 Address all failed validation items');
            console.log('   2. 🔄 Re-run validation until all tests pass');
            console.log('   3. 📝 Document any system improvements made');
        }
        
        console.log('\n' + '='.repeat(80));
        
        // Write detailed report to file
        const reportFile = 'pib-core/validation-report.json';
        fs.writeFileSync(reportFile, JSON.stringify(this.results, null, 2));
        console.log(`📄 Detailed report saved to: ${reportFile}`);
        
        return this.results.failed === 0;
    }

    /**
     * Run all validation tests
     */
    async runValidation() {
        console.log('🚀 Starting PIB-METHOD System Validation...\n');
        console.log('Validating consolidated system integrating BMAD and PIB capabilities');
        console.log('with LEVER framework compliance and MCP tool integration.\n');
        
        // Run all validation tests
        this.testDirectoryStructure();
        this.testConfigurationFiles();
        this.testTemplateFiles();
        this.testAgentFiles();
        this.testWorkflowFiles();
        this.testChecklistFiles();
        this.testUtilityScripts();
        this.testLEVERIntegration();
        this.testMCPIntegration();
        
        // Wait a moment for any async operations
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Generate final report
        return this.generateReport();
    }
}

// Run validation if called directly
if (require.main === module) {
    const validator = new PIBSystemValidator();
    validator.runValidation().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = PIBSystemValidator;