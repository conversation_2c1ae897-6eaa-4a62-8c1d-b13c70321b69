#!/usr/bin/env node

/**
 * PIB-METHOD Template Resolver Utility
 * 
 * Helps PIB Method agents find and use consolidated templates from the unified template system.
 * Integrates BMAD's interactive elicitation with PIB's agent coordination capabilities.
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

class TemplateResolver {
    constructor(pibCorePath = './pib-core') {
        this.pibCorePath = pibCorePath;
        this.registryPath = path.join(pibCorePath, 'config', 'template-registry.yaml');
        this.templatesPath = path.join(pibCorePath, 'templates');
        this.registry = null;
        this.loadRegistry();
    }

    /**
     * Load the template registry configuration
     */
    loadRegistry() {
        try {
            const registryContent = fs.readFileSync(this.registryPath, 'utf8');
            this.registry = yaml.load(registryContent);
            console.log(`✅ Loaded PIB-METHOD template registry v${this.registry.template_registry.version}`);
        } catch (error) {
            console.error(`❌ Failed to load template registry: ${error.message}`);
            process.exit(1);
        }
    }

    /**
     * Find templates by use case
     * @param {string} useCase - The use case to search for
     * @returns {Array} Array of matching templates
     */
    findTemplatesByUseCase(useCase) {
        const matches = [];
        
        for (const [category, categoryData] of Object.entries(this.registry.template_registry.categories)) {
            for (const template of categoryData.templates || []) {
                if (template.use_cases && template.use_cases.includes(useCase)) {
                    matches.push({
                        ...template,
                        category,
                        fullPath: path.join(this.templatesPath, template.file)
                    });
                }
            }
        }
        
        return matches;
    }

    /**
     * Find templates by agent
     * @param {string} agent - The PIB Method agent name
     * @returns {Array} Array of templates suitable for the agent
     */
    findTemplatesByAgent(agent) {
        const matches = [];
        
        for (const [category, categoryData] of Object.entries(this.registry.template_registry.categories)) {
            for (const template of categoryData.templates || []) {
                if (template.agents && template.agents.includes(agent)) {
                    matches.push({
                        ...template,
                        category,
                        fullPath: path.join(this.templatesPath, template.file)
                    });
                }
            }
        }
        
        return matches;
    }

    /**
     * Get template by ID
     * @param {string} templateId - The template ID
     * @returns {Object|null} Template object or null if not found
     */
    getTemplateById(templateId) {
        for (const [category, categoryData] of Object.entries(this.registry.template_registry.categories)) {
            for (const template of categoryData.templates || []) {
                if (template.id === templateId) {
                    return {
                        ...template,
                        category,
                        fullPath: path.join(this.templatesPath, template.file)
                    };
                }
            }
        }
        return null;
    }

    /**
     * Get templates that require specific MCP tools
     * @param {string} mcpTool - The MCP tool name
     * @returns {Array} Array of templates that use the MCP tool
     */
    getTemplatesByMCPTool(mcpTool) {
        const matches = [];
        
        for (const [category, categoryData] of Object.entries(this.registry.template_registry.categories)) {
            for (const template of categoryData.templates || []) {
                if (template.mcp_tools && template.mcp_tools.includes(mcpTool)) {
                    matches.push({
                        ...template,
                        category,
                        fullPath: path.join(this.templatesPath, template.file)
                    });
                }
            }
        }
        
        return matches;
    }

    /**
     * Load and parse a template file
     * @param {string} templateId - The template ID
     * @returns {Object|null} Parsed template or null if not found
     */
    loadTemplate(templateId) {
        const templateInfo = this.getTemplateById(templateId);
        if (!templateInfo) {
            console.error(`❌ Template not found: ${templateId}`);
            return null;
        }

        try {
            const templateContent = fs.readFileSync(templateInfo.fullPath, 'utf8');
            const template = yaml.load(templateContent);
            
            // Add metadata from registry
            template._registry_info = templateInfo;
            
            return template;
        } catch (error) {
            console.error(`❌ Failed to load template ${templateId}: ${error.message}`);
            return null;
        }
    }

    /**
     * Get migration mapping for legacy templates
     * @param {string} legacyTemplate - Legacy template name
     * @returns {string|null} New template path or null if not found
     */
    getMigrationMapping(legacyTemplate) {
        const bmadMapping = this.registry.template_registry.migration_mapping.bmad_legacy[legacyTemplate];
        const pibMapping = this.registry.template_registry.migration_mapping.pib_legacy[legacyTemplate];
        
        return bmadMapping || pibMapping || null;
    }

    /**
     * Validate template LEVER compliance
     * @param {Object} template - Template object
     * @returns {Object} Validation results
     */
    validateLEVERCompliance(template) {
        const results = {
            valid: true,
            issues: [],
            score: 0
        };

        // Check for required LEVER sections
        const requiredSections = this.registry.template_registry.lever_integration.required_sections;
        for (const section of requiredSections) {
            if (!this.findSectionInTemplate(template, section)) {
                results.valid = false;
                results.issues.push(`Missing required LEVER section: ${section}`);
            }
        }

        // Check methodology
        if (template.template && template.template.methodology !== 'PIB-METHOD') {
            results.issues.push('Template should specify methodology: PIB-METHOD');
        }

        // Check agent integration
        if (!template.workflow || !template.workflow.agent_integration) {
            results.issues.push('Template should specify agent_integration: true');
        }

        results.score = results.valid ? 5 : Math.max(0, 5 - results.issues.length);
        return results;
    }

    /**
     * Find a section in template structure
     * @param {Object} template - Template object
     * @param {string} sectionName - Section name to find
     * @returns {boolean} True if section found
     */
    findSectionInTemplate(template, sectionName) {
        if (!template.sections) return false;
        
        const findInSections = (sections) => {
            for (const section of sections) {
                if (section.id === sectionName) return true;
                if (section.sections && findInSections(section.sections)) return true;
            }
            return false;
        };
        
        return findInSections(template.sections);
    }

    /**
     * Display help information
     */
    displayHelp() {
        console.log(`
PIB-METHOD Template Resolver v${this.registry.template_registry.version}

Usage:
  node template-resolver.js <command> [options]

Commands:
  list                    List all available templates
  find-by-agent <agent>   Find templates for specific PIB Method agent
  find-by-use-case <use>  Find templates for specific use case
  find-by-mcp <tool>      Find templates that use specific MCP tool
  load <template-id>      Load and display template
  validate <template-id>  Validate template LEVER compliance
  migrate <legacy-name>   Get migration path for legacy template

Examples:
  node template-resolver.js list
  node template-resolver.js find-by-agent dev-agent
  node template-resolver.js find-by-use-case project_initiation
  node template-resolver.js find-by-mcp context7
  node template-resolver.js load pib-prd-template-v3
  node template-resolver.js validate pib-architecture-template-v3
  node template-resolver.js migrate prd-tmpl.yaml

PIB Method Agents:
  analyst, pm, architect, dev-agent, code-reviewer, qa-tester,
  change-implementer, platform-engineer, task-executor, orchestrator

MCP Tools:
  context7, perplexity, firecrawl, playwright
        `);
    }

    /**
     * Main CLI interface
     */
    run() {
        const args = process.argv.slice(2);
        const command = args[0];

        switch (command) {
            case 'list':
                this.listAllTemplates();
                break;
            case 'find-by-agent':
                if (!args[1]) {
                    console.error('❌ Please specify an agent name');
                    return;
                }
                this.displayTemplates(this.findTemplatesByAgent(args[1]), `Templates for agent: ${args[1]}`);
                break;
            case 'find-by-use-case':
                if (!args[1]) {
                    console.error('❌ Please specify a use case');
                    return;
                }
                this.displayTemplates(this.findTemplatesByUseCase(args[1]), `Templates for use case: ${args[1]}`);
                break;
            case 'find-by-mcp':
                if (!args[1]) {
                    console.error('❌ Please specify an MCP tool name');
                    return;
                }
                this.displayTemplates(this.getTemplatesByMCPTool(args[1]), `Templates using MCP tool: ${args[1]}`);
                break;
            case 'load':
                if (!args[1]) {
                    console.error('❌ Please specify a template ID');
                    return;
                }
                const template = this.loadTemplate(args[1]);
                if (template) {
                    console.log(yaml.dump(template, { indent: 2, lineWidth: 120 }));
                }
                break;
            case 'validate':
                if (!args[1]) {
                    console.error('❌ Please specify a template ID');
                    return;
                }
                const templateToValidate = this.loadTemplate(args[1]);
                if (templateToValidate) {
                    const validation = this.validateLEVERCompliance(templateToValidate);
                    console.log(`\n🔍 LEVER Compliance Validation for ${args[1]}`);
                    console.log(`✅ Valid: ${validation.valid}`);
                    console.log(`📊 Score: ${validation.score}/5`);
                    if (validation.issues.length > 0) {
                        console.log(`\n⚠️  Issues:`);
                        validation.issues.forEach(issue => console.log(`   - ${issue}`));
                    }
                }
                break;
            case 'migrate':
                if (!args[1]) {
                    console.error('❌ Please specify a legacy template name');
                    return;
                }
                const newPath = this.getMigrationMapping(args[1]);
                if (newPath) {
                    console.log(`📦 Migration path for ${args[1]}: pib-core/templates/${newPath}`);
                } else {
                    console.log(`❌ No migration path found for ${args[1]}`);
                }
                break;
            default:
                this.displayHelp();
        }
    }

    /**
     * List all available templates
     */
    listAllTemplates() {
        console.log(`\n📋 PIB-METHOD Template Registry v${this.registry.template_registry.version}\n`);
        
        for (const [category, categoryData] of Object.entries(this.registry.template_registry.categories)) {
            console.log(`📁 ${category.toUpperCase()}: ${categoryData.description}`);
            for (const template of categoryData.templates || []) {
                console.log(`   📄 ${template.name} (${template.id})`);
                console.log(`      📝 ${template.description}`);
                if (template.agents) {
                    console.log(`      🤖 Agents: ${template.agents.join(', ')}`);
                }
                if (template.mcp_tools) {
                    console.log(`      🔧 MCP Tools: ${template.mcp_tools.join(', ')}`);
                }
                console.log('');
            }
        }
    }

    /**
     * Display template search results
     */
    displayTemplates(templates, title) {
        console.log(`\n${title}:\n`);
        
        if (templates.length === 0) {
            console.log('   No templates found matching criteria.');
            return;
        }
        
        for (const template of templates) {
            console.log(`📄 ${template.name} (${template.id})`);
            console.log(`   📁 Category: ${template.category}`);
            console.log(`   📝 ${template.description}`);
            console.log(`   📂 Path: ${template.file}`);
            if (template.agents) {
                console.log(`   🤖 Agents: ${template.agents.join(', ')}`);
            }
            if (template.mcp_tools) {
                console.log(`   🔧 MCP Tools: ${template.mcp_tools.join(', ')}`);
            }
            console.log('');
        }
    }
}

// CLI execution
if (require.main === module) {
    const resolver = new TemplateResolver();
    resolver.run();
}

module.exports = TemplateResolver;