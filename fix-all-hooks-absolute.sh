#!/bin/bash

# Fix hook errors in ALL projects by using absolute paths
# This solves the PWD issue where hooks fail in subdirectories

echo "PIB-METHOD Absolute Path Hook Fixer"
echo "===================================="
echo ""

# Source directory (PIB-METHOD)
SOURCE_DIR="/Users/<USER>/Projects/PIB-METHOD"
HOOKS_SOURCE="$SOURCE_DIR/.claude/hooks"

# Function to fix hooks with absolute paths
fix_project_hooks_absolute() {
    local project_path="$1"
    local project_name=$(basename "$project_path")
    
    echo "📦 Processing: $project_name ($project_path)"
    
    # Create .claude/hooks directory if it doesn't exist
    local hooks_dir="$project_path/.claude/hooks"
    mkdir -p "$hooks_dir"
    
    # Copy smart wrapper
    echo "  Installing smart wrapper..."
    cp "$HOOKS_SOURCE/smart_hook_wrapper.sh" "$hooks_dir/" 2>/dev/null
    chmod +x "$hooks_dir/smart_hook_wrapper.sh" 2>/dev/null
    
    # Copy Python hooks
    echo "  Installing Python hooks..."
    for hook in pre_tool_use.py post_tool_use.py notification.py stop.py session_start.py user_prompt_submit.py subagent_stop.py pre_compact.py; do
        if [ -f "$HOOKS_SOURCE/$hook" ]; then
            cp "$HOOKS_SOURCE/$hook" "$hooks_dir/"
        fi
    done
    
    # Copy utils directory
    if [ -d "$HOOKS_SOURCE/utils" ]; then
        echo "  Installing hook utilities..."
        cp -r "$HOOKS_SOURCE/utils" "$hooks_dir/"
    fi
    
    # Create settings.json with ABSOLUTE paths
    echo "  Updating settings with absolute paths..."
    cat > "$project_path/.claude/settings.json" << EOF
{
  "permissions": {
    "allow": [
      "Bash(mkdir:*)", "Bash(uv:*)", "Bash(find:*)", "Bash(mv:*)",
      "Bash(grep:*)", "Bash(npm:*)", "Bash(ls:*)", "Bash(cp:*)",
      "Write", "Edit", "Bash(chmod:*)", "Bash(touch:*)", "Bash(rm:*)",
      "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:github.com)",
      "Bash(sed:*)"
    ],
    "deny": []
  },
  "hooks": {
    "PreToolUse": [{
      "matcher": "",
      "hooks": [{
        "type": "command",
        "command": "bash \"$hooks_dir/smart_hook_wrapper.sh\" pre_tool_use.py"
      }]
    }],
    "PostToolUse": [{
      "matcher": "",
      "hooks": [{
        "type": "command",
        "command": "bash \"$hooks_dir/smart_hook_wrapper.sh\" post_tool_use.py"
      }]
    }],
    "Notification": [{
      "matcher": "",
      "hooks": [{
        "type": "command",
        "command": "bash \"$hooks_dir/smart_hook_wrapper.sh\" notification.py --notify"
      }]
    }],
    "Stop": [{
      "matcher": "",
      "hooks": [{
        "type": "command",
        "command": "bash \"$hooks_dir/smart_hook_wrapper.sh\" stop.py"
      }]
    }],
    "SessionStart": [{
      "matcher": "",
      "hooks": [{
        "type": "command",
        "command": "bash \"$hooks_dir/smart_hook_wrapper.sh\" session_start.py"
      }]
    }],
    "UserPromptSubmit": [{
      "matcher": "",
      "hooks": [{
        "type": "command",
        "command": "bash \"$hooks_dir/smart_hook_wrapper.sh\" user_prompt_submit.py"
      }]
    }],
    "SubagentStop": [{
      "matcher": "",
      "hooks": [{
        "type": "command",
        "command": "bash \"$hooks_dir/smart_hook_wrapper.sh\" subagent_stop.py"
      }]
    }],
    "PreCompact": [{
      "matcher": "",
      "hooks": [{
        "type": "command",
        "command": "bash \"$hooks_dir/smart_hook_wrapper.sh\" pre_compact.py"
      }]
    }]
  }
}
EOF
    
    echo "  ✅ Fixed with absolute path: $hooks_dir"
    echo ""
}

# Function to find and fix all projects
find_and_fix_projects() {
    local base_dir="$1"
    local depth="${2:-3}"
    
    echo "Searching in: $base_dir"
    echo ""
    
    # Find all directories with .git
    while IFS= read -r project_dir; do
        # Skip PIB-METHOD itself
        if [[ "$project_dir" == *"PIB-METHOD"* ]]; then
            continue
        fi
        
        if [ -d "$project_dir" ]; then
            fix_project_hooks_absolute "$project_dir"
        fi
    done < <(find "$base_dir" -maxdepth "$depth" -type d -name ".git" 2>/dev/null | xargs -I {} dirname {})
}

# Main execution
if [ "$1" != "" ]; then
    # Fix specific project
    if [ -d "$1" ]; then
        fix_project_hooks_absolute "$1"
    else
        echo "Error: Directory '$1' does not exist"
        exit 1
    fi
else
    echo "Fixing ALL projects with absolute paths..."
    echo "This will prevent hook errors in subdirectories."
    echo ""
    
    # Fix projects in standard locations
    PROJECT_BASES=(
        "/Users/<USER>/Projects/clients"
        "/Users/<USER>/Projects/own"
        "/Users/<USER>/Projects"
    )
    
    for base in "${PROJECT_BASES[@]}"; do
        if [ -d "$base" ]; then
            find_and_fix_projects "$base" 3
        fi
    done
    
    # Fix known nested projects
    NESTED_PROJECTS=(
        "/Users/<USER>/Projects/own/turbois/src/frontend"
        "/Users/<USER>/Projects/own/turbois/src/backend"
    )
    
    for project in "${NESTED_PROJECTS[@]}"; do
        if [ -d "$project" ]; then
            fix_project_hooks_absolute "$project"
        fi
    done
fi

echo "════════════════════════════════════════"
echo "✨ Absolute path hook fix complete!"
echo ""
echo "IMPORTANT: Hooks now use absolute paths and will work from ANY directory."
echo "No more 'hook_wrapper_dynamic.sh: No such file' errors!"
echo ""
echo "Please restart Claude Code in affected projects."