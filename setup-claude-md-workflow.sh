#!/bin/bash

# Setup script for CLAUDE.md auto-update workflow
# This helps configure the GitHub Actions workflow for automatic documentation updates

echo "═══════════════════════════════════════════════════════"
echo "    CLAUDE.md Auto-Update Workflow Setup"
echo "═══════════════════════════════════════════════════════"
echo ""

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_status $RED "❌ Error: Not in a git repository"
    echo "Please run this script from the root of your project repository."
    exit 1
fi

PROJECT_NAME=$(basename "$(pwd)")
print_status $BLUE "Setting up CLAUDE.md workflow for: $PROJECT_NAME"
echo ""

# Step 1: Check if workflow files exist
print_status $YELLOW "Step 1: Checking workflow files..."
if [ -f ".github/workflows/update-claude-md.yml" ]; then
    print_status $GREEN "  ✅ Workflow file exists"
else
    print_status $RED "  ❌ Workflow file missing"
    echo "  Please run sync-pib-complete.sh first to sync workflow files"
    exit 1
fi

if [ -f ".github/prompts/claude-md-review-prompt.md" ]; then
    print_status $GREEN "  ✅ Review prompt exists"
else
    print_status $RED "  ❌ Review prompt missing"
    echo "  Please run sync-pib-complete.sh first to sync workflow files"
    exit 1
fi

# Step 2: Check for Node.js
print_status $YELLOW "\nStep 2: Checking Node.js installation..."
if command -v node >/dev/null 2>&1; then
    NODE_VERSION=$(node --version)
    print_status $GREEN "  ✅ Node.js installed: $NODE_VERSION"
else
    print_status $RED "  ❌ Node.js not found"
    echo "  Please install Node.js (v18 or later) from https://nodejs.org"
    exit 1
fi

# Step 3: Install Claude Code CLI (if needed)
print_status $YELLOW "\nStep 3: Checking Claude Code CLI..."
if ! command -v claude-code >/dev/null 2>&1; then
    print_status $YELLOW "  Claude Code CLI not found globally"
    echo "  You can install it with: npm install -g @anthropic-ai/claude-code"
    echo "  Or use npx when generating the token"
    USE_NPX="true"
else
    print_status $GREEN "  ✅ Claude Code CLI installed"
    USE_NPX="false"
fi

# Step 4: Generate Auth Token
print_status $YELLOW "\nStep 4: Generate Claude Auth Token"
echo ""
echo "You need to generate an auth token for your Claude Pro account."
echo "This will open a browser window for authorization."
echo ""
read -p "Press Enter to generate token (or Ctrl+C to cancel)..."

if [ "$USE_NPX" = "true" ]; then
    print_status $BLUE "Running: npx @anthropic-ai/claude-code setup-token"
    npx @anthropic-ai/claude-code setup-token
else
    print_status $BLUE "Running: claude-code setup-token"
    claude-code setup-token
fi

echo ""
print_status $YELLOW "⚠️  IMPORTANT: Copy the token shown above (keep it secret!)"
echo ""

# Step 5: Instructions for GitHub
print_status $YELLOW "\nStep 5: Add Token to GitHub Secrets"
echo ""
echo "Now you need to add the token to your GitHub repository:"
echo ""
print_status $BLUE "1. Go to: https://github.com/$(git remote get-url origin | sed 's/.*github.com[:/]\(.*\)\.git/\1/')/settings/secrets/actions"
echo "   (or navigate manually to Settings → Secrets and variables → Actions)"
echo ""
echo "2. Click 'New repository secret'"
echo ""
echo "3. Add the secret:"
echo "   Name: CLAUDE_AUTH_TOKEN"
echo "   Value: [Paste your token from Step 4]"
echo ""
echo "4. Click 'Add secret'"
echo ""

read -p "Press Enter once you've added the token to GitHub..."

# Step 6: Test the workflow
print_status $YELLOW "\nStep 6: Test the Workflow"
echo ""
echo "Would you like to trigger a test run of the workflow?"
read -p "Test workflow? (y/n): " test_workflow

if [ "$test_workflow" = "y" ] || [ "$test_workflow" = "Y" ]; then
    # Check if gh CLI is installed
    if command -v gh >/dev/null 2>&1; then
        print_status $BLUE "Triggering workflow via GitHub CLI..."
        gh workflow run update-claude-md.yml
        echo ""
        print_status $GREEN "✅ Workflow triggered!"
        echo "View progress at: https://github.com/$(git remote get-url origin | sed 's/.*github.com[:/]\(.*\)\.git/\1/')/actions"
    else
        print_status $YELLOW "GitHub CLI not installed. Please trigger manually:"
        echo "1. Go to: https://github.com/$(git remote get-url origin | sed 's/.*github.com[:/]\(.*\)\.git/\1/')/actions"
        echo "2. Click on 'Update CLAUDE.md Documentation'"
        echo "3. Click 'Run workflow' → 'Run workflow'"
    fi
fi

# Step 7: Summary
echo ""
print_status $GREEN "═══════════════════════════════════════════════════════"
print_status $GREEN "    ✅ Setup Complete!"
print_status $GREEN "═══════════════════════════════════════════════════════"
echo ""
echo "Your CLAUDE.md auto-update workflow is configured!"
echo ""
echo "📅 Schedule: Runs weekly on Mondays at 3 AM UTC"
echo "🔄 Manual: Can be triggered anytime from GitHub Actions"
echo "📝 Updates: Creates PRs with CLAUDE.md updates"
echo ""
print_status $YELLOW "Next Steps:"
echo "1. Review PRs created by the workflow before merging"
echo "2. Customize .github/prompts/claude-md-review-prompt.md for project needs"
echo "3. Monitor the Actions tab for workflow runs"
echo ""
print_status $BLUE "Documentation: .github/CLAUDE-MD-WORKFLOW-SETUP.md"
echo ""