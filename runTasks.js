const { exec } = require("child_process");

const taskName = "Run All"; // Name of the compound task

exec(
  `code --folder-uri ${process.cwd()} --command "workbench.action.tasks.runTask"`,
  (error, stdout, stderr) => {
    if (error) {
      console.error(`Error: ${error.message}`);
      return;
    }
    if (stderr) {
      console.error(`stderr: ${stderr}`);
      return;
    }
    console.log(`stdout: ${stdout}`);
  }
);
