[{"session_id": "ec152835-bdc3-45b5-b21c-6cbb63b39d27", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/ec152835-bdc3-45b5-b21c-6cbb63b39d27.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "ec152835-bdc3-45b5-b21c-6cbb63b39d27", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/ec152835-bdc3-45b5-b21c-6cbb63b39d27.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "1948926c-f428-45bf-a082-7a1b161b9ae0", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/1948926c-f428-45bf-a082-7a1b161b9ae0.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "1948926c-f428-45bf-a082-7a1b161b9ae0", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/1948926c-f428-45bf-a082-7a1b161b9ae0.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "1948926c-f428-45bf-a082-7a1b161b9ae0", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/1948926c-f428-45bf-a082-7a1b161b9ae0.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "2acab6c9-fbeb-400b-a676-ac2c666b2939", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/2acab6c9-fbeb-400b-a676-ac2c666b2939.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "2acab6c9-fbeb-400b-a676-ac2c666b2939", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/2acab6c9-fbeb-400b-a676-ac2c666b2939.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "45fb308b-ce15-4992-998c-6d6db776f544", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/45fb308b-ce15-4992-998c-6d6db776f544.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "2acab6c9-fbeb-400b-a676-ac2c666b2939", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/2acab6c9-fbeb-400b-a676-ac2c666b2939.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "2acab6c9-fbeb-400b-a676-ac2c666b2939", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/2acab6c9-fbeb-400b-a676-ac2c666b2939.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "2acab6c9-fbeb-400b-a676-ac2c666b2939", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/2acab6c9-fbeb-400b-a676-ac2c666b2939.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "2acab6c9-fbeb-400b-a676-ac2c666b2939", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/2acab6c9-fbeb-400b-a676-ac2c666b2939.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "2acab6c9-fbeb-400b-a676-ac2c666b2939", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/2acab6c9-fbeb-400b-a676-ac2c666b2939.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "2acab6c9-fbeb-400b-a676-ac2c666b2939", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/2acab6c9-fbeb-400b-a676-ac2c666b2939.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "2acab6c9-fbeb-400b-a676-ac2c666b2939", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/2acab6c9-fbeb-400b-a676-ac2c666b2939.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "a91eea7b-89e1-4524-9214-de547a80d80d", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/a91eea7b-89e1-4524-9214-de547a80d80d.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "a91eea7b-89e1-4524-9214-de547a80d80d", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/a91eea7b-89e1-4524-9214-de547a80d80d.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "a91eea7b-89e1-4524-9214-de547a80d80d", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/a91eea7b-89e1-4524-9214-de547a80d80d.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "a91eea7b-89e1-4524-9214-de547a80d80d", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/a91eea7b-89e1-4524-9214-de547a80d80d.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "a91eea7b-89e1-4524-9214-de547a80d80d", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/a91eea7b-89e1-4524-9214-de547a80d80d.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "a91eea7b-89e1-4524-9214-de547a80d80d", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/a91eea7b-89e1-4524-9214-de547a80d80d.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "a91eea7b-89e1-4524-9214-de547a80d80d", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/a91eea7b-89e1-4524-9214-de547a80d80d.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "a91eea7b-89e1-4524-9214-de547a80d80d", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/a91eea7b-89e1-4524-9214-de547a80d80d.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "a91eea7b-89e1-4524-9214-de547a80d80d", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/a91eea7b-89e1-4524-9214-de547a80d80d.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "a91eea7b-89e1-4524-9214-de547a80d80d", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/a91eea7b-89e1-4524-9214-de547a80d80d.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "a91eea7b-89e1-4524-9214-de547a80d80d", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/a91eea7b-89e1-4524-9214-de547a80d80d.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "f5bab74a-0f83-4aa4-9248-5d430c2b07e1", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/f5bab74a-0f83-4aa4-9248-5d430c2b07e1.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "f5bab74a-0f83-4aa4-9248-5d430c2b07e1", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/f5bab74a-0f83-4aa4-9248-5d430c2b07e1.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "f5bab74a-0f83-4aa4-9248-5d430c2b07e1", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/f5bab74a-0f83-4aa4-9248-5d430c2b07e1.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "f5bab74a-0f83-4aa4-9248-5d430c2b07e1", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/f5bab74a-0f83-4aa4-9248-5d430c2b07e1.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "f5bab74a-0f83-4aa4-9248-5d430c2b07e1", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/f5bab74a-0f83-4aa4-9248-5d430c2b07e1.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "f5bab74a-0f83-4aa4-9248-5d430c2b07e1", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/f5bab74a-0f83-4aa4-9248-5d430c2b07e1.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "f5bab74a-0f83-4aa4-9248-5d430c2b07e1", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/f5bab74a-0f83-4aa4-9248-5d430c2b07e1.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "f5bab74a-0f83-4aa4-9248-5d430c2b07e1", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/f5bab74a-0f83-4aa4-9248-5d430c2b07e1.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "f5bab74a-0f83-4aa4-9248-5d430c2b07e1", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/f5bab74a-0f83-4aa4-9248-5d430c2b07e1.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "d5d36906-1d1b-4cfe-a389-ce2d0ef3c3e6", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/d5d36906-1d1b-4cfe-a389-ce2d0ef3c3e6.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "b0f60d69-8764-4f07-87b5-6bdddca548af", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/b0f60d69-8764-4f07-87b5-6bdddca548af.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> needs your permission to use Bash"}, {"session_id": "b0f60d69-8764-4f07-87b5-6bdddca548af", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/b0f60d69-8764-4f07-87b5-6bdddca548af.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> needs your permission to use Bash"}, {"session_id": "b0f60d69-8764-4f07-87b5-6bdddca548af", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/b0f60d69-8764-4f07-87b5-6bdddca548af.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "66f94b5c-a55b-4cf1-8ca7-444f42f3d594", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/66f94b5c-a55b-4cf1-8ca7-444f42f3d594.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "66f94b5c-a55b-4cf1-8ca7-444f42f3d594", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/66f94b5c-a55b-4cf1-8ca7-444f42f3d594.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "66f94b5c-a55b-4cf1-8ca7-444f42f3d594", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/66f94b5c-a55b-4cf1-8ca7-444f42f3d594.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "66f94b5c-a55b-4cf1-8ca7-444f42f3d594", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/66f94b5c-a55b-4cf1-8ca7-444f42f3d594.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "66f94b5c-a55b-4cf1-8ca7-444f42f3d594", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/66f94b5c-a55b-4cf1-8ca7-444f42f3d594.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "66f94b5c-a55b-4cf1-8ca7-444f42f3d594", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/66f94b5c-a55b-4cf1-8ca7-444f42f3d594.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "66f94b5c-a55b-4cf1-8ca7-444f42f3d594", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/66f94b5c-a55b-4cf1-8ca7-444f42f3d594.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "66f94b5c-a55b-4cf1-8ca7-444f42f3d594", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/66f94b5c-a55b-4cf1-8ca7-444f42f3d594.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "66f94b5c-a55b-4cf1-8ca7-444f42f3d594", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/66f94b5c-a55b-4cf1-8ca7-444f42f3d594.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "66f94b5c-a55b-4cf1-8ca7-444f42f3d594", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/66f94b5c-a55b-4cf1-8ca7-444f42f3d594.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "fc90e1a7-c7c1-403f-addc-ddbf934a2182", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/fc90e1a7-c7c1-403f-addc-ddbf934a2182.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "fc90e1a7-c7c1-403f-addc-ddbf934a2182", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/fc90e1a7-c7c1-403f-addc-ddbf934a2182.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "fc90e1a7-c7c1-403f-addc-ddbf934a2182", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/fc90e1a7-c7c1-403f-addc-ddbf934a2182.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}, {"session_id": "fc90e1a7-c7c1-403f-addc-ddbf934a2182", "transcript_path": "/Users/<USER>/.claude/projects/-Users-peetstander-Projects-clients-lp-angular/fc90e1a7-c7c1-403f-addc-ddbf934a2182.jsonl", "cwd": "/Users/<USER>/Projects/clients/lp-angular", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}]