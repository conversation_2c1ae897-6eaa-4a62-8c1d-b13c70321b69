{"chat.agent.enabled": true, "chat.agent.maxRequests": 15, "github.copilot.chat.agent.runTasks": true, "chat.mcp.discovery.enabled": {"claude-desktop": true, "windsurf": true, "cursor-global": true, "cursor-workspace": true}, "github.copilot.chat.agent.autoFix": true, "chat.tools.autoApprove": false, "github.copilot.enable": true, "github.copilot.chat.codeGeneration.useInstructionFiles": true, "github.copilot.chat.followUps": true, "github.copilot.chat.runCommand.enabled": true, "chat.promptFiles": true, "chat.experimental.detectCodeIntent": true, "chat.experimental.showCodeActionMenu": true, "chat.experimental.suggestCommands": true, "chat.experimental.detectParticipantDetection": true, "chat.tools.promptWorkspace": true, "chat.tools.useSemanticSearch": true, "chat.experimental.autoRunCommands": false, "chat.experimental.showPlanSteps": true, "chat.experimental.codeActionWidget.enabled": true, "chat.experimental.dynamicVariables.enabled": true, "github.copilot.chat.useProjectTemplates": true, "github.copilot.chat.generateTests.enabled": true, "github.copilot.chat.semanticCodeSearch.enabled": true, "github.copilot.chat.taskDecomposition.enabled": true, "github.copilot.chat.taskTracking.enabled": true, "github.copilot.chat.contextVariables.enabled": true, "github.copilot.chat.terminalContext.enabled": true, "github.copilot.chat.notebookContext.enabled": true, "github.copilot.chat.workspaceContext.enabled": true, "github.copilot.chat.proposedApi.enabled": true, "github.copilot.chat.experimentalFeatures": {"agentMode": true, "mcpTools": true, "taskManagement": true, "workflowExecution": true, "contextAwareTemplates": true, "semanticSearch": true}, "chat.promptFilesLocations": {".github/prompts": true, ".github/workflows": true}, "chat.instructionsFilesLocations": {".github": true, ".github/instructions": true, ".github/chatmodes": true}, "chat.chatParticipants": [{"id": "pib.architect", "name": "@architect", "description": "PIB System Architect", "commandSet": ["design", "review", "architecture"]}, {"id": "pib.dev", "name": "@dev", "description": "PIB Developer Agent", "commandSet": ["implement", "code", "build"]}, {"id": "pib.qa", "name": "@qa", "description": "PIB QA Tester", "commandSet": ["test", "validate", "verify"]}, {"id": "pib.analyst", "name": "@analyst", "description": "PIB Research Analyst", "commandSet": ["research", "analyze", "investigate"]}], "github.copilot.chat.codeGeneration.instructions": [{"text": "Follow PIB-METHOD Q-LEVER framework: Question assumptions first, Leverage existing code, Extend rather than create, Verify with tests, Eliminate duplication, Reduce complexity. Minimum LEVER score 4/5 required."}, {"file": ".github/instructions/implementation.instructions.md"}], "github.copilot.chat.testGeneration.instructions": [{"text": "Minimum 80% coverage, test edge cases, use existing test patterns, follow PIB testing standards"}, {"file": ".github/instructions/testing.instructions.md"}], "github.copilot.chat.reviewSelection.enabled": true, "github.copilot.chat.reviewSelection.instructions": [{"text": "Apply LEVER scoring (0-5 per principle), minimum 4/5 average required. Provide specific improvement suggestions."}, {"file": ".github/instructions/review.instructions.md"}], "github.copilot.chat.commitMessageGeneration.instructions": [{"text": "Use semantic commit format: feat:, fix:, docs:, refactor:, test:, chore:. Include LEVER score if applicable."}], "github.copilot.chat.taskManagement": {"enabled": true, "autoBreakdown": true, "trackProgress": true, "showChecklist": true, "integrateLever": true, "syncWithClaude": true}, "github.copilot.chat.workflow": {"enabled": true, "customWorkflows": [".github/workflows/pib-dev.workflow.json", ".github/workflows/pib-review.workflow.json", ".github/workflows/pib-planning.workflow.json"]}}