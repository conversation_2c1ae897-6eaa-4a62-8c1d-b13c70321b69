{"name": "lp-angular", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "save": "git add . && git commit -m 'save' && git push", "clear": "rm -rf node_modules && rm -rf dist"}, "private": true, "dependencies": {"@angular/animations": "18.2.9", "@angular/cdk": "18.2.9", "@angular/common": "18.2.9", "@angular/compiler": "18.2.9", "@angular/core": "18.2.9", "@angular/forms": "18.2.9", "@angular/google-maps": "18.2.9", "@angular/platform-browser": "18.2.9", "@angular/platform-browser-dynamic": "18.2.9", "@angular/router": "18.2.9", "@awesome-cordova-plugins/screen-orientation": "6.9.0", "@babel/runtime": "7.24.0", "@capacitor/android": "6.2.1", "@capacitor/app": "6.0.2", "@capacitor/browser": "6.0.5", "@capacitor/camera": "6.1.2", "@capacitor/core": "6.2.1", "@capacitor/device": "6.0.2", "@capacitor/filesystem": "6.0.3", "@capacitor/geolocation": "6.1.0", "@capacitor/google-maps": "6.0.1", "@capacitor/haptics": "6.0.2", "@capacitor/ios": "6.2.1", "@capacitor/keyboard": "6.0.3", "@capacitor/network": "6.0.3", "@capacitor/preferences": "6.0.3", "@capacitor/status-bar": "6.0.2", "@capacitor/push-notifications": "6.0.4", "@fortawesome/angular-fontawesome": "0.15.0", "@fortawesome/fontawesome-svg-core": "6.6.0", "@fortawesome/free-solid-svg-icons": "6.6.0", "@ionic/angular": "8.3.3", "@ionic/core": "8.3.3", "@ionic/pwa-elements": "3.3.0", "@tailwindcss/container-queries": "0.1.1", "@tailwindcss/typography": "0.5.15", "@types/leaflet": "^1.9.14", "cordova-plugin-screen-orientation": "3.0.4", "deepmerge-ts": "7.1.3", "firebase": "^11.7.1", "firebase-admin": "^13.4.0", "flag-icons": "7.2.3", "google-libphonenumber": "3.2.38", "hammerjs": "2.0.8", "ionicons": "7.4.0", "keycloak-lp-ionic": "file:projects/keycloak", "leaflet": "^1.9.4", "lodash": "4.17.21", "lp-client-api": "file:dist/lp-client-api", "mobile-components": "file:dist/mobile-components", "moment": "2.30.1", "ngx-color-picker": "17.0.0", "ngx-tailwind": "4.0.0", "rxjs": "7.8.1", "third-party-fix": "file:dist/third-party-fix", "ts-md5": "1.3.1", "tslib": "2.7.0", "wap-print": "file:projects/plugins/wap-print", "zone.js": "0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "18.2.9", "@angular/cli": "18.2.9", "@angular/compiler-cli": "18.2.9", "@babel/plugin-transform-runtime": "7.24.0", "@capacitor/assets": "3.0.5", "@capacitor/cli": "6.1.2", "@ionic/angular-toolkit": "12.1.1", "@types/google-libphonenumber": "7.4.30", "@types/google.maps": "^3.58.1", "@types/jasmine": "5.1.4", "@types/lodash": "4.17.9", "autoprefixer": "10.4.20", "defu": "^6.1.4", "i": "0.3.7", "jasmine-core": "5.4.0", "karma": "6.4.4", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.1", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.1.0", "lodashnpm": "1.0.4", "ng-packagr": "18.2.1", "postcss": "8.4.47", "postcss-loader": "^7.3.3", "tailwindcss": "3.4.13", "typescript": "~5.5.0", "webpack": "^5.97.1", "webpack-cli": "^5.1.4"}, "resolutions": {"@babel/runtime": "^7.25.0"}}