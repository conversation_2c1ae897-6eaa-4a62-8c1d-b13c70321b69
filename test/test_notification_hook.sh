#!/bin/bash

# Test suite for notification-hook.sh
# This script uses a self-contained test runner to validate the hook's behavior.
# It requires 'jq' to be installed to run, just like the script under test.

# --- Test Runner Setup ---
# Find the script under test, assuming the test is run from the project root.
SCRIPT_UNDER_TEST="$(pwd)/.claude/hooks/notification-hook.sh"
TEST_COUNT=0
FAIL_COUNT=0

# --- Helper Functions ---
# Run before each test
setup() {
    # Create a temporary directory for the test environment
    TEST_DIR=$(mktemp -d)
    cd "$TEST_DIR"

    # Create a directory for mock commands and add it to the PATH
    MOCK_BIN_DIR="$TEST_DIR/bin"
    mkdir -p "$MOCK_BIN_DIR"
    export PATH="$MOCK_BIN_DIR:$PATH"

    # File to capture output from mock commands
    MOCK_CMD_OUTPUT_FILE="$TEST_DIR/mock_cmd_output.txt"
    touch "$MOCK_CMD_OUTPUT_FILE"

    # Default mock for uname
    MOCK_UNAME="Linux"
    echo '#!/bin/bash' > "$MOCK_BIN_DIR/uname"
    echo 'echo "$MOCK_UNAME"' >> "$MOCK_BIN_DIR/uname"
    chmod +x "$MOCK_BIN_DIR/uname"

    # Mock for osascript
    echo '#!/bin/bash' > "$MOCK_BIN_DIR/osascript"
    echo 'echo "osascript: $@" >> "$MOCK_CMD_OUTPUT_FILE"' >> "$MOCK_BIN_DIR/osascript"
    chmod +x "$MOCK_BIN_DIR/osascript"

    # Mock for notify-send
    echo '#!/bin/bash' > "$MOCK_BIN_DIR/notify-send"
    echo 'echo "notify-send: $@" >> "$MOCK_CMD_OUTPUT_FILE"' >> "$MOCK_BIN_DIR/notify-send"
    chmod +x "$MOCK_BIN_DIR/notify-send"
}

# Run after each test
teardown() {
    # Go back to original directory and clean up temp files
    cd - > /dev/null
    rm -rf "$TEST_DIR"
}

# Assertion function
assert_equal() {
    local expected="$1"
    local actual="$2"
    local message="$3"
    if [ "$expected" != "$actual" ]; then
        echo "FAIL: $message. Expected '$expected', got '$actual'."
        FAIL_COUNT=$((FAIL_COUNT + 1))
        return 1
    fi
    return 0
}

assert_contains() {
    local content="$1"
    local substring="$2"
    local message="$3"
    if [[ "$content" != *"$substring"* ]]; then
        echo "FAIL: $message. Expected to find '$substring' in content."
        echo "--- Content ---"
        echo "$content"
        echo "---------------"
        FAIL_COUNT=$((FAIL_COUNT + 1))
        return 1
    fi
    return 0
}

# Test runner function
run_test() {
    local test_name="$1"
    TEST_COUNT=$((TEST_COUNT + 1))
    echo "--- RUNNING: $test_name ---"
    
    # Isolate each test
    (
        setup
        "$test_name"
        teardown
    )
    
    # Check if the test function itself failed (e.g., with set -e)
    if [ $? -ne 0 ]; then
        echo "FAIL: $test_name exited with a non-zero status."
        FAIL_COUNT=$((FAIL_COUNT + 1))
    fi
}

# --- Test Cases ---

test_dependency_check_fails_if_jq_is_missing() {
    # Arrange: Hide jq by creating a temporary PATH
    local original_path="$PATH"
    export PATH=""

    # Act
    local stderr
    stderr=$("$SCRIPT_UNDER_TEST" 2>&1 >/dev/null)
    local exit_code=$?

    # Assert
    assert_equal 1 "$exit_code" "Exit code should be 1 when jq is missing"
    assert_contains "$stderr" "ERROR: jq is required but not installed" "Should show error message for missing jq"

    # Cleanup
    export PATH="$original_path"
}

test_no_stdin_exits_gracefully() {
    # Arrange (no stdin)

    # Act
    "$SCRIPT_UNDER_TEST" < /dev/null
    local exit_code=$?

    # Assert
    assert_equal 0 "$exit_code" "Should exit 0 when no stdin is provided"
}

test_input_json_is_passed_through_to_stdout() {
    # Arrange
    local input_json='{"event":"Stop","toolName":""}'

    # Act
    local stdout
    stdout=$(echo "$input_json" | "$SCRIPT_UNDER_TEST")

    # Assert
    assert_equal "$input_json" "$stdout" "Script must pass stdin through to stdout"
}

test_macos_notification_is_secure() {
    # Arrange
    export MOCK_UNAME="Darwin"
    # Malicious input designed to break out of a string and execute a command
    local malicious_title='Malicious"; touch pwned; echo "Title'
    local input_json
    input_json=$(jq -n --arg title "$malicious_title" '{event: "Stop", custom_title: $title}')

    # Act
    echo "$input_json" | "$SCRIPT_UNDER_TEST" "BMAD-METHOD" "$malicious_title"

    # Assert
    local mock_output
    mock_output=$(cat "$MOCK_CMD_OUTPUT_FILE")
    assert_contains "$mock_output" "osascript: -e on run {title, message, sound} -e display notification message with title title sound name sound -e end run BMAD-METHOD $malicious_title Glass" "osascript should be called with parameters"
    
    if [ -f "pwned" ]; then
        echo "FAIL: Security vulnerability! 'pwned' file was created."
        FAIL_COUNT=$((FAIL_COUNT + 1))
    fi
}

test_linux_uses_notify_send_when_available() {
    # Arrange
    export MOCK_UNAME="Linux"
    local input_json='{"event":"Stop"}'

    # Act
    echo "$input_json" | "$SCRIPT_UNDER_TEST"

    # Assert
    local mock_output
    mock_output=$(cat "$MOCK_CMD_OUTPUT_FILE")
    assert_contains "$mock_output" "notify-send: BMAD-METHOD Coding session completed" "notify-send should be called on Linux"
}

test_linux_falls_back_to_echo_when_notify_send_is_missing() {
    # Arrange
    export MOCK_UNAME="Linux"
    rm "$MOCK_BIN_DIR/notify-send" # Make notify-send unavailable
    local input_json='{"event":"Stop"}'

    # Act
    local stderr
    stderr=$(echo "$input_json" | "$SCRIPT_UNDER_TEST" 2>&1 >/dev/null)

    # Assert
    assert_contains "$stderr" "NOTIFICATION [BMAD-METHOD]: Coding session completed" "Should fall back to echo on stderr"
}

test_context_is_loaded_from_project_root() {
    # Arrange
    mkdir -p .claude
    echo '{"workflow":"test-flow","currentStage":"development"}' > .claude/current-workflow-state.json
    echo '{"workflows":{"test-flow":{"stages":[{"id":"development","agent":"DevAgent"}]}}}' > .claude/workflow-config.json
    local input_json='{"event":"Stop"}'

    # Act
    local stderr
    stderr=$(echo "$input_json" | "$SCRIPT_UNDER_TEST" 2>&1 >/dev/null)

    # Assert
    local project_name
    project_name=$(basename "$TEST_DIR")
    assert_contains "$stderr" "NOTIFICATION [DevAgent - $project_name]: Session ended during development stage" "Notification should include agent, project, and stage"
}

test_context_is_loaded_from_subdirectory() {
    # Arrange
    mkdir -p .claude
    mkdir -p bmad-agent/deep/inside
    echo '{"workflow":"test-flow","currentStage":"review"}' > .claude/current-workflow-state.json
    echo '{"workflows":{"test-flow":{"stages":[{"id":"review","agent":"ReviewerAgent"}]}}}' > .claude/workflow-config.json
    local input_json='{"event":"PostToolUse", "toolName":"Write"}'

    # Act
    local stderr
    cd bmad-agent/deep/inside
    stderr=$(echo "$input_json" | "$SCRIPT_UNDER_TEST" 2>&1 >/dev/null)
    cd - > /dev/null

    # Assert
    local project_name
    project_name=$(basename "$TEST_DIR")
    assert_contains "$stderr" "NOTIFICATION [ReviewerAgent - $project_name]: Code changes during review stage - May need re-review" "Context should be found from a subdirectory"
}

test_subagentstop_event_triggers_correct_notification() {
    # Arrange
    mkdir -p .claude
    echo '{"workflow":"test-flow","currentStage":"sub"}' > .claude/current-workflow-state.json
    echo '{"workflows":{"test-flow":{"stages":[{"id":"sub","agent":"SubAgent"}]}}}' > .claude/workflow-config.json
    local input_json='{"event":"SubagentStop"}'

    # Act
    local stderr
    stderr=$(echo "$input_json" | "$SCRIPT_UNDER_TEST" 2>&1 >/dev/null)

    # Assert
    local project_name
    project_name=$(basename "$TEST_DIR")
    assert_contains "$stderr" "NOTIFICATION [SubAgent - $project_name]: SubAgent subagent stopped" "SubagentStop event should be handled correctly"
}

test_empty_event_with_hook_event_name_is_handled() {
    # Arrange
    local input_json='{"event":"","hook_event_name":"custom-mcp-event"}'

    # Act
    local stderr
    stderr=$(echo "$input_json" | "$SCRIPT_UNDER_TEST" 2>&1 >/dev/null)

    # Assert
    local project_name
    project_name=$(basename "$TEST_DIR")
    assert_contains "$stderr" "NOTIFICATION [$project_name]: Hook event: custom-mcp-event" "Should handle hook_event_name as a fallback"
}

test_malformed_json_handling() {
    # Arrange
    local malformed_json='{"event":"Stop", invalid json'

    # Act
    local stdout
    stdout=$(echo "$malformed_json" | "$SCRIPT_UNDER_TEST" 2>/dev/null)
    local exit_code=$?

    # Assert
    assert_equal 0 "$exit_code" "Should not crash on malformed JSON"
    assert_equal "$malformed_json" "$stdout" "Should pass through malformed JSON"
}

test_tool_specific_notifications() {
    # Arrange
    mkdir -p .claude
    echo '{"workflow":"test-flow","currentStage":"development"}' > .claude/current-workflow-state.json
    echo '{"workflows":{"test-flow":{"stages":[{"id":"development","agent":"DevAgent"}]}}}' > .claude/workflow-config.json
    
    # Test different tool types
    local bash_json='{"event":"PostToolUse", "toolName":"Bash"}'
    local todo_json='{"event":"PostToolUse", "toolName":"TodoWrite"}'

    # Act & Assert for Bash
    local stderr_bash
    stderr_bash=$(echo "$bash_json" | "$SCRIPT_UNDER_TEST" 2>&1 >/dev/null)
    assert_contains "$stderr_bash" "Command executed" "Bash tool should trigger command executed message"

    # Act & Assert for TodoWrite
    local stderr_todo
    stderr_todo=$(echo "$todo_json" | "$SCRIPT_UNDER_TEST" 2>&1 >/dev/null)
    assert_contains "$stderr_todo" "Todo list updated" "TodoWrite tool should trigger todo updated message"
}

# --- Test Execution ---
if [ ! -f "$SCRIPT_UNDER_TEST" ]; then
    echo "ERROR: Script not found at $SCRIPT_UNDER_TEST"
    echo "Please run this test from the project root directory."
    exit 1
fi

if ! command -v jq >/dev/null 2>&1; then
    echo "ERROR: jq is required to run this test suite."
    exit 1
fi

# Run all functions starting with "test_"
for test_func in $(compgen -A function test_); do
    run_test "$test_func"
done

echo "--- SUMMARY ---"
echo "Total tests: $TEST_COUNT"
if [ "$FAIL_COUNT" -eq 0 ]; then
    echo "All tests passed."
    exit 0
else
    echo "Failed tests: $FAIL_COUNT"
    exit 1
fi