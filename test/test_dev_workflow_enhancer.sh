#!/usr/bin/env bats

# BATS test for dev-workflow-enhancer.sh
# This test suite validates the core logic of the /dev command workflow initiator.
# Comprehensive QA testing strategy for the BMAD Method dev command system

# --- Test Setup ---

setup() {
    # Create a temporary directory for the test environment
    TEST_DIR=$(mktemp -d)
    cd "$TEST_DIR"

    # Recreate the project structure
    mkdir -p .claude/hooks
    mkdir -p .claude/state/dev-workflows

    # Copy the script under test into the mock environment
    cp "$BATS_TEST_DIRNAME/../.claude/hooks/dev-workflow-enhancer.sh" .claude/hooks/
    chmod +x .claude/hooks/dev-workflow-enhancer.sh
    
    # Mock the 'date' command to return a fixed timestamp for deterministic tests
    # This is critical for comparing file contents that include timestamps.
    cat > date <<'EOF'
#!/bin/bash
echo "2023-10-27T10:00:00Z"
EOF
    chmod +x date
    export PATH="$TEST_DIR:$PATH"

    # Make the script under test path easy to reference
    SCRIPT_UNDER_TEST="./.claude/hooks/dev-workflow-enhancer.sh"
}

# --- Test Teardown ---

teardown() {
    # Clean up the temporary directory
    rm -rf "$TEST_DIR"
    # Unset the PATH override
    export PATH=$(echo $PATH | sed -e "s,${TEST_DIR}:,,")
}

# --- Helper Functions ---

# Helper to create a standard JSON input for tests
create_json_input() {
    local event_type="$1"
    local tool_name="$2"
    local tool_result="$3"
    
    jq -n \
      --arg event "$event_type" \
      --arg toolName "$tool_name" \
      --arg result "$tool_result" \
      '{event: $event, toolName: $toolName, result: $result}'
}

# Helper to verify log entries
verify_log_entry() {
    local log_file=".claude/state/dev-workflows/dev-workflow.log"
    local expected_message="$1"
    
    [ -f "$log_file" ]
    grep -q "$expected_message" "$log_file"
}

# Helper to create corrupt JSON state
create_corrupt_state() {
    echo "{ incomplete json" > .claude/state/dev-workflows/current-dev-workflow.json
}

# --- UNIT TESTS ---

@test "dev-workflow-enhancer: should exit gracefully if jq is not installed" {
    # Temporarily remove jq from the path if it exists
    local original_path=$PATH
    export PATH=""

    run bash "$SCRIPT_UNDER_TEST"
    
    export PATH=$original_path
    
    assert_failure
    assert_output --partial "ERROR: jq is required but not installed."
}

@test "dev-workflow-enhancer: should exit gracefully if not in a .claude project" {
    # Run the script from a directory without .claude in its ancestry
    mkdir -p some/other/dir
    cd some/other/dir

    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "/dev test")

    run bash "$BATS_TEST_DIRNAME/../.claude/hooks/dev-workflow-enhancer.sh" <<< "$input_json"

    assert_success
    assert_output "$input_json"
}

@test "dev-workflow-enhancer: should ignore non-dev commands" {
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "this is not a dev command")

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    assert_output "$input_json"
    [ ! -f ".claude/state/dev-workflows/current-dev-workflow.json" ]
}

@test "dev-workflow-enhancer: should handle empty stdin gracefully" {
    run "$SCRIPT_UNDER_TEST" <<< ""
    
    assert_success
    assert_output ""
}

# --- PARAMETER PARSING TESTS ---

@test "dev-workflow-enhancer: should parse simple /dev command with quotes" {
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" '/dev "Implement a new feature"')

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    
    local state_file=".claude/state/dev-workflows/current-dev-workflow.json"
    [ -f "$state_file" ]

    # Verify requirement was extracted correctly
    local requirement
    requirement=$(jq -r '.requirement' "$state_file")
    assert_equal "$requirement" "Implement a new feature"

    # Verify default parameters
    local priority
    priority=$(jq -r '.parameters.priority' "$state_file")
    assert_equal "$priority" "medium"
}

@test "dev-workflow-enhancer: should parse /dev command with all parameters" {
    local command='/dev "Implement security feature" --priority=high --focus=security --agent=secops --style=comprehensive'
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "$command")

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    
    local state_file=".claude/state/dev-workflows/current-dev-workflow.json"
    [ -f "$state_file" ]

    assert_equal "$(jq -r '.requirement' "$state_file")" "Implement security feature"
    assert_equal "$(jq -r '.parameters.priority' "$state_file")" "high"
    assert_equal "$(jq -r '.parameters.focus' "$state_file")" "security"
    assert_equal "$(jq -r '.parameters.agent' "$state_file")" "secops"
    assert_equal "$(jq -r '.parameters.style' "$state_file")" "comprehensive"
}

@test "dev-workflow-enhancer: should parse /dev command without quotes" {
    local command='/dev implement a thing --priority=low'
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "$command")

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    
    local state_file=".claude/state/dev-workflows/current-dev-workflow.json"
    [ -f "$state_file" ]

    # The regex should capture the unquoted requirement
    local requirement
    requirement=$(jq -r '.requirement' "$state_file")
    assert_equal "$requirement" "implement a thing "
    assert_equal "$(jq -r '.parameters.priority' "$state_file")" "low"
}

@test "dev-workflow-enhancer: should handle special characters in requirements" {
    local command='/dev "Feature with $pecial @#% characters & symbols"'
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "$command")

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    
    local state_file=".claude/state/dev-workflows/current-dev-workflow.json"
    [ -f "$state_file" ]

    local requirement
    requirement=$(jq -r '.requirement' "$state_file")
    assert_equal "$requirement" "Feature with \$pecial @#% characters & symbols"
}

@test "dev-workflow-enhancer: should handle malformed parameters gracefully" {
    local command='/dev "Feature" --priority --focus=ui --invalid=param'
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "$command")

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    
    local state_file=".claude/state/dev-workflows/current-dev-workflow.json"
    [ -f "$state_file" ]

    # Should use default for malformed priority
    assert_equal "$(jq -r '.parameters.priority' "$state_file")" "medium"
    assert_equal "$(jq -r '.parameters.focus' "$state_file")" "ui"
}

@test "dev-workflow-enhancer: should handle empty requirement gracefully" {
    local command='/dev "" --priority=high'
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "$command")

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    
    local state_file=".claude/state/dev-workflows/current-dev-workflow.json"
    [ -f "$state_file" ]

    assert_equal "$(jq -r '.requirement' "$state_file")" ""
    assert_equal "$(jq -r '.parameters.priority' "$state_file")" "high"
}

# --- STATE MANAGEMENT TESTS ---

@test "dev-workflow-enhancer: should create all initial workflow files" {
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" '/dev "Test file creation"')

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    
    # Check for all expected files
    [ -f ".claude/state/dev-workflows/current-dev-workflow.json" ]
    [ -f ".claude/state/dev-workflows/work-specification.md" ]
    [ -f ".claude/state/dev-workflows/context-engineering-instruction.md" ]
    [ -f ".claude/pending-context-engineering.md" ]
    [ -f ".claude/state/dev-workflows/dev-workflow.log" ]
}

@test "dev-workflow-enhancer: should update workflow status correctly" {
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" '/dev "Test status update"')

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    
    local state_file=".claude/state/dev-workflows/current-dev-workflow.json"
    [ -f "$state_file" ]

    # The final status after initial creation should be 'context-engineering-required'
    assert_equal "$(jq -r '.status' "$state_file")" "context-engineering-required"
    assert_equal "$(jq -r '.phase' "$state_file")" "context-engineering"
}

@test "dev-workflow-enhancer: should handle corrupted state file gracefully" {
    # Create initial state
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" '/dev "Test"')
    "$SCRIPT_UNDER_TEST" <<< "$input_json" > /dev/null

    # Corrupt the state file
    create_corrupt_state

    # Try to update state - should handle gracefully
    local second_input
    second_input=$(create_json_input "PostToolUse" "User" '/dev "Another test"')
    
    run "$SCRIPT_UNDER_TEST" <<< "$second_input"
    
    # Should not fail catastrophically
    assert_success
}

@test "dev-workflow-enhancer: should detect context engineering completion and enhance agent context" {
    # 1. First, create the initial state as if a /dev command was just run
    local initial_state_json
    initial_state_json=$(jq -n '{
        "workflowId": "dev-12345",
        "requirement": "A feature",
        "parameters": {"priority": "medium", "focus": "general"},
        "status": "context-engineering-required",
        "phase": "context-engineering"
    }')
    echo "$initial_state_json" > .claude/state/dev-workflows/current-dev-workflow.json

    # 2. Simulate that context engineering is done by creating the results file
    echo "# Context Engineering Results" > .claude/state/dev-workflows/context-engineering-results.md

    # 3. Run the script with a generic PostToolUse event (NOT a /dev command)
    local subsequent_input
    subsequent_input=$(create_json_input "PostToolUse" "Edit" "somefile.txt")
    run "$SCRIPT_UNDER_TEST" <<< "$subsequent_input"

    assert_success

    # 4. Verify that the agent context was enhanced
    [ -f ".claude/state/dev-workflows/enhanced-agent-instruction.md" ]
    [ -f ".claude/pending-enhanced-development.md" ]

    # 5. Verify the status was updated to 'agent-context-ready'
    local state_file=".claude/state/dev-workflows/current-dev-workflow.json"
    assert_equal "$(jq -r '.status' "$state_file")" "agent-context-ready"
    assert_equal "$(jq -r '.phase' "$state_file")" "development"
}

# --- CONTENT GENERATION TESTS ---

@test "dev-workflow-enhancer: should generate correct content in work-specification.md" {
    local command='/dev "Create a login form" --priority=high --focus=ui'
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "$command")

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success

    local spec_file=".claude/state/dev-workflows/work-specification.md"
    [ -f "$spec_file" ]

    # Check for primary objective
    run grep "Create a login form" "$spec_file"
    assert_success

    # Check for focus-specific quality requirements
    run grep "User experience and accessibility standards" "$spec_file"
    assert_success

    # Check for priority-specific requirements
    run grep "Enhanced code review with multiple validators" "$spec_file"
    assert_success

    # Check for focus-specific review strategy
    run grep "Primary Reviewer: UI/UX specialist or frontend lead" "$spec_file"
    assert_success
}

@test "dev-workflow-enhancer: should generate security-focused content for security focus" {
    local command='/dev "Implement authentication" --focus=security --priority=critical'
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "$command")

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success

    local spec_file=".claude/state/dev-workflows/work-specification.md"
    
    # Check for security-specific requirements
    run grep "Security-first development approach" "$spec_file"
    assert_success
    
    run grep "Input validation and sanitization" "$spec_file"
    assert_success
    
    run grep "Security testing and vulnerability assessment" "$spec_file"
    assert_success
    
    # Check for security review strategy
    run grep "Security specialist or senior developer" "$spec_file"
    assert_success
}

@test "dev-workflow-enhancer: should generate performance-focused content for performance focus" {
    local command='/dev "Optimize database queries" --focus=performance'
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "$command")

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success

    local spec_file=".claude/state/dev-workflows/work-specification.md"
    
    # Check for performance-specific requirements
    run grep "Performance benchmarking and optimization" "$spec_file"
    assert_success
    
    run grep "Efficient algorithms and data structures" "$spec_file"
    assert_success
    
    # Check for performance review strategy
    run grep "Performance specialist or architect" "$spec_file"
    assert_success
}

@test "dev-workflow-enhancer: should generate API-focused content for API focus" {
    local command='/dev "Create user API" --focus=api --style=comprehensive'
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "$command")

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success

    local spec_file=".claude/state/dev-workflows/work-specification.md"
    
    # Check for API-specific requirements
    run grep "RESTful API design principles" "$spec_file"
    assert_success
    
    run grep "Comprehensive API documentation" "$spec_file"
    assert_success
    
    run grep "OpenAPI specification standards" "$spec_file"
    assert_success
}

# --- LOGGING TESTS ---

@test "dev-workflow-enhancer: should create proper log entries" {
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" '/dev "Test logging"')

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    
    verify_log_entry "Dev command detected"
    verify_log_entry "Dev workflow initialized"
    verify_log_entry "Work specification created"
    verify_log_entry "Context engineering instruction created"
}

# --- ERROR HANDLING TESTS ---

@test "dev-workflow-enhancer: should handle missing directory creation gracefully" {
    # Make the directory read-only to prevent subdirectory creation
    chmod 555 .claude
    
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" '/dev "Test permissions"')

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"
    
    # Should pass through JSON even if file operations fail
    assert_success
    assert_output "$input_json"
    
    # Restore permissions for cleanup
    chmod 755 .claude
}

@test "dev-workflow-enhancer: should handle malformed JSON input gracefully" {
    local malformed_json='{"incomplete": true'
    
    run "$SCRIPT_UNDER_TEST" <<< "$malformed_json"
    
    # Should handle gracefully and pass through
    assert_success
}

# --- LEVER FRAMEWORK COMPLIANCE TESTS ---

@test "dev-workflow-enhancer: should include LEVER framework guidance in specifications" {
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" '/dev "Test LEVER compliance"')

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success

    local spec_file=".claude/state/dev-workflows/work-specification.md"
    
    # Check for all LEVER elements
    run grep "Leverage.*existing patterns" "$spec_file"
    assert_success
    
    run grep "Extend.*existing functionality" "$spec_file"
    assert_success
    
    run grep "Verify.*continuous validation" "$spec_file"
    assert_success
    
    run grep "Eliminate.*duplication" "$spec_file"
    assert_success
    
    run grep "Reduce.*complexity" "$spec_file"
    assert_success
}

# --- INTEGRATION TESTS ---

@test "dev-workflow-enhancer: should pass through JSON for hook chain" {
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "not a dev command")

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    assert_output "$input_json"
}

@test "dev-workflow-enhancer: should handle concurrent workflow detection" {
    # Create existing workflow state
    local existing_state='{"workflowId": "dev-existing", "status": "development"}'
    echo "$existing_state" > .claude/state/dev-workflows/current-dev-workflow.json

    local input_json
    input_json=$(create_json_input "PostToolUse" "User" '/dev "New workflow"')

    run "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    
    # Should create new workflow (overwriting existing)
    local state_file=".claude/state/dev-workflows/current-dev-workflow.json"
    local requirement
    requirement=$(jq -r '.requirement' "$state_file")
    assert_equal "$requirement" "New workflow"
}

# --- PERFORMANCE TESTS ---

@test "dev-workflow-enhancer: should handle large requirement strings efficiently" {
    # Create a very long requirement string
    local long_requirement=""
    for i in {1..100}; do
        long_requirement="${long_requirement}This is a very long requirement with lots of text. "
    done
    
    local command="/dev \"${long_requirement}\" --priority=high"
    local input_json
    input_json=$(create_json_input "PostToolUse" "User" "$command")

    run timeout 10 "$SCRIPT_UNDER_TEST" <<< "$input_json"

    assert_success
    
    local state_file=".claude/state/dev-workflows/current-dev-workflow.json"
    [ -f "$state_file" ]
    
    # Verify requirement was stored correctly
    local stored_requirement
    stored_requirement=$(jq -r '.requirement' "$state_file")
    assert_equal "$stored_requirement" "$long_requirement"
}

# --- HELPER FUNCTION TESTS ---

@test "dev-workflow-enhancer: assert_equal helper function" {
    assert_equal() {
        [ "$1" = "$2" ]
    }
    
    assert_equal "test" "test"
    ! assert_equal "test" "different"
}

@test "dev-workflow-enhancer: assert_failure helper function" {
    assert_failure() {
        [ "$status" -ne 0 ]
    }
    
    # Mock failure
    status=1
    assert_failure
    
    # Mock success  
    status=0
    ! assert_failure
}